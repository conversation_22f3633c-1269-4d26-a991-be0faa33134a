<?php
/**
 * فئة التقارير
 * تتعامل مع إنشاء وإدارة التقارير المختلفة في نظام إدارة صالون الحلاقة
 */

// منع الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول المباشر لهذا الملف');
}

class Report {
    private $db;

    /**
     * إنشاء كائن من فئة التقارير
     * @param Database $db كائن قاعدة البيانات
     */
    public function __construct($db) {
        $this->db = $db;

        // التأكد من وجود مجلد السجلات
        $logDir = dirname(dirname(dirname(__FILE__))) . '/logs';
        if (!file_exists($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }

    /**
     * تسجيل رسالة في ملف السجل
     * @param string $message الرسالة المراد تسجيلها
     * @param string $type نوع الرسالة (info, error, debug)
     */
    private function log($message, $type = 'info') {
        $logFile = dirname(dirname(dirname(__FILE__))) . '/logs/inventory_report.log';
        $date = date('Y-m-d H:i:s');
        $logMessage = "[$date] [$type] $message" . PHP_EOL;
        file_put_contents($logFile, $logMessage, FILE_APPEND);
    }

    /**
     * إنشاء تقرير شامل للمبيعات باستخدام جدول نهاية اليوم
     * @param array $filters فلاتر البحث
     * @return array تقرير المبيعات
     */
    public function generateSalesReport($filters = []) {
        try {
            // إنشاء كائنات النماذج
            $invoiceModel = new Invoice($this->db);
            $serviceModel = new Service($this->db);
            $productModel = new Product($this->db);

            // تقرير شامل للمبيعات
            $report = [
                'filters' => $filters,
                'summary' => [],
                'payment_methods' => [],
                'daily_sales' => [],
                'top_services' => [],
                'top_products' => [],
                'sales_by_branch' => [],
                'sales_by_category' => []
            ];

            // بناء الاستعلام لجدول نهاية اليوم
            $sql = "SELECT
                        SUM(total_sales) as total_sales,
                        COUNT(*) as total_days,
                        SUM(cash_amount) as cash_amount,
                        SUM(card_amount) as card_amount,
                        SUM(other_amount) as other_amount,
                        SUM(total_expenses) as total_expenses
                    FROM end_days WHERE 1=1";

            $bindings = [];

            // إضافة شروط الفلترة
            if (!empty($filters['start_date'])) {
                $sql .= " AND date >= :start_date";
                $bindings[':start_date'] = $filters['start_date'];
            }

            if (!empty($filters['end_date'])) {
                $sql .= " AND date <= :end_date";
                $bindings[':end_date'] = $filters['end_date'];
            }

            if (!empty($filters['branch_id'])) {
                $sql .= " AND branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            // تنفيذ الاستعلام
            $this->db->prepare($sql);
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            $summaryData = $this->db->fetch();

            // إذا لم تكن هناك بيانات
            if (!$summaryData) {
                $summaryData = [
                    'total_sales' => 0,
                    'total_days' => 0,
                    'cash_amount' => 0,
                    'card_amount' => 0,
                    'other_amount' => 0,
                    'total_expenses' => 0
                ];
            }

            // إجمالي المبيعات والخصومات
            $report['summary']['total_sales'] = floatval($summaryData['total_sales']);
            $report['summary']['total_days'] = intval($summaryData['total_days']);
            $report['summary']['total_expenses'] = floatval($summaryData['total_expenses']);
            $report['summary']['net_profit'] = $report['summary']['total_sales'] - $report['summary']['total_expenses'];

            // الحصول على عدد الفواتير من جدول الفواتير
            $report['summary']['total_invoices'] = $invoiceModel->getInvoicesCount($filters);

            // المبيعات حسب طريقة الدفع
            $report['payment_methods'] = [
                ['method' => 'cash', 'total' => floatval($summaryData['cash_amount'])],
                ['method' => 'card', 'total' => floatval($summaryData['card_amount'])],
                ['method' => 'other', 'total' => floatval($summaryData['other_amount'])]
            ];

            // المبيعات اليومية من جدول نهاية اليوم
            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                // بناء الاستعلام للحصول على المبيعات اليومية
                $dailySql = "SELECT
                            date,
                            total_sales,
                            cash_amount,
                            card_amount,
                            other_amount,
                            total_expenses,
                            created_at as start_time,
                        FROM end_days
                        WHERE 1=1";

                $dailyBindings = [];

                // إضافة شروط الفلترة
                if (!empty($filters['start_date'])) {
                    $dailySql .= " AND date >= :start_date";
                    $dailyBindings[':start_date'] = $filters['start_date'];
                }

                if (!empty($filters['end_date'])) {
                    $dailySql .= " AND date <= :end_date";
                    $dailyBindings[':end_date'] = $filters['end_date'];
                }

                if (!empty($filters['branch_id'])) {
                    $dailySql .= " AND branch_id = :branch_id";
                    $dailyBindings[':branch_id'] = $filters['branch_id'];
                }

                $dailySql .= " ORDER BY date ASC";

                // تنفيذ الاستعلام
                $this->db->prepare($dailySql);
                foreach ($dailyBindings as $param => $value) {
                    $this->db->bind($param, $value);
                }

                $dailyRecords = $this->db->fetchAll();

                // تسجيل عدد السجلات اليومية المسترجعة
                error_log('Report: Number of daily records retrieved: ' . count($dailyRecords));

                // تجميع المبيعات حسب اليوم
                $dailySalesMap = [];

                // إذا كان هناك سجلات، قم بتجميعها حسب اليوم
                if (!empty($dailyRecords)) {
                    foreach ($dailyRecords as $record) {
                        // تسجيل بيانات السجل للتحقق
                        error_log('Report: Daily Record Date: ' . $record['date'] . ', Sales: ' . $record['total_sales']);

                        $date = $record['date']; // التاريخ من السجل

                        // الحصول على عدد الفواتير لهذا اليوم من جدول الفواتير
                        $dayFilters = $filters;
                        $dayFilters['date'] = $date;
                        $invoicesCount = $invoiceModel->getInvoicesCount($dayFilters);

                        $dateObj = new DateTime($date);

                        // Formatear las horas de inicio y fin
                        $startTime = !empty($record['start_time']) ? $record['start_time'] : null;
                        $endTime = !empty($record['end_time']) ? $record['end_time'] : null;

                        // Extraer solo la parte de la hora (HH:MM:SS) si existe
                        $formattedStartTime = $startTime ? date('H:i:s', strtotime($startTime)) : null;
                        $formattedEndTime = $endTime ? date('H:i:s', strtotime($endTime)) : null;

                        $dailySalesMap[$date] = [
                            'date' => $date,
                            'day' => $dateObj->format('d'),
                            'month' => $dateObj->format('m'),
                            'year' => $dateObj->format('Y'),
                            'total' => floatval($record['total_sales']),
                            'expenses' => floatval($record['total_expenses']),
                            'profit' => floatval($record['total_sales']) - floatval($record['total_expenses']),
                            'cash_amount' => floatval($record['cash_amount']),
                            'card_amount' => floatval($record['card_amount']),
                            'other_amount' => floatval($record['other_amount']),
                            'invoices_count' => $invoicesCount,
                            'start_time' => $formattedStartTime,
                            'end_time' => $formattedEndTime,
                            'full_start_time' => $startTime,
                            'full_end_time' => $endTime
                        ];

                        // تسجيل المجموع الجديد لليوم
                        error_log('Report: Daily data for ' . $date . ': Sales=' . $dailySalesMap[$date]['total'] .
                                 ', Expenses=' . $dailySalesMap[$date]['expenses'] .
                                 ', Start=' . $dailySalesMap[$date]['start_time'] .
                                 ', End=' . $dailySalesMap[$date]['end_time']);
                    }
                } else {
                    error_log('Report: No daily records found in the specified date range');
                }

                // تسجيل عدد الأيام التي تم تجميعها
                error_log('Report: Number of days with sales: ' . count($dailySalesMap));

                // إنشاء سجلات للأيام التي لا توجد بها فواتير
                $startDate = new DateTime($filters['start_date']);
                $endDate = new DateTime($filters['end_date']);
                $endDate->modify('+1 day'); // لتضمين تاريخ النهاية

                $currentDate = clone $startDate;
                while ($currentDate < $endDate) {
                    $dateStr = $currentDate->format('Y-m-d');

                    // إذا لم يكن هناك سجل لهذا اليوم، قم بإنشائه مع قيمة صفر
                    if (!isset($dailySalesMap[$dateStr])) {
                        $dailySalesMap[$dateStr] = [
                            'date' => $dateStr,
                            'day' => $currentDate->format('d'),
                            'month' => $currentDate->format('m'),
                            'year' => $currentDate->format('Y'),
                            'total' => 0,
                            'invoices_count' => 0
                        ];
                    }

                    $currentDate->modify('+1 day');
                }

                // تحويل المصفوفة الترابطية إلى مصفوفة عادية
                $dailyData = array_values($dailySalesMap);

                // ترتيب البيانات حسب التاريخ
                usort($dailyData, function($a, $b) {
                    return strcmp($a['date'], $b['date']);
                });

                // تسجيل البيانات النهائية
                error_log('Report: Final daily sales data: ' . json_encode($dailyData));

                $report['daily_sales'] = $dailyData;
            }

            // أفضل الخدمات
            $report['top_services'] = $serviceModel->getTopServices(
                5,
                $filters['branch_id'] ?? null,
                $filters['period'] ?? 'month'
            );

            // أفضل المنتجات
            $report['top_products'] = $this->getTopProducts($filters);

            // المبيعات حسب الفرع
            $report['sales_by_branch'] = $this->getSalesByBranch($filters);

            // المبيعات حسب فئة الخدمات
            $report['sales_by_category'] = $this->getSalesByServiceCategory($filters);

            return $report;
        } catch (Exception $e) {
            error_log('خطأ أثناء إنشاء تقرير المبيعات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على أفضل المنتجات
     * @param array $filters فلاتر البحث
     * @return array قائمة المنتجات الأكثر مبيعًا
     */
    private function getTopProducts($filters) {
        try {
            $sql = "SELECT p.id, p.name, p.price, p.cost,
                           SUM(ii.quantity) as sold_quantity,
                           SUM(ii.total) as total_sales,
                           COUNT(DISTINCT i.id) as orders_count
                    FROM products p
                    JOIN invoice_items ii ON p.id = ii.item_id AND ii.item_type = 'product'
                    JOIN invoices i ON ii.invoice_id = i.id";

            $whereConditions = [];
            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "i.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                $whereConditions[] = "i.created_at BETWEEN :start_date AND :end_date";
                $bindings[':start_date'] = $filters['start_date'] . ' 00:00:00';
                $bindings[':end_date'] = $filters['end_date'] . ' 23:59:59';
            } elseif (!empty($filters['month']) && !empty($filters['year'])) {
                $whereConditions[] = "MONTH(i.created_at) = :month AND YEAR(i.created_at) = :year";
                $bindings[':month'] = $filters['month'];
                $bindings[':year'] = $filters['year'];
            }

            // إضافة شروط WHERE
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            // تجميع حسب المنتج وترتيب النتائج
            $sql .= " GROUP BY p.id, p.name, p.price, p.cost
                      ORDER BY sold_quantity DESC
                      LIMIT 5";

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع المنتجات الأكثر مبيعًا: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على المبيعات حسب الفروع
     * @param array $filters فلاتر البحث
     * @return array المبيعات حسب الفروع
     */
    private function getSalesByBranch($filters) {
        try {
            $sql = "SELECT b.id, b.name,
                           COUNT(DISTINCT i.id) as total_invoices,
                           SUM(i.final_amount) as total_sales
                    FROM branches b
                    LEFT JOIN invoices i ON b.id = i.branch_id";

            $whereConditions = ["b.is_active = 1"];
            $bindings = [];

            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                $whereConditions[] = "i.created_at BETWEEN :start_date AND :end_date";
                $bindings[':start_date'] = $filters['start_date'] . ' 00:00:00';
                $bindings[':end_date'] = $filters['end_date'] . ' 23:59:59';
            } elseif (!empty($filters['month']) && !empty($filters['year'])) {
                $whereConditions[] = "MONTH(i.created_at) = :month AND YEAR(i.created_at) = :year";
                $bindings[':month'] = $filters['month'];
                $bindings[':year'] = $filters['year'];
            }

            // إضافة شروط WHERE
            $sql .= " WHERE " . implode(' AND ', $whereConditions);

            $sql .= " GROUP BY b.id, b.name
                      ORDER BY total_sales DESC";

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع المبيعات حسب الفروع: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على المبيعات حسب فئات الخدمات
     * @param array $filters فلاتر البحث
     * @return array المبيعات حسب فئات الخدمات
     */
    private function getSalesByServiceCategory($filters) {
        try {
            $sql = "SELECT sc.id, sc.name,
                           COUNT(DISTINCT ii.id) as total_services,
                           SUM(ii.total) as total_sales
                    FROM service_categories sc
                    JOIN services s ON sc.id = s.category_id
                    JOIN invoice_items ii ON s.id = ii.item_id AND ii.item_type = 'service'
                    JOIN invoices i ON ii.invoice_id = i.id";

            $whereConditions = [];
            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "i.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                $whereConditions[] = "i.created_at BETWEEN :start_date AND :end_date";
                $bindings[':start_date'] = $filters['start_date'] . ' 00:00:00';
                $bindings[':end_date'] = $filters['end_date'] . ' 23:59:59';
            } elseif (!empty($filters['month']) && !empty($filters['year'])) {
                $whereConditions[] = "MONTH(i.created_at) = :month AND YEAR(i.created_at) = :year";
                $bindings[':month'] = $filters['month'];
                $bindings[':year'] = $filters['year'];
            }

            // إضافة شروط WHERE
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            $sql .= " GROUP BY sc.id, sc.name
                      ORDER BY total_sales DESC";

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع المبيعات حسب فئات الخدمات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * إنشاء تقرير الموظفين
     * @param array $filters فلاتر البحث
     * @return array تقرير الموظفين
     */
    public function generateEmployeeReport($filters = []) {
        try {
            $employeeModel = new Employee($this->db);

            $report = [
                'filters' => $filters,
                'top_employees' => [],
                'employee_performance' => [],
                'salary_summary' => []
            ];

            // أفضل الموظفين
            $report['top_employees'] = $employeeModel->getTopEmployees(
                5,
                $filters['period'] ?? 'month',
                $filters['branch_id'] ?? null
            );

            // أداء الموظفين بالتفصيل
            $report['employee_performance'] = $this->getEmployeePerformanceDetails($filters);

            // ملخص الرواتب
            $report['salary_summary'] = $this->getEmployeeSalarySummary($filters);

            return $report;
        } catch (Exception $e) {
            error_log('خطأ أثناء إنشاء تقرير الموظفين: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على تفاصيل أداء الموظفين
     * @param array $filters فلاتر البحث
     * @return array تفاصيل أداء الموظفين
     */
    protected function getEmployeePerformanceDetails($filters) {
        try {
            $sql = "SELECT e.id, e.name, e.position,
                           COUNT(DISTINCT i.id) as total_invoices,
                           SUM(ii.total) as total_sales,
                           COUNT(DISTINCT s.id) as total_services,
                           AVG(ii.total) as avg_transaction_value
                    FROM employees e
                    LEFT JOIN invoice_items ii ON e.id = ii.employee_id
                    LEFT JOIN invoices i ON ii.invoice_id = i.id
                    LEFT JOIN services s ON ii.item_id = s.id AND ii.item_type = 'service'";

            $whereConditions = [];
            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "e.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                $whereConditions[] = "i.created_at BETWEEN :start_date AND :end_date";
                $bindings[':start_date'] = $filters['start_date'] . ' 00:00:00';
                $bindings[':end_date'] = $filters['end_date'] . ' 23:59:59';
            } elseif (!empty($filters['month']) && !empty($filters['year'])) {
                $whereConditions[] = "MONTH(i.created_at) = :month AND YEAR(i.created_at) = :year";
                $bindings[':month'] = $filters['month'];
                $bindings[':year'] = $filters['year'];
            }

            // إضافة شروط WHERE
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            $sql .= " GROUP BY e.id, e.name, e.position
                      ORDER BY total_sales DESC";

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع تفاصيل أداء الموظفين: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على ملخص رواتب الموظفين
     * @param array $filters فلاتر البحث
     * @return array ملخص رواتب الموظفين
     */
    private function getEmployeeSalarySummary($filters) {
        try {
            $sql = "SELECT e.id, e.name, e.position,
                           es.month, es.year,
                           SUM(es.fixed_amount) as total_fixed_salary,
                           SUM(es.commission_amount) as total_commission,
                           SUM(es.bonuses) as total_bonuses,
                           SUM(es.deductions) as total_deductions,
                           SUM(es.total_amount) as total_salary,
                           COUNT(*) as paid_months,
                           SUM(CASE WHEN es.payment_status = 'paid' THEN 1 ELSE 0 END) as fully_paid_months
                    FROM employees e
                    LEFT JOIN employee_salaries es ON e.id = es.employee_id";

            $whereConditions = [];
            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "e.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (!empty($filters['year'])) {
                $whereConditions[] = "es.year = :year";
                $bindings[':year'] = $filters['year'];
            }

            // إضافة شروط WHERE
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            $sql .= " GROUP BY e.id, e.name, e.position, es.month, es.year
                      ORDER BY total_salary DESC";

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع ملخص رواتب الموظفين: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * إنشاء تقرير المخزون
     * @param array $filters فلاتر البحث
     * @return array تقرير المخزون
     */
    public function generateInventoryReport($filters = []) {
        try {
            $inventoryModel = new Inventory($this->db);
            $productModel = new Product($this->db);

            $report = [
                'filters' => $filters,
                'summary' => [],
                'categories' => [],
                'low_stock_products' => [],
                'out_of_stock_products' => [],
                'stagnant_products' => [],
                'stock_value' => null,
                'sold_products' => [],
                'consumed_products' => [],
                'top_selling_products' => [],
                'top_consumed_products' => []
            ];

            // قيمة المخزون
            $stockValue = $inventoryModel->calculateInventoryValue(
                $filters['branch_id'] ?? null
            );

            $report['stock_value'] = [
                'total_cost' => $stockValue['total_cost'],
                'total_value' => $stockValue['total_value'],
                'expected_profit' => $stockValue['expected_profit']
            ];

            // إجمالي عدد المنتجات
            $report['summary']['total_products'] = $productModel->getProductsCount([
                'branch_id' => $filters['branch_id'] ?? null
            ]);

            // ملخص فئات المنتجات
            $report['categories'] = $productModel->getProductCategories();

            // المنتجات منخفضة المخزون
            $report['low_stock_products'] = $inventoryModel->getLowStockProducts(
                $filters['branch_id'] ?? null
            );

            // المنتجات المنتهية من المخزون
            $report['out_of_stock_products'] = $inventoryModel->getOutOfStockProducts(
                $filters['branch_id'] ?? null
            );

            // المنتجات الراكدة
            $report['stagnant_products'] = $inventoryModel->getStagnantProducts(
                $filters['days'] ?? 30,
                $filters['branch_id'] ?? null
            );

            // التنبؤ بالمخزون
            $report['stock_forecast'] = $this->getStockForecast($filters);

            // المنتجات المباعة
            $soldProducts = $this->getSoldProducts($filters);
            $report['sold_products'] = [
                'total_quantity' => $soldProducts['total_quantity'] ?? 0,
                'total_value' => $soldProducts['total_value'] ?? 0,
                'total_cost' => $soldProducts['total_cost'] ?? 0,
                'total_profit' => $soldProducts['total_profit'] ?? 0
            ];

            // المنتجات المستهلكة (المستخدمة داخليًا)
            $consumedProducts = $this->getConsumedProducts($filters);
            $report['consumed_products'] = [
                'total_quantity' => $consumedProducts['total_quantity'] ?? 0,
                'total_value' => $consumedProducts['total_value'] ?? 0
            ];

            // المنتجات الأكثر مبيعًا
            $report['top_selling_products'] = $this->getTopSellingProducts($filters);

            // المنتجات الأكثر استهلاكًا
            $report['top_consumed_products'] = $this->getTopConsumedProducts($filters);

            return $report;
        } catch (Exception $e) {
            error_log('خطأ أثناء إنشاء تقرير المخزون: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * التنبؤ بحالة المخزون
     * @param array $filters فلاتر البحث
     * @return array توقعات المخزون
     */
    private function getStockForecast($filters) {
        try {
            $inventoryModel = new Inventory($this->db);

            // استرجاع المنتجات
            $this->db->prepare("SELECT id, name FROM products
                                WHERE is_for_sale = 1 AND is_active = 1");
            $products = $this->db->fetchAll();

            $forecast = [];

            foreach ($products as $product) {
                try {
                    $stockPrediction = $inventoryModel->predictStockOutDate(
                        $product['id'],
                        $filters['branch_id'] ?? $_SESSION['user_branch_id'],
                        $filters['period_days'] ?? 30
                    );

                    $forecast[] = $stockPrediction;
                } catch (Exception $e) {
                    // تخطي المنتجات التي لا يمكن التنبؤ بها
                    continue;
                }
            }

            // ترتيب التوقعات حسب أقرب تاريخ للنفاد
            usort($forecast, function($a, $b) {
                if ($a['days_until_stock_out'] === null) return 1;
                if ($b['days_until_stock_out'] === null) return -1;
                return $a['days_until_stock_out'] - $b['days_until_stock_out'];
            });

            return $forecast;
        } catch (Exception $e) {
            error_log('خطأ أثناء التنبؤ بحالة المخزون: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * إنشاء تقرير المصروفات
     * @param array $filters فلاتر البحث
     * @return array تقرير المصروفات
     */
    public function generateExpensesReport($filters = []) {
        try {
            $expenseModel = new Expense($this->db);

            $report = [
                'filters' => $filters,
                'summary' => [],
                'categories' => [],
                'daily_expenses' => [],
                'payment_methods' => [],
                'top_expense_categories' => []
            ];

            // إجمالي المصروفات
            $report['summary']['total_expenses'] = $expenseModel->getTotalExpenses($filters);
            $report['summary']['total_expense_count'] = $expenseModel->getExpensesCount($filters);

            // المصروفات حسب الفئة
            $report['categories'] = $expenseModel->getExpensesByCategory($filters);

            // أعلى فئات المصروفات تكلفة
            $report['top_expense_categories'] = $expenseModel->getTopExpenseCategories(
                $filters,
                5
            );

            // المصروفات اليومية
            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                $startDate = new DateTime($filters['start_date']);
                $endDate = new DateTime($filters['end_date']);
                $endDate->modify('+1 day'); // لتضمين تاريخ النهاية

                $dailyData = [];
                $currentDate = clone $startDate;

                while ($currentDate < $endDate) {
                    $dateStr = $currentDate->format('Y-m-d');
                    $dailyFilters = $filters;
                    $dailyFilters['date'] = $dateStr;

                    $dailyTotal = $expenseModel->getTotalExpenses($dailyFilters);

                    $dailyData[] = [
                        'date' => $dateStr,
                        'day' => $currentDate->format('d'),
                        'month' => $currentDate->format('m'),
                        'year' => $currentDate->format('Y'),
                        'total' => $dailyTotal
                    ];

                    $currentDate->modify('+1 day');
                }

                $report['daily_expenses'] = $dailyData;
            }

            // طرق الدفع
            $report['payment_methods'] = $expenseModel->getExpensesByPaymentMethod($filters);

            // المصروفات حسب طريقة الدفع
            $paymentMethods = ['cash', 'card', 'other'];
            foreach ($paymentMethods as $method) {
                $methodFilters = $filters;
                $methodFilters['payment_method'] = $method;
                $total = $expenseModel->getTotalExpenses($methodFilters);

                $report['payment_methods'][] = [
                    'method' => $method,
                    'total' => $total
                ];
            }

            // مقارنة المصروفات بين فترتين
            if (!empty($filters['compare_periods'])) {
                $comparePeriods = $filters['compare_periods'];
                $report['expenses_comparison'] = $expenseModel->compareExpensesPeriods(
                    $comparePeriods['period1'],
                    $comparePeriods['period2'],
                    $filters['branch_id'] ?? null
                );
            }

            return $report;
        } catch (Exception $e) {
            error_log('خطأ أثناء إنشاء تقرير المصروفات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على بيانات المنتجات المباعة
     * @param array $filters فلاتر البحث
     * @return array بيانات المنتجات المباعة
     */
    private function getSoldProducts($filters) {
        try {
            // طباعة الفلاتر للتشخيص
            $this->log('Filters for getSoldProducts: ' . json_encode($filters), 'debug');

            // تعديل الاستعلام ليستخدم جدول invoice_items بدلاً من inventory_transactions
            $sql = "SELECT
                       COALESCE(SUM(ii.quantity), 0) as total_quantity,
                       COALESCE(SUM(ii.total), 0) as total_value,
                       COALESCE(SUM(ii.quantity * p.cost), 0) as total_cost,
                       COALESCE(SUM(ii.total - (ii.quantity * p.cost)), 0) as total_profit
                    FROM invoice_items ii
                    JOIN products p ON ii.item_id = p.id AND ii.item_type = 'product'
                    JOIN invoices i ON ii.invoice_id = i.id
                    WHERE p.is_for_sale = 1 AND i.payment_status = 'paid'";

            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['branch_id'])) {
                $sql .= " AND i.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                $sql .= " AND DATE(i.created_at) BETWEEN :start_date AND :end_date";
                $bindings[':start_date'] = $filters['start_date'];
                $bindings[':end_date'] = $filters['end_date'];
            } elseif (!empty($filters['month']) && !empty($filters['year'])) {
                $sql .= " AND MONTH(i.created_at) = :month AND YEAR(i.created_at) = :year";
                $bindings[':month'] = $filters['month'];
                $bindings[':year'] = $filters['year'];
            }

            if (!empty($filters['category_id'])) {
                $sql .= " AND p.category_id = :category_id";
                $bindings[':category_id'] = $filters['category_id'];
            }

            // طباعة الاستعلام للتشخيص
            $this->log('SQL Query for getSoldProducts: ' . $sql, 'debug');
            $this->log('SQL Bindings: ' . json_encode($bindings), 'debug');

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            $result = $this->db->fetch();

            // طباعة النتيجة للتشخيص
            $this->log('Result from getSoldProducts: ' . json_encode($result), 'debug');

            // التأكد من أن النتيجة تحتوي على قيم صحيحة
            if ($result) {
                // التأكد من أن القيم ليست null
                $result['total_quantity'] = $result['total_quantity'] ?? 0;
                $result['total_value'] = $result['total_value'] ?? 0;
                $result['total_cost'] = $result['total_cost'] ?? 0;
                $result['total_profit'] = $result['total_profit'] ?? 0;

                return $result;
            } else {
                return [
                    'total_quantity' => 0,
                    'total_value' => 0,
                    'total_cost' => 0,
                    'total_profit' => 0
                ];
            }
        } catch (Exception $e) {
            $this->log('خطأ أثناء استرجاع بيانات المنتجات المباعة: ' . $e->getMessage(), 'error');
            throw $e;
        }
    }

    /**
     * الحصول على بيانات المنتجات المستهلكة داخليًا
     * @param array $filters فلاتر البحث
     * @return array بيانات المنتجات المستهلكة
     */
    private function getConsumedProducts($filters) {
        try {
            // طباعة الفلاتر للتشخيص
            $this->log('Filters for getConsumedProducts: ' . json_encode($filters), 'debug');

            $sql = "SELECT
                       COALESCE(SUM(ABS(it.quantity)), 0) as total_quantity,
                       COALESCE(SUM(ABS(it.quantity) * p.cost), 0) as total_value
                    FROM inventory_transactions it
                    JOIN products p ON it.product_id = p.id
                    WHERE it.transaction_type = 'out'
                    AND (it.notes LIKE '%استهلاك%' OR (p.is_for_sale = 0 AND it.notes NOT LIKE '%بيع منتج%' AND it.notes NOT LIKE '%فاتورة%'))";

            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['branch_id'])) {
                $sql .= " AND it.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                $sql .= " AND DATE(it.created_at) BETWEEN :start_date AND :end_date";
                $bindings[':start_date'] = $filters['start_date'];
                $bindings[':end_date'] = $filters['end_date'];
            } elseif (!empty($filters['month']) && !empty($filters['year'])) {
                $sql .= " AND MONTH(it.created_at) = :month AND YEAR(it.created_at) = :year";
                $bindings[':month'] = $filters['month'];
                $bindings[':year'] = $filters['year'];
            }

            if (!empty($filters['category_id'])) {
                $sql .= " AND p.category_id = :category_id";
                $bindings[':category_id'] = $filters['category_id'];
            }

            // طباعة الاستعلام للتشخيص
            $this->log('SQL Query for getConsumedProducts: ' . $sql, 'debug');
            $this->log('SQL Bindings: ' . json_encode($bindings), 'debug');

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            $result = $this->db->fetch();

            // طباعة النتيجة للتشخيص
            $this->log('Result from getConsumedProducts: ' . json_encode($result), 'debug');

            // التأكد من أن النتيجة تحتوي على قيم صحيحة
            if ($result) {
                // التأكد من أن القيم ليست null
                $result['total_quantity'] = $result['total_quantity'] ?? 0;
                $result['total_value'] = $result['total_value'] ?? 0;

                return $result;
            } else {
                return [
                    'total_quantity' => 0,
                    'total_value' => 0
                ];
            }
        } catch (Exception $e) {
            $this->log('خطأ أثناء استرجاع بيانات المنتجات المستهلكة: ' . $e->getMessage(), 'error');
            throw $e;
        }
    }

    /**
     * الحصول على المنتجات الأكثر مبيعًا
     * @param array $filters فلاتر البحث
     * @return array قائمة المنتجات الأكثر مبيعًا
     */
    private function getTopSellingProducts($filters) {
        try {
            // طباعة الفلاتر للتشخيص
            $this->log('Filters for getTopSellingProducts: ' . json_encode($filters), 'debug');

            $sql = "SELECT p.id, p.name as product_name, p.price, p.cost,
                           SUM(ii.quantity) as quantity,
                           SUM(ii.total) as total_value,
                           SUM(ii.quantity * p.cost) as total_cost,
                           SUM(ii.total - (ii.quantity * p.cost)) as total_profit
                    FROM products p
                    JOIN invoice_items ii ON p.id = ii.item_id AND ii.item_type = 'product'
                    JOIN invoices i ON ii.invoice_id = i.id
                    WHERE p.is_for_sale = 1 AND i.payment_status = 'paid'";

            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['branch_id'])) {
                $sql .= " AND i.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                $sql .= " AND DATE(i.created_at) BETWEEN :start_date AND :end_date";
                $bindings[':start_date'] = $filters['start_date'];
                $bindings[':end_date'] = $filters['end_date'];
            } elseif (!empty($filters['month']) && !empty($filters['year'])) {
                $sql .= " AND MONTH(i.created_at) = :month AND YEAR(i.created_at) = :year";
                $bindings[':month'] = $filters['month'];
                $bindings[':year'] = $filters['year'];
            }

            if (!empty($filters['category_id'])) {
                $sql .= " AND p.category_id = :category_id";
                $bindings[':category_id'] = $filters['category_id'];
            }

            // تجميع حسب المنتج وترتيب النتائج
            $sql .= " GROUP BY p.id, p.name, p.price, p.cost
                      ORDER BY quantity DESC
                      LIMIT 10";

            // طباعة الاستعلام للتشخيص
            $this->log('SQL Query for getTopSellingProducts: ' . $sql, 'debug');
            $this->log('SQL Bindings: ' . json_encode($bindings), 'debug');

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            $result = $this->db->fetchAll();

            // طباعة عدد النتائج للتشخيص
            $this->log('Number of top selling products: ' . count($result), 'debug');

            return $result;
        } catch (Exception $e) {
            $this->log('خطأ أثناء استرجاع المنتجات الأكثر مبيعًا: ' . $e->getMessage(), 'error');
            throw $e;
        }
    }

    /**
     * الحصول على المنتجات الأكثر استهلاكًا داخليًا
     * @param array $filters فلاتر البحث
     * @return array قائمة المنتجات الأكثر استهلاكًا
     */
    private function getTopConsumedProducts($filters) {
        try {
            // طباعة الفلاتر للتشخيص
            $this->log('Filters for getTopConsumedProducts: ' . json_encode($filters), 'debug');

            $sql = "SELECT p.id, p.name as product_name, p.cost,
                           COALESCE(SUM(ABS(it.quantity)), 0) as quantity,
                           COALESCE(SUM(ABS(it.quantity) * p.cost), 0) as total_value
                    FROM products p
                    JOIN inventory_transactions it ON p.id = it.product_id
                    WHERE it.transaction_type = 'out'
                    AND (it.notes LIKE '%استهلاك%' OR (p.is_for_sale = 0 AND it.notes NOT LIKE '%بيع منتج%' AND it.notes NOT LIKE '%فاتورة%'))";

            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['branch_id'])) {
                $sql .= " AND it.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                $sql .= " AND DATE(it.created_at) BETWEEN :start_date AND :end_date";
                $bindings[':start_date'] = $filters['start_date'];
                $bindings[':end_date'] = $filters['end_date'];
            } elseif (!empty($filters['month']) && !empty($filters['year'])) {
                $sql .= " AND MONTH(it.created_at) = :month AND YEAR(it.created_at) = :year";
                $bindings[':month'] = $filters['month'];
                $bindings[':year'] = $filters['year'];
            }

            if (!empty($filters['category_id'])) {
                $sql .= " AND p.category_id = :category_id";
                $bindings[':category_id'] = $filters['category_id'];
            }

            // تجميع حسب المنتج وترتيب النتائج
            $sql .= " GROUP BY p.id, p.name, p.cost
                      ORDER BY quantity DESC
                      LIMIT 10";

            // طباعة الاستعلام للتشخيص
            $this->log('SQL Query for getTopConsumedProducts: ' . $sql, 'debug');
            $this->log('SQL Bindings: ' . json_encode($bindings), 'debug');

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            $result = $this->db->fetchAll();

            // طباعة عدد النتائج للتشخيص
            $this->log('Number of top consumed products: ' . count($result), 'debug');

            return $result;
        } catch (Exception $e) {
            $this->log('خطأ أثناء استرجاع المنتجات الأكثر استهلاكًا: ' . $e->getMessage(), 'error');
            throw $e;
        }
    }
}
