<?php
/**
 * ملف التحقق من وضع الصيانة
 * يتم استدعاؤه في بداية كل صفحة للتحقق من وضع الصيانة
 */

// التحقق من وجود ملف وضع الصيانة
function isMaintenanceMode() {
    $maintenanceFile = __DIR__ . '/../config/maintenance_mode.txt';
    return file_exists($maintenanceFile);
}

// التحقق من استثناءات وضع الصيانة
function isMaintenanceException() {
    // قائمة الصفحات المستثناة من وضع الصيانة
    $exceptions = [
        'maintenance.php',
        'maintenance_control.php',
        'login.php',
        'logout.php'
    ];
    
    $currentScript = basename($_SERVER['SCRIPT_NAME']);
    
    // السماح للصفحات المستثناة
    if (in_array($currentScript, $exceptions)) {
        return true;
    }
    
    // السماح للمدراء بالوصول
    if (isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin') {
        return true;
    }
    
    // السماح لطلبات AJAX
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        return true;
    }
    
    return false;
}

// إعادة التوجيه لصفحة الصيانة
function redirectToMaintenance() {
    $maintenanceUrl = '/backup/new1/maintenance.php';
    
    // التحقق من أن المستخدم ليس في صفحة الصيانة بالفعل
    if (basename($_SERVER['SCRIPT_NAME']) !== 'maintenance.php') {
        header('Location: ' . $maintenanceUrl);
        exit;
    }
}

// الدالة الرئيسية للتحقق من الصيانة
function checkMaintenanceMode() {
    // إذا كان النظام في وضع الصيانة
    if (isMaintenanceMode()) {
        // إذا لم يكن المستخدم مستثنى
        if (!isMaintenanceException()) {
            redirectToMaintenance();
        }
    }
}

// تشغيل التحقق تلقائياً إذا تم استدعاء الملف
if (!defined('MAINTENANCE_CHECK_DISABLED')) {
    checkMaintenanceMode();
}
?>
