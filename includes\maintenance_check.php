<?php
/**
 * ملف التحقق من وضع الصيانة
 * يتم استدعاؤه في بداية كل صفحة للتحقق من وضع الصيانة
 */

// التحقق من وجود ملف وضع الصيانة
function isMaintenanceMode() {
    $maintenanceFile = __DIR__ . '/../config/maintenance_mode.txt';
    $exists = file_exists($maintenanceFile);

    // تسجيل للتشخيص
    error_log('Maintenance check: File path = ' . $maintenanceFile . ', Exists = ' . ($exists ? 'true' : 'false'));

    return $exists;
}

// التحقق من استثناءات وضع الصيانة
function isMaintenanceException() {
    // قائمة الصفحات المستثناة من وضع الصيانة
    $exceptions = [
        'maintenance.php',
        'maintenance_control.php',
        'login.php',
        'logout.php'
    ];
    
    $currentScript = basename($_SERVER['SCRIPT_NAME']);
    
    // السماح للصفحات المستثناة
    if (in_array($currentScript, $exceptions)) {
        return true;
    }
    
    // السماح للمدراء بالوصول
    if (isset($_SESSION['user_role'])) {
        // استخدام الثوابت المعرفة في النظام
        $allowedRoles = [
            defined('ROLE_ADMIN') ? ROLE_ADMIN : 'admin',
            defined('ROLE_MANAGER') ? ROLE_MANAGER : 'manager',
            'مدير' // للتوافق مع النظم القديمة
        ];

        if (in_array($_SESSION['user_role'], $allowedRoles)) {
            return true;
        }
    }
    
    // السماح لطلبات AJAX
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        return true;
    }
    
    return false;
}

// إعادة التوجيه لصفحة الصيانة
function redirectToMaintenance() {
    // التحقق من أن المستخدم ليس في صفحة الصيانة بالفعل
    if (basename($_SERVER['SCRIPT_NAME']) !== 'maintenance.php') {
        // تحديد المسار الأساسي للموقع
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        $scriptName = $_SERVER['SCRIPT_NAME'];

        // العثور على المجلد الجذر للموقع
        $documentRoot = $_SERVER['DOCUMENT_ROOT'];
        $currentPath = dirname($_SERVER['SCRIPT_FILENAME']);

        // حساب المسار النسبي للجذر
        $relativePath = str_replace($documentRoot, '', $currentPath);
        $relativePath = str_replace('\\', '/', $relativePath); // لنظام Windows

        // إزالة المجلدات الفرعية للوصول للجذر
        $pathParts = explode('/', trim($relativePath, '/'));
        $levelsUp = count(array_filter($pathParts));

        // بناء المسار لصفحة الصيانة
        if ($levelsUp > 0) {
            $maintenanceUrl = str_repeat('../', $levelsUp) . 'maintenance.php';
        } else {
            $maintenanceUrl = 'maintenance.php';
        }

        // تسجيل للتشخيص
        error_log("Maintenance redirect: Current path = $currentPath, Levels up = $levelsUp, URL = $maintenanceUrl");

        header('Location: ' . $maintenanceUrl);
        exit;
    }
}

// الدالة الرئيسية للتحقق من الصيانة
function checkMaintenanceMode() {
    // تسجيل معلومات التشخيص
    $currentScript = basename($_SERVER['SCRIPT_NAME']);
    $userRole = $_SESSION['user_role'] ?? 'غير محدد';

    error_log('Maintenance check: Current script = ' . $currentScript . ', User role = ' . $userRole);

    // إذا كان النظام في وضع الصيانة
    if (isMaintenanceMode()) {
        error_log('Maintenance mode is ACTIVE');

        // إذا لم يكن المستخدم مستثنى
        if (!isMaintenanceException()) {
            error_log('User is NOT exempt, redirecting to maintenance page');
            redirectToMaintenance();
        } else {
            error_log('User is exempt from maintenance mode');
        }
    } else {
        error_log('Maintenance mode is NOT active');
    }
}

// تشغيل التحقق تلقائياً إذا تم استدعاء الملف
if (!defined('MAINTENANCE_CHECK_DISABLED')) {
    checkMaintenanceMode();
}
?>
