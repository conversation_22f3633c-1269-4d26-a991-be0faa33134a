<?php
/**
 * صفحة اختبار إصلاح مشكلة الرواتب
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once 'config/init.php';

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head><meta charset='UTF-8'><title>اختبار إصلاح مشكلة الرواتب</title></head>";
echo "<body style='font-family: Arial, sans-serif; margin: 20px;'>";

echo "<h1>🧪 اختبار إصلاح مشكلة الرواتب</h1>";

// اختبار البيانات
$testCases = [
    [
        'title' => 'تقرير شهر مايو 2025',
        'start_date' => '2025-05-01',
        'end_date' => '2025-05-31',
        'expected' => 'يجب أن يظهر راتب الموظف رقم 3 لشهر 5'
    ],
    [
        'title' => 'تقرير شهر يونيو 2025',
        'start_date' => '2025-06-01',
        'end_date' => '2025-06-30',
        'expected' => 'يجب ألا يظهر راتب الموظف رقم 3 لشهر 5'
    ]
];

foreach ($testCases as $test) {
    echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 15px 0; border-radius: 5px;'>";
    echo "<h2>📊 {$test['title']}</h2>";
    echo "<p><strong>الفترة:</strong> {$test['start_date']} إلى {$test['end_date']}</p>";
    echo "<p><strong>المتوقع:</strong> {$test['expected']}</p>";
    
    // تحضير المرشحات
    $filters = [
        'start_date' => $test['start_date'],
        'end_date' => $test['end_date'],
        'branch_id' => 1 // افتراض الفرع الأول
    ];
    
    // اختبار الاستعلام الجديد
    echo "<h3>🔍 نتائج الاختبار:</h3>";
    
    try {
        $startDate = $filters['start_date'];
        $endDate = $filters['end_date'];
        
        // حساب الشهر والسنة
        $startMonth = date('n', strtotime($startDate));
        $startYear = date('Y', strtotime($startDate));
        $endMonth = date('n', strtotime($endDate));
        $endYear = date('Y', strtotime($endDate));
        
        echo "<p><strong>تفاصيل الحساب:</strong></p>";
        echo "<ul>";
        echo "<li>شهر البداية: $startMonth، سنة البداية: $startYear</li>";
        echo "<li>شهر النهاية: $endMonth، سنة النهاية: $endYear</li>";
        echo "</ul>";
        
        // الاستعلام الجديد المحسن
        $employeeSalaryQuery = "SELECT es.*, e.name as employee_name, e.position
                              FROM employee_salaries es
                              LEFT JOIN employees e ON es.employee_id = e.id
                              WHERE es.payment_status = 'paid'
                              AND (
                                  (es.year = :start_year AND es.year = :end_year AND es.month >= :start_month AND es.month <= :end_month) OR
                                  (es.year = :start_year AND es.year < :end_year AND es.month >= :start_month) OR
                                  (es.year = :end_year AND es.year > :start_year AND es.month <= :end_month) OR
                                  (es.year > :start_year AND es.year < :end_year)
                              )";
        
        if (!empty($filters['branch_id'])) {
            $employeeSalaryQuery .= " AND e.branch_id = :branch_id";
        }
        
        $employeeSalaryQuery .= " ORDER BY es.year DESC, es.month DESC, es.employee_id";
        
        $db->prepare($employeeSalaryQuery);
        $db->bind(':start_year', $startYear);
        $db->bind(':start_month', $startMonth);
        $db->bind(':end_year', $endYear);
        $db->bind(':end_month', $endMonth);
        
        if (!empty($filters['branch_id'])) {
            $db->bind(':branch_id', $filters['branch_id']);
        }
        
        $salaries = $db->fetchAll();
        
        echo "<p><strong>عدد الرواتب الموجودة:</strong> " . count($salaries) . "</p>";
        
        if (!empty($salaries)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th>معرف الراتب</th>";
            echo "<th>اسم الموظف</th>";
            echo "<th>الشهر</th>";
            echo "<th>السنة</th>";
            echo "<th>المبلغ</th>";
            echo "<th>تاريخ الدفع</th>";
            echo "<th>الحالة</th>";
            echo "</tr>";
            
            $totalAmount = 0;
            foreach ($salaries as $salary) {
                $totalAmount += $salary['total_amount'];
                echo "<tr>";
                echo "<td>{$salary['id']}</td>";
                echo "<td>{$salary['employee_name']}</td>";
                echo "<td>{$salary['month']}</td>";
                echo "<td>{$salary['year']}</td>";
                echo "<td>" . number_format($salary['total_amount'], 2) . " جنيه</td>";
                echo "<td>" . ($salary['payment_date'] ?? 'غير محدد') . "</td>";
                echo "<td>{$salary['payment_status']}</td>";
                echo "</tr>";
            }
            
            echo "<tr style='background: #e0e0e0; font-weight: bold;'>";
            echo "<td colspan='4'>الإجمالي</td>";
            echo "<td>" . number_format($totalAmount, 2) . " جنيه</td>";
            echo "<td colspan='2'></td>";
            echo "</tr>";
            echo "</table>";
        } else {
            echo "<p style='color: #666;'>لا توجد رواتب مدفوعة في هذه الفترة</p>";
        }
        
        // عرض الاستعلام المستخدم
        echo "<details style='margin: 10px 0;'>";
        echo "<summary style='cursor: pointer; background: #f5f5f5; padding: 5px;'>عرض الاستعلام المستخدم</summary>";
        echo "<pre style='background: #f8f8f8; padding: 10px; border: 1px solid #ddd; overflow-x: auto;'>";
        echo htmlspecialchars($employeeSalaryQuery);
        echo "</pre>";
        echo "</details>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'><strong>خطأ:</strong> " . $e->getMessage() . "</p>";
    }
    
    echo "</div>";
}

// اختبار مقارنة مع الطريقة القديمة
echo "<div style='border: 2px solid #007bff; padding: 15px; margin: 15px 0; border-radius: 5px; background: #f8f9ff;'>";
echo "<h2>📈 مقارنة مع الطريقة القديمة</h2>";

try {
    // الطريقة القديمة (بناءً على payment_date)
    echo "<h3>🔴 الطريقة القديمة (payment_date):</h3>";
    $oldQuery = "SELECT es.*, e.name as employee_name, e.position
                 FROM employee_salaries es
                 LEFT JOIN employees e ON es.employee_id = e.id
                 WHERE es.payment_status = 'paid'
                 AND es.payment_date BETWEEN '2025-06-01' AND '2025-06-30'
                 ORDER BY es.payment_date DESC";
    
    $db->prepare($oldQuery);
    $oldSalaries = $db->fetchAll();
    
    echo "<p><strong>رواتب يونيو بالطريقة القديمة:</strong> " . count($oldSalaries) . " راتب</p>";
    
    if (!empty($oldSalaries)) {
        $oldTotal = 0;
        foreach ($oldSalaries as $salary) {
            $oldTotal += $salary['total_amount'];
            echo "<p>- {$salary['employee_name']}: " . number_format($salary['total_amount'], 2) . " جنيه (شهر {$salary['month']}/{$salary['year']})</p>";
        }
        echo "<p><strong>الإجمالي القديم:</strong> " . number_format($oldTotal, 2) . " جنيه</p>";
    }
    
    // الطريقة الجديدة (بناءً على month/year)
    echo "<h3>🟢 الطريقة الجديدة (month/year):</h3>";
    $newQuery = "SELECT es.*, e.name as employee_name, e.position
                 FROM employee_salaries es
                 LEFT JOIN employees e ON es.employee_id = e.id
                 WHERE es.payment_status = 'paid'
                 AND es.year = 2025 AND es.month = 6
                 ORDER BY es.employee_id";
    
    $db->prepare($newQuery);
    $newSalaries = $db->fetchAll();
    
    echo "<p><strong>رواتب يونيو بالطريقة الجديدة:</strong> " . count($newSalaries) . " راتب</p>";
    
    if (!empty($newSalaries)) {
        $newTotal = 0;
        foreach ($newSalaries as $salary) {
            $newTotal += $salary['total_amount'];
            echo "<p>- {$salary['employee_name']}: " . number_format($salary['total_amount'], 2) . " جنيه (شهر {$salary['month']}/{$salary['year']})</p>";
        }
        echo "<p><strong>الإجمالي الجديد:</strong> " . number_format($newTotal, 2) . " جنيه</p>";
    } else {
        echo "<p style='color: green;'>✅ لا توجد رواتب لشهر يونيو (هذا صحيح!)</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>خطأ في المقارنة:</strong> " . $e->getMessage() . "</p>";
}

echo "</div>";

// عرض بيانات الموظف رقم 3 تحديداً
echo "<div style='border: 2px solid #28a745; padding: 15px; margin: 15px 0; border-radius: 5px; background: #f8fff8;'>";
echo "<h2>👤 بيانات الموظف رقم 3 تحديداً</h2>";

try {
    $employeeQuery = "SELECT es.*, e.name as employee_name
                     FROM employee_salaries es
                     LEFT JOIN employees e ON es.employee_id = e.id
                     WHERE es.employee_id = 3
                     AND es.payment_status = 'paid'
                     ORDER BY es.year DESC, es.month DESC";
    
    $db->prepare($employeeQuery);
    $employeeSalaries = $db->fetchAll();
    
    echo "<p><strong>جميع رواتب الموظف رقم 3:</strong></p>";
    
    if (!empty($employeeSalaries)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>الشهر/السنة</th>";
        echo "<th>المبلغ</th>";
        echo "<th>تاريخ الدفع</th>";
        echo "<th>ملاحظات</th>";
        echo "</tr>";
        
        foreach ($employeeSalaries as $salary) {
            $monthYear = $salary['month'] . '/' . $salary['year'];
            $paymentDate = $salary['payment_date'] ?? 'غير محدد';
            
            // تحديد لون الصف حسب الشهر
            $rowColor = '';
            if ($salary['month'] == 5 && $salary['year'] == 2025) {
                $rowColor = 'background: #d4edda;'; // أخضر فاتح لشهر مايو
            } elseif ($salary['month'] == 6 && $salary['year'] == 2025) {
                $rowColor = 'background: #f8d7da;'; // أحمر فاتح لشهر يونيو
            }
            
            echo "<tr style='$rowColor'>";
            echo "<td>$monthYear</td>";
            echo "<td>" . number_format($salary['total_amount'], 2) . " جنيه</td>";
            echo "<td>$paymentDate</td>";
            echo "<td>";
            if ($salary['month'] == 5 && $paymentDate == '2025-06-01') {
                echo "راتب مايو مدفوع في يونيو ✅";
            }
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>لا توجد رواتب للموظف رقم 3</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>خطأ:</strong> " . $e->getMessage() . "</p>";
}

echo "</div>";

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h2>📝 ملخص الإصلاح</h2>";
echo "<ul>";
echo "<li><strong>المشكلة:</strong> كانت الرواتب تظهر في التقرير بناءً على تاريخ الدفع (payment_date)</li>";
echo "<li><strong>الحل:</strong> تم تغيير المنطق ليعتمد على شهر وسنة الراتب (month, year)</li>";
echo "<li><strong>النتيجة:</strong> راتب مايو للموظف رقم 3 سيظهر في تقرير مايو وليس يونيو</li>";
echo "<li><strong>الفائدة:</strong> التقارير تعكس الآن الرواتب الصحيحة لكل شهر</li>";
echo "</ul>";
echo "</div>";

echo "</body></html>";
?>
