<?php
/**
 * صفحة لوحة التحكم الرئيسية
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// عنوان الصفحة
$pageTitle = 'لوحة التحكم';

// استدعاء رأس الصفحة
include '../includes/templates/header.php';

// إنشاء كائنات النماذج
$invoiceModel = new Invoice($db);
$customerModel = new Customer($db);
$employeeModel = new Employee($db);
$appointmentModel = new Appointment($db);
$inventoryModel = new Inventory($db);
$expenseModel = new Expense($db);
$endDayModel = new EndDay($db);

// فلتر الفرع الحالي
$branchId = $_SESSION['user_branch_id'] ?? 1; // استخدام الفرع رقم 1 كقيمة افتراضية إذا لم يكن محدداً

// التحقق من دور المستخدم - إذا كان كاشير، عرض فقط بيانات اليوم المفتوح
$isCashier = ($_SESSION['user_role'] === ROLE_CASHIER);
$isAdmin = ($_SESSION['user_role'] === ROLE_ADMIN || $_SESSION['user_role'] === ROLE_MANAGER);

// الحصول على اليوم المفتوح الحالي أو آخر يوم مغلق
$openDay = null;
$hasOpenDay = false;
$openDayId = null;
$hasLastClosedDay = false;

// تسجيل معلومات للتشخيص
error_log('Dashboard - User role: ' . $_SESSION['user_role'] . ', isCashier: ' . ($isCashier ? 'true' : 'false') . ', isAdmin: ' . ($isAdmin ? 'true' : 'false'));
error_log('Dashboard - Getting open day for branch ID: ' . $branchId);

// البحث عن يوم عمل مفتوح للفرع
$sql = "SELECT id, date FROM end_days WHERE branch_id = :branch_id AND closed_at IS NULL";
$db->prepare($sql);
$db->bind(':branch_id', $branchId);
$openDayRecord = $db->fetch();

// طباعة نتيجة الاستعلام للتشخيص

if ($openDayRecord) {
    $openDayId = $openDayRecord['id'];
    $openDay = $openDayRecord['date'];
    $hasOpenDay = true;
    error_log('Dashboard - Open day found: ID=' . $openDayId . ', Date=' . $openDay);
} else {
    // إذا لم يكن هناك يوم مفتوح، نبحث عن آخر يوم مغلق
    error_log('Dashboard - No open day found, looking for last closed day');
    $sql = "SELECT id, date FROM end_days WHERE branch_id = :branch_id AND closed_at IS NOT NULL ORDER BY closed_at DESC LIMIT 1";
    $db->prepare($sql);
    $db->bind(':branch_id', $branchId);
    $lastClosedDay = $db->fetch();

    // طباعة نتيجة الاستعلام للتشخيص

    if ($lastClosedDay) {
        $openDayId = $lastClosedDay['id'];
        $openDay = $lastClosedDay['date'];
        $hasLastClosedDay = true;
        error_log('Dashboard - Last closed day found: ID=' . $openDayId . ', Date=' . $openDay);
    } else {
        error_log('Dashboard - No open day and no last closed day found');
    }
}

// فلاتر المبيعات الأساسية
$salesFilters = [
    'branch_id' => $branchId,
    'payment_status' => 'paid'
];

// إجمالي المبيعات اليوم (الفواتير المدفوعة فقط)
$todaySales = 0;

// التحقق من دور المستخدم وحالة يوم العمل
if ($hasOpenDay) {
    // إذا كان هناك يوم مفتوح، عرض مبيعات اليوم المفتوح لجميع المستخدمين
    $salesFilters['end_day_id'] = $openDayId;
    $todaySales = $invoiceModel->getTotalSales($salesFilters);

    error_log('Dashboard - Total sales for open day (ID=' . $openDayId . '): ' . $todaySales);
} elseif (!$isCashier && $hasLastClosedDay) {
    // إذا لم يكن هناك يوم مفتوح ولكن هناك آخر يوم مغلق والمستخدم ليس كاشير
    $salesFilters['end_day_id'] = $openDayId;
    $todaySales = $invoiceModel->getTotalSales($salesFilters);

    error_log('Dashboard - Total sales for last closed day (ID=' . $openDayId . '): ' . $todaySales);
} else {
    // إذا لم يكن هناك يوم مفتوح والمستخدم كاشير، أو لم يكن هناك أي يوم عمل
    $todaySales = 0;
    error_log('Dashboard - No open day or last closed day, showing zero sales');
}

// إجمالي المبيعات هذا الأسبوع (الفواتير المدفوعة فقط)
$weekStart = date('Y-m-d', strtotime('monday this week'));
$weekSalesFilters = [
    'branch_id' => $branchId,
    'payment_status' => 'paid'
];

// التحقق من دور المستخدم وحالة يوم العمل
$weekSales = 0;

if ($hasOpenDay) {
    // إذا كان هناك يوم مفتوح، عرض نفس بيانات اليوم المفتوح لجميع المستخدمين
    $weekSalesFilters['end_day_id'] = $openDayId;
    $weekSales = $invoiceModel->getTotalSales($weekSalesFilters);
    error_log('Dashboard - Weekly sales for open day (ID=' . $openDayId . '): ' . $weekSales);
} elseif (!$isCashier && $hasLastClosedDay) {
    // إذا لم يكن هناك يوم مفتوح ولكن هناك آخر يوم مغلق والمستخدم ليس كاشير
    $weekSalesFilters['end_day_id'] = $openDayId;
    $weekSales = $invoiceModel->getTotalSales($weekSalesFilters);
    error_log('Dashboard - Weekly sales for last closed day (ID=' . $openDayId . '): ' . $weekSales);
} else {
    // إذا لم يكن هناك يوم مفتوح والمستخدم كاشير، أو لم يكن هناك أي يوم عمل
    $weekSales = 0;
    error_log('Dashboard - No open day or last closed day, showing zero weekly sales');
}

// إجمالي المبيعات هذا الشهر (الفواتير المدفوعة فقط)
$monthStart = date('Y-m-01');
$monthSalesFilters = [
    'branch_id' => $branchId,
    'payment_status' => 'paid'
];

// التحقق من دور المستخدم وحالة يوم العمل
$monthSales = 0;

if ($hasOpenDay) {
    // إذا كان هناك يوم مفتوح، عرض نفس بيانات اليوم المفتوح لجميع المستخدمين
    $monthSalesFilters['end_day_id'] = $openDayId;
    $monthSales = $invoiceModel->getTotalSales($monthSalesFilters);
    error_log('Dashboard - Monthly sales for open day (ID=' . $openDayId . '): ' . $monthSales);
} elseif (!$isCashier && $hasLastClosedDay) {
    // إذا لم يكن هناك يوم مفتوح ولكن هناك آخر يوم مغلق والمستخدم ليس كاشير
    $monthSalesFilters['end_day_id'] = $openDayId;
    $monthSales = $invoiceModel->getTotalSales($monthSalesFilters);
    error_log('Dashboard - Monthly sales for last closed day (ID=' . $openDayId . '): ' . $monthSales);
} else {
    // إذا لم يكن هناك يوم مفتوح والمستخدم كاشير، أو لم يكن هناك أي يوم عمل
    $monthSales = 0;
    error_log('Dashboard - No open day or last closed day, showing zero monthly sales');
}

// الحصول على بيانات المصروفات
$todayExpenses = 0;

// التحقق من دور المستخدم وحالة يوم العمل
if ($hasOpenDay) {
    // إذا كان هناك يوم مفتوح، عرض مصروفات اليوم المفتوح لجميع المستخدمين
    $todayExpenses = $expenseModel->getTotalExpenses(['end_day_id' => $openDayId]);
    error_log('Dashboard - Expenses for open day (ID=' . $openDayId . '): ' . $todayExpenses);
} elseif (!$isCashier && $hasLastClosedDay) {
    // إذا لم يكن هناك يوم مفتوح ولكن هناك آخر يوم مغلق والمستخدم ليس كاشير
    $todayExpenses = $expenseModel->getTotalExpenses(['end_day_id' => $openDayId]);
    error_log('Dashboard - Expenses for last closed day (ID=' . $openDayId . '): ' . $todayExpenses);
} else {
    // إذا لم يكن هناك يوم مفتوح والمستخدم كاشير، أو لم يكن هناك أي يوم عمل
    $todayExpenses = 0;
    error_log('Dashboard - No open day or last closed day, showing zero expenses');
}

// عدد المواعيد اليوم
$todayAppointments = $appointmentModel->getAppointmentsCount([
    'date' => date('Y-m-d'),
    'branch_id' => $branchId
]);

// عدد العملاء
$totalCustomers = $customerModel->getCustomersCount(['branch_id' => $branchId]);

// عدد المنتجات منخفضة المخزون
$lowStockProducts = count($inventoryModel->getLowStockProducts($branchId));

// استرجاع أفضل الموظفين
// إذا كان المستخدم كاشير، نحتاج إلى استعلام مخصص للحصول على أفضل الموظفين لليوم المفتوح
$topEmployees = [];

if ($isCashier && $hasOpenDay) {
    // استعلام مخصص للحصول على أفضل الموظفين لليوم المفتوح
    $sql = "SELECT e.id, e.name, e.position, COUNT(ii.id) as services_count, SUM(ii.total) as total_sales
           FROM employees e
           LEFT JOIN invoice_items ii ON e.id = ii.employee_id
           LEFT JOIN invoices i ON ii.invoice_id = i.id
           WHERE e.is_active = 1
           AND i.end_day_id = :end_day_id";

    if ($branchId) {
        $sql .= " AND e.branch_id = :branch_id";
    }

    $sql .= " GROUP BY e.id
             ORDER BY total_sales DESC
             LIMIT 5";

    $db->prepare($sql);
    $db->bind(':end_day_id', $openDayId);

    if ($branchId) {
        $db->bind(':branch_id', $branchId);
    }

    $topEmployees = $db->fetchAll();
} elseif (!$isCashier) {
    // للمستخدمين الآخرين، نستخدم الطريقة العادية
    $topEmployees = $employeeModel->getTopEmployees(5, 'month', $branchId);
}

// استرجاع أفضل العملاء
$topCustomers = [];

if ($isCashier && $hasOpenDay) {
    // استعلام مخصص للحصول على أفضل العملاء لليوم المفتوح
    $sql = "SELECT c.id, c.name, c.phone, COUNT(i.id) as visits_count, SUM(i.final_amount) as total_sales
           FROM customers c
           JOIN invoices i ON c.id = i.customer_id
           WHERE i.end_day_id = :end_day_id";

    if ($branchId) {
        $sql .= " AND i.branch_id = :branch_id";
    }

    $sql .= " GROUP BY c.id
             ORDER BY total_sales DESC
             LIMIT 5";

    $db->prepare($sql);
    $db->bind(':end_day_id', $openDayId);

    if ($branchId) {
        $db->bind(':branch_id', $branchId);
    }

    $topCustomers = $db->fetchAll();
} elseif (!$isCashier) {
    // للمستخدمين الآخرين، نستخدم الطريقة العادية
    $topCustomers = $customerModel->getTopCustomers(5, $branchId);
}

// استرجاع المواعيد القادمة
// استخدام طريقة getAppointmentsByDate بدلاً من getUpcomingAppointments التي لا توجد
$upcomingAppointments = $appointmentModel->getAppointmentsByDate(date('Y-m-d'), [
    'branch_id' => $branchId,
    'status' => 'booked'
]);

// ترتيب المواعيد حسب وقت البدء والاقتصار على 5 مواعيد فقط
$currentTime = date('H:i:s');
$filteredAppointments = [];

foreach ($upcomingAppointments as $appointment) {
    if ($appointment['start_time'] >= $currentTime) {
        $filteredAppointments[] = $appointment;
        if (count($filteredAppointments) >= 5) {
            break;
        }
    }
}

$upcomingAppointments = $filteredAppointments;

// بيانات الرسم البياني للمبيعات
$salesChartData = [];

// أسماء الأيام بالعربية
$arabicDays = [
    'Saturday' => 'السبت',
    'Sunday' => 'الأحد',
    'Monday' => 'الإثنين',
    'Tuesday' => 'الثلاثاء',
    'Wednesday' => 'الأربعاء',
    'Thursday' => 'الخميس',
    'Friday' => 'الجمعة'
];

// التحقق من دور المستخدم وحالة يوم العمل
error_log('Dashboard - Preparing chart data, hasOpenDay: ' . ($hasOpenDay ? 'true' : 'false') . ', isCashier: ' . ($isCashier ? 'true' : 'false') . ', isAdmin: ' . ($isAdmin ? 'true' : 'false'));

if ($hasOpenDay) {
    // إذا كان هناك يوم مفتوح، عرض بيانات اليوم المفتوح لجميع المستخدمين
    $today = date('Y-m-d');
    $dayName = date('l');
    $dayName = $arabicDays[$dayName] ?? $dayName;

    $salesChartData[] = [
        'day' => $dayName . ' (اليوم المفتوح)',
        'date' => $today,
        'sales' => $todaySales
    ];
    error_log('Dashboard - Chart data for open day: ' . $todaySales);
} elseif (!$isCashier && $hasLastClosedDay) {
    // إذا لم يكن هناك يوم مفتوح ولكن هناك آخر يوم مغلق والمستخدم ليس كاشير
    $today = date('Y-m-d');
    $dayName = date('l');
    $dayName = $arabicDays[$dayName] ?? $dayName;

    $salesChartData[] = [
        'day' => $dayName . ' (آخر يوم مغلق)',
        'date' => $today,
        'sales' => $todaySales
    ];
    error_log('Dashboard - Chart data for last closed day: ' . $todaySales);
} else {
    // إذا لم يكن هناك يوم مفتوح والمستخدم كاشير، أو لم يكن هناك أي يوم عمل
    $today = date('Y-m-d');
    $dayName = date('l');
    $dayName = $arabicDays[$dayName] ?? $dayName;

    $salesChartData[] = [
        'day' => $dayName . ' (لا يوجد يوم مفتوح)',
        'date' => $today,
        'sales' => 0
    ];
    error_log('Dashboard - Chart data for no open day: 0');
}

// إذا كان المستخدم مدير أو ادمن، أضف بيانات آخر 7 أيام للرسم البياني
if ($isAdmin) {
    // مسح البيانات السابقة للرسم البياني
    $salesChartData = [];

    // للمستخدمين الآخرين، عرض بيانات آخر 7 أيام
    for ($i = 6; $i >= 0; $i--) {
        $date = date('Y-m-d', strtotime("-$i days"));
        $dayName = date('l', strtotime("-$i days"));
        $dayName = $arabicDays[$dayName] ?? $dayName;

        $daySales = $invoiceModel->getTotalSales([
            'start_date' => $date,
            'end_date' => $date,
            'branch_id' => $branchId
        ]);

        $salesChartData[] = [
            'day' => $dayName,
            'date' => $date,
            'sales' => $daySales
        ];
    }
}

// تحويل بيانات الرسم البياني إلى JSON
$salesChartJson = json_encode($salesChartData);
?>
<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <!-- صف الإحصائيات -->
    <div class="row">
        <!-- مبيعات اليوم -->
        <div class="col-md-3 mb-4">
            <div class="small-card bg-primary-light">
                <div class="card-info">
                    <h6 class="card-title"><?php
                        if ($hasOpenDay) {
                            echo 'مبيعات اليوم المفتوح';
                        } elseif ($openDayId) {
                            echo 'مبيعات آخر يوم مغلق';
                        } else {
                            echo 'مبيعات اليوم';
                        }
                    ?></h6>
                    <h3 class="card-value"><?php echo number_format($todaySales, 2); ?></h3>
                </div>
                <div class="card-icon bg-primary text-white">
                    <i class="fas fa-cash-register"></i>
                </div>
            </div>
        </div>

        <!-- مصروفات اليوم -->
        <div class="col-md-3 mb-4">
            <div class="small-card bg-danger-light">
                <div class="card-info">
                    <h6 class="card-title"><?php
                        if ($hasOpenDay) {
                            echo 'مصروفات اليوم المفتوح';
                        } elseif ($openDayId) {
                            echo 'مصروفات آخر يوم مغلق';
                        } else {
                            echo 'مصروفات اليوم';
                        }
                    ?></h6>
                    <h3 class="card-value"><?php echo number_format($todayExpenses, 2); ?></h3>
                </div>
                <div class="card-icon bg-danger text-white">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
            </div>
        </div>

        <!-- المواعيد اليوم -->
        <div class="col-md-3 mb-4">
            <div class="small-card bg-info-light">
                <div class="card-info">
                    <h6 class="card-title">مواعيد اليوم</h6>
                    <h3 class="card-value"><?php echo $todayAppointments; ?></h3>
                </div>
                <div class="card-icon bg-info text-white">
                    <i class="fas fa-calendar-check"></i>
                </div>
            </div>
        </div>

        <!-- المنتجات منخفضة المخزون -->
        <div class="col-md-3 mb-4">
            <div class="small-card bg-warning-light">
                <div class="card-info">
                    <h6 class="card-title">منتجات منخفضة المخزون</h6>
                    <h3 class="card-value"><?php echo $lowStockProducts; ?></h3>
                </div>
                <div class="card-icon bg-warning text-white">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- صف الرسوم البيانية والجداول -->
    <div class="row">
        <!-- رسم بياني المبيعات -->
        <div class="col-md-8 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent py-3">
                    <h5 class="mb-0"><?php
                        if ($isAdmin) {
                            echo 'المبيعات خلال الأسبوع الماضي';
                        } elseif ($hasOpenDay) {
                            echo 'مبيعات اليوم المفتوح';
                        } elseif ($openDayId) {
                            echo 'مبيعات آخر يوم مغلق';
                        } else {
                            echo 'لا يوجد يوم عمل مفتوح';
                        }
                    ?></h5>
                </div>
                <div class="card-body">
                    <canvas id="salesChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- قائمة المواعيد القادمة -->
        <div class="col-md-4 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent py-3 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">المواعيد القادمة</h5>
                    <?php if (hasPermission('appointments_view')): ?>
                    <a href="../pages/appointments/index.php" class="btn btn-sm btn-primary">عرض الكل</a>
                    <?php endif; ?>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        <?php if (empty($upcomingAppointments)): ?>
                            <div class="list-group-item text-center text-muted py-4">
                                <i class="fas fa-calendar-times mb-2 fa-2x"></i>
                                <p class="mb-0">لا توجد مواعيد قادمة اليوم</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($upcomingAppointments as $appointment): ?>
                                <div class="list-group-item py-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($appointment['customer_name']); ?></h6>
                                            <p class="small text-muted mb-0"><?php echo htmlspecialchars($appointment['service_name']); ?></p>
                                        </div>
                                        <div class="text-end">
                                            <h6 class="mb-1 text-primary"><?php echo date('h:i A', strtotime($appointment['start_time'])); ?></h6>
                                            <p class="small text-muted mb-0"><?php echo htmlspecialchars($appointment['employee_name']); ?></p>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- صف أفضل الموظفين والعملاء -->
    <?php if (!$isCashier): ?>
    <div class="row">
        <!-- أفضل الموظفين -->
        <div class="col-md-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent py-3 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">أفضل الموظفين هذا الشهر</h5>
                    <?php if (hasPermission('employees_view')): ?>
                    <a href="../pages/employees/index.php" class="btn btn-sm btn-primary">عرض الكل</a>
                    <?php endif; ?>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>الموظف</th>
                                    <th>الخدمات</th>
                                    <th>المبيعات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($topEmployees)): ?>
                                    <tr>
                                        <td colspan="3" class="text-center py-4 text-muted">لا توجد بيانات متاحة</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($topEmployees as $employee): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($employee['name']); ?></td>
                                            <td><?php echo $employee['services_count']; ?></td>
                                            <td><?php echo number_format($employee['total_sales'], 2); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- أفضل العملاء -->
        <div class="col-md-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent py-3 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">أفضل العملاء</h5>
                    <?php if (hasPermission('customers_view')): ?>
                    <a href="../pages/customers/index.php" class="btn btn-sm btn-primary">عرض الكل</a>
                    <?php endif; ?>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>العميل</th>
                                    <th>الزيارات</th>
                                    <th>المشتريات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($topCustomers)): ?>
                                    <tr>
                                        <td colspan="3" class="text-center py-4 text-muted">لا توجد بيانات متاحة</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($topCustomers as $customer): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($customer['name']); ?></td>
                                            <td><?php echo isset($customer['visits_count']) ? number_format($customer['visits_count']) : 0; ?></td>
                                            <td><?php echo isset($customer['total_sales']) ? number_format($customer['total_sales'], 2) : '0.00'; ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

</div>

<!-- استدعاء قالب الفوتر -->
<?php include '../includes/templates/footer.php'; ?>

<!-- كود JavaScript للرسوم البيانية -->
<script>
    // رسم بياني للمبيعات
    $(document).ready(function() {
        const salesChartData = <?php echo $salesChartJson; ?>;

        const labels = salesChartData.map(item => item.day);
        const data = salesChartData.map(item => item.sales);

        const ctx = document.getElementById('salesChart').getContext('2d');
        const salesChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'المبيعات',
                    data: data,
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    borderColor: '#3498db',
                    borderWidth: 2,
                    pointBackgroundColor: '#3498db',
                    pointBorderColor: '#fff',
                    pointRadius: 5,
                    tension: 0.3,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.7)',
                        padding: 10,
                        titleFont: {
                            size: 14
                        },
                        bodyFont: {
                            size: 14
                        },
                        callbacks: {
                            label: function(context) {
                                return `المبيعات: ${context.raw} ر.س`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            font: {
                                size: 12
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 12
                            }
                        }
                    }
                }
            }
        });
    });
</script>
