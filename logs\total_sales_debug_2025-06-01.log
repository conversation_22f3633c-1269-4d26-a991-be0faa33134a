[2025-06-01 05:35:32] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:35:32] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:35:32] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:35:32] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:35:32] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:35:32] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:35:32] ربط المعلمة: :branch_id = 1
[2025-06-01 05:35:32] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:35:32] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:35:32] نتيجة الاستعلام: 805.00
[2025-06-01 05:35:32] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:35:32] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:35:32] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:35:32] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:35:32] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:35:32] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:35:32] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:35:32] ربط المعلمة: :branch_id = 1
[2025-06-01 05:35:32] ربط المعلمة: :payment_method = cash
[2025-06-01 05:35:32] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:35:32] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:35:32] نتيجة الاستعلام: 805.00
[2025-06-01 05:35:32] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:35:32] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:35:32] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:35:32] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:35:32] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:35:32] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:35:32] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:35:32] ربط المعلمة: :branch_id = 1
[2025-06-01 05:35:32] ربط المعلمة: :payment_method = card
[2025-06-01 05:35:32] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:35:32] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:35:32] نتيجة الاستعلام: 
[2025-06-01 05:35:32] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:35:32] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:35:32] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:35:32] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:35:32] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:35:32] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:35:32] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:35:32] ربط المعلمة: :branch_id = 1
[2025-06-01 05:35:32] ربط المعلمة: :payment_method = other
[2025-06-01 05:35:32] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:35:32] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:35:32] نتيجة الاستعلام: 
[2025-06-01 05:35:32] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:35:32] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:35:32] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:35:32] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:35:32] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:35:32] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:35:32] ربط المعلمة: :branch_id = 1
[2025-06-01 05:35:32] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:35:32] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:35:32] نتيجة الاستعلام: 805.00
[2025-06-01 05:35:32] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:35:32] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:35:32] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:35:32] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:35:32] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:35:32] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:35:32] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:35:32] ربط المعلمة: :branch_id = 1
[2025-06-01 05:35:32] ربط المعلمة: :payment_method = cash
[2025-06-01 05:35:32] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:35:32] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:35:32] نتيجة الاستعلام: 805.00
[2025-06-01 05:35:32] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:35:32] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:35:32] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:35:32] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:35:32] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:35:32] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:35:32] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:35:32] ربط المعلمة: :branch_id = 1
[2025-06-01 05:35:32] ربط المعلمة: :payment_method = card
[2025-06-01 05:35:32] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:35:32] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:35:32] نتيجة الاستعلام: 
[2025-06-01 05:35:32] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:35:32] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:35:32] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:35:32] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:35:32] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:35:32] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:35:32] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:35:32] ربط المعلمة: :branch_id = 1
[2025-06-01 05:35:32] ربط المعلمة: :payment_method = other
[2025-06-01 05:35:32] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:35:32] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:35:32] نتيجة الاستعلام: 
[2025-06-01 05:35:32] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1"}
[2025-06-01 05:37:14] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:37:14] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:37:14] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:37:14] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:37:14] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:37:14] ربط المعلمة: :branch_id = 1
[2025-06-01 05:37:14] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:37:14] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:37:14] نتيجة الاستعلام: 43945.00
[2025-06-01 05:37:14] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:37:14] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:37:14] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:37:14] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:37:14] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:37:14] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:37:14] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:37:14] ربط المعلمة: :branch_id = 1
[2025-06-01 05:37:14] ربط المعلمة: :payment_method = cash
[2025-06-01 05:37:14] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:37:14] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:37:14] نتيجة الاستعلام: 43945.00
[2025-06-01 05:37:14] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1","payment_method":"card"}
[2025-06-01 05:37:14] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:37:14] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:37:14] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:37:14] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:37:14] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:37:14] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:37:14] ربط المعلمة: :branch_id = 1
[2025-06-01 05:37:14] ربط المعلمة: :payment_method = card
[2025-06-01 05:37:14] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:37:14] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:37:14] نتيجة الاستعلام: 
[2025-06-01 05:37:14] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1","payment_method":"other"}
[2025-06-01 05:37:14] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:37:14] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:37:14] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:37:14] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:37:14] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:37:14] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:37:14] ربط المعلمة: :branch_id = 1
[2025-06-01 05:37:14] ربط المعلمة: :payment_method = other
[2025-06-01 05:37:14] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:37:14] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:37:14] نتيجة الاستعلام: 
[2025-06-01 05:37:14] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1"}
[2025-06-01 05:37:14] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:37:14] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:37:14] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:37:14] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:37:14] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:37:14] ربط المعلمة: :branch_id = 1
[2025-06-01 05:37:14] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:37:14] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:37:14] نتيجة الاستعلام: 43945.00
[2025-06-01 05:37:14] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:37:14] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:37:14] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:37:14] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:37:14] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:37:14] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:37:14] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:37:14] ربط المعلمة: :branch_id = 1
[2025-06-01 05:37:14] ربط المعلمة: :payment_method = cash
[2025-06-01 05:37:14] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:37:14] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:37:14] نتيجة الاستعلام: 43945.00
[2025-06-01 05:37:14] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1","payment_method":"card"}
[2025-06-01 05:37:14] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:37:14] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:37:14] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:37:14] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:37:14] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:37:14] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:37:14] ربط المعلمة: :branch_id = 1
[2025-06-01 05:37:14] ربط المعلمة: :payment_method = card
[2025-06-01 05:37:14] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:37:14] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:37:14] نتيجة الاستعلام: 
[2025-06-01 05:37:14] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1","payment_method":"other"}
[2025-06-01 05:37:14] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:37:14] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:37:14] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:37:14] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:37:14] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:37:14] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:37:14] ربط المعلمة: :branch_id = 1
[2025-06-01 05:37:14] ربط المعلمة: :payment_method = other
[2025-06-01 05:37:14] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:37:14] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:37:14] نتيجة الاستعلام: 
[2025-06-01 05:37:14] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1"}
[2025-06-01 05:40:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:55] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:40:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:55] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:40:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:55] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:40:55] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:40:55] نتيجة الاستعلام: 43945.00
[2025-06-01 05:40:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:40:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:55] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:40:55] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:40:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:55] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:40:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:55] ربط المعلمة: :payment_method = cash
[2025-06-01 05:40:55] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:40:55] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:40:55] نتيجة الاستعلام: 43945.00
[2025-06-01 05:40:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1","payment_method":"card"}
[2025-06-01 05:40:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:55] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:40:55] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:40:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:55] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:40:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:55] ربط المعلمة: :payment_method = card
[2025-06-01 05:40:55] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:40:55] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:40:55] نتيجة الاستعلام: 
[2025-06-01 05:40:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1","payment_method":"other"}
[2025-06-01 05:40:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:55] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:40:55] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:40:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:55] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:40:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:55] ربط المعلمة: :payment_method = other
[2025-06-01 05:40:55] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:40:55] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:40:55] نتيجة الاستعلام: 
[2025-06-01 05:40:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1"}
[2025-06-01 05:40:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:55] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:40:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:55] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:40:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:55] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:40:55] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:40:55] نتيجة الاستعلام: 43945.00
[2025-06-01 05:40:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:40:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:55] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:40:55] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:40:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:55] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:40:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:55] ربط المعلمة: :payment_method = cash
[2025-06-01 05:40:55] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:40:55] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:40:55] نتيجة الاستعلام: 43945.00
[2025-06-01 05:40:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1","payment_method":"card"}
[2025-06-01 05:40:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:55] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:40:55] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:40:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:55] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:40:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:55] ربط المعلمة: :payment_method = card
[2025-06-01 05:40:55] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:40:55] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:40:55] نتيجة الاستعلام: 
[2025-06-01 05:40:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1","payment_method":"other"}
[2025-06-01 05:40:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:55] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:40:55] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:40:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:55] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:40:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:55] ربط المعلمة: :payment_method = other
[2025-06-01 05:40:55] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:40:55] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:40:55] نتيجة الاستعلام: 
[2025-06-01 05:40:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:40:58] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:58] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:58] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:40:58] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:58] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:40:58] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:58] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:40:58] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:40:58] نتيجة الاستعلام: 805.00
[2025-06-01 05:40:58] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:40:58] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:58] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:58] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:40:58] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:40:58] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:58] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:40:58] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:58] ربط المعلمة: :payment_method = cash
[2025-06-01 05:40:58] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:40:58] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:40:58] نتيجة الاستعلام: 805.00
[2025-06-01 05:40:58] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:40:58] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:58] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:58] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:40:58] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:40:58] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:58] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:40:58] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:58] ربط المعلمة: :payment_method = card
[2025-06-01 05:40:58] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:40:58] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:40:58] نتيجة الاستعلام: 
[2025-06-01 05:40:58] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:40:58] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:58] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:58] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:40:58] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:40:58] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:58] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:40:58] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:58] ربط المعلمة: :payment_method = other
[2025-06-01 05:40:58] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:40:58] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:40:58] نتيجة الاستعلام: 
[2025-06-01 05:40:58] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:40:58] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:58] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:58] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:40:58] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:58] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:40:58] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:58] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:40:58] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:40:58] نتيجة الاستعلام: 805.00
[2025-06-01 05:40:58] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:40:58] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:58] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:58] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:40:58] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:40:58] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:58] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:40:58] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:58] ربط المعلمة: :payment_method = cash
[2025-06-01 05:40:58] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:40:58] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:40:58] نتيجة الاستعلام: 805.00
[2025-06-01 05:40:58] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:40:58] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:58] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:58] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:40:58] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:40:58] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:58] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:40:58] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:58] ربط المعلمة: :payment_method = card
[2025-06-01 05:40:58] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:40:58] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:40:58] نتيجة الاستعلام: 
[2025-06-01 05:40:58] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:40:58] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:58] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:58] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:40:58] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:40:58] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:58] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:40:58] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:58] ربط المعلمة: :payment_method = other
[2025-06-01 05:40:58] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:40:58] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:40:58] نتيجة الاستعلام: 
[2025-06-01 05:40:58] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:41:53] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:53] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:53] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:53] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:53] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:53] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:53] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:53] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:53] نتيجة الاستعلام: 805.00
[2025-06-01 05:41:53] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:41:53] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:53] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:53] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:41:53] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:53] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:53] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:53] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:53] ربط المعلمة: :payment_method = cash
[2025-06-01 05:41:53] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:53] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:53] نتيجة الاستعلام: 805.00
[2025-06-01 05:41:53] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:41:53] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:53] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:53] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:41:53] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:53] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:53] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:53] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:53] ربط المعلمة: :payment_method = card
[2025-06-01 05:41:53] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:53] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:53] نتيجة الاستعلام: 
[2025-06-01 05:41:53] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:41:53] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:53] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:53] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:41:53] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:53] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:53] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:53] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:53] ربط المعلمة: :payment_method = other
[2025-06-01 05:41:53] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:53] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:53] نتيجة الاستعلام: 
[2025-06-01 05:41:53] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:41:53] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:53] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:53] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:53] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:53] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:53] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:53] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:53] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:53] نتيجة الاستعلام: 805.00
[2025-06-01 05:41:53] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:41:53] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:53] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:53] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:41:53] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:53] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:53] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:53] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:53] ربط المعلمة: :payment_method = cash
[2025-06-01 05:41:53] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:53] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:53] نتيجة الاستعلام: 805.00
[2025-06-01 05:41:53] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:41:53] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:53] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:53] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:41:53] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:53] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:53] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:53] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:53] ربط المعلمة: :payment_method = card
[2025-06-01 05:41:53] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:53] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:53] نتيجة الاستعلام: 
[2025-06-01 05:41:53] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:41:53] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:53] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:53] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:41:53] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:53] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:53] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:53] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:53] ربط المعلمة: :payment_method = other
[2025-06-01 05:41:53] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:53] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:53] نتيجة الاستعلام: 
[2025-06-01 05:41:53] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:41:54] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:54] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:54] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:54] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:54] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:54] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:54] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:54] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:54] نتيجة الاستعلام: 805.00
[2025-06-01 05:41:54] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:41:54] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:54] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:54] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:41:54] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:54] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:54] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:54] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:54] ربط المعلمة: :payment_method = cash
[2025-06-01 05:41:54] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:54] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:54] نتيجة الاستعلام: 805.00
[2025-06-01 05:41:54] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:41:54] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:54] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:54] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:41:54] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:54] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:54] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:54] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:54] ربط المعلمة: :payment_method = card
[2025-06-01 05:41:54] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:54] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:54] نتيجة الاستعلام: 
[2025-06-01 05:41:54] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:41:54] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:54] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:54] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:41:54] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:54] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:54] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:54] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:54] ربط المعلمة: :payment_method = other
[2025-06-01 05:41:54] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:54] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:54] نتيجة الاستعلام: 
[2025-06-01 05:41:54] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:41:54] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:54] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:54] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:54] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:54] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:54] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:54] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:54] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:54] نتيجة الاستعلام: 805.00
[2025-06-01 05:41:54] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:41:54] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:54] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:54] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:41:54] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:54] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:54] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:54] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:54] ربط المعلمة: :payment_method = cash
[2025-06-01 05:41:54] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:54] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:54] نتيجة الاستعلام: 805.00
[2025-06-01 05:41:54] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:41:54] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:54] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:54] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:41:54] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:54] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:54] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:54] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:54] ربط المعلمة: :payment_method = card
[2025-06-01 05:41:54] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:54] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:54] نتيجة الاستعلام: 
[2025-06-01 05:41:54] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:41:54] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:54] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:54] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:41:54] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:54] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:54] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:54] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:54] ربط المعلمة: :payment_method = other
[2025-06-01 05:41:54] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:54] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:54] نتيجة الاستعلام: 
[2025-06-01 05:41:54] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:41:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:55] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:55] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:55] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:55] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:55] نتيجة الاستعلام: 805.00
[2025-06-01 05:41:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:41:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:55] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:41:55] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:55] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:55] ربط المعلمة: :payment_method = cash
[2025-06-01 05:41:55] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:55] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:55] نتيجة الاستعلام: 805.00
[2025-06-01 05:41:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:41:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:55] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:41:55] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:55] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:55] ربط المعلمة: :payment_method = card
[2025-06-01 05:41:55] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:55] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:55] نتيجة الاستعلام: 
[2025-06-01 05:41:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:41:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:55] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:41:55] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:55] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:55] ربط المعلمة: :payment_method = other
[2025-06-01 05:41:55] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:55] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:55] نتيجة الاستعلام: 
[2025-06-01 05:41:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:41:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:55] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:55] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:55] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:55] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:55] نتيجة الاستعلام: 805.00
[2025-06-01 05:41:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:41:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:55] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:41:55] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:55] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:55] ربط المعلمة: :payment_method = cash
[2025-06-01 05:41:55] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:55] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:55] نتيجة الاستعلام: 805.00
[2025-06-01 05:41:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:41:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:55] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:41:55] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:55] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:55] ربط المعلمة: :payment_method = card
[2025-06-01 05:41:55] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:55] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:55] نتيجة الاستعلام: 
[2025-06-01 05:41:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:41:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:55] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:41:55] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:55] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:55] ربط المعلمة: :payment_method = other
[2025-06-01 05:41:55] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:55] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:55] نتيجة الاستعلام: 
[2025-06-01 05:41:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:44:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:44:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:44:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:44:29] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:44:29] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:44:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:44:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:44:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:44:29] نتيجة الاستعلام: 805.00
[2025-06-01 05:44:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:44:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:44:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:44:29] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:44:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:44:29] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:44:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:44:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:44:29] ربط المعلمة: :payment_method = cash
[2025-06-01 05:44:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:44:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:44:29] نتيجة الاستعلام: 805.00
[2025-06-01 05:44:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:44:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:44:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:44:29] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:44:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:44:29] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:44:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:44:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:44:29] ربط المعلمة: :payment_method = card
[2025-06-01 05:44:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:44:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:44:29] نتيجة الاستعلام: 
[2025-06-01 05:44:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:44:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:44:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:44:29] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:44:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:44:29] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:44:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:44:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:44:29] ربط المعلمة: :payment_method = other
[2025-06-01 05:44:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:44:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:44:29] نتيجة الاستعلام: 
[2025-06-01 05:44:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:44:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:44:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:44:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:44:29] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:44:29] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:44:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:44:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:44:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:44:29] نتيجة الاستعلام: 805.00
[2025-06-01 05:44:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:44:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:44:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:44:29] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:44:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:44:29] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:44:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:44:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:44:29] ربط المعلمة: :payment_method = cash
[2025-06-01 05:44:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:44:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:44:29] نتيجة الاستعلام: 805.00
[2025-06-01 05:44:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:44:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:44:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:44:29] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:44:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:44:29] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:44:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:44:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:44:29] ربط المعلمة: :payment_method = card
[2025-06-01 05:44:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:44:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:44:29] نتيجة الاستعلام: 
[2025-06-01 05:44:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:44:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:44:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:44:29] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:44:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:44:29] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:44:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:44:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:44:29] ربط المعلمة: :payment_method = other
[2025-06-01 05:44:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:44:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:44:29] نتيجة الاستعلام: 
[2025-06-01 05:44:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 805.00
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :payment_method = cash
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 805.00
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :payment_method = card
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :payment_method = other
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 805.00
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :payment_method = cash
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 805.00
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :payment_method = card
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :payment_method = other
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 805.00
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :payment_method = cash
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 805.00
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :payment_method = card
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :payment_method = other
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 805.00
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :payment_method = cash
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 805.00
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :payment_method = card
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :payment_method = other
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:49:30] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:30] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:30] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:30] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:30] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:30] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:30] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:30] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:30] نتيجة الاستعلام: 805.00
[2025-06-01 05:49:30] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:49:30] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:30] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:30] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:49:30] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:30] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:30] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:30] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:30] ربط المعلمة: :payment_method = cash
[2025-06-01 05:49:30] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:30] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:30] نتيجة الاستعلام: 805.00
[2025-06-01 05:49:30] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:49:30] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:30] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:30] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:49:30] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:30] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:30] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:30] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:30] ربط المعلمة: :payment_method = card
[2025-06-01 05:49:30] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:30] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:30] نتيجة الاستعلام: 
[2025-06-01 05:49:30] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:49:30] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:30] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:30] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:49:30] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:30] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:30] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:30] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:30] ربط المعلمة: :payment_method = other
[2025-06-01 05:49:30] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:30] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:30] نتيجة الاستعلام: 
[2025-06-01 05:49:30] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:49:30] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:30] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:30] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:30] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:30] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:30] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:30] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:30] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:30] نتيجة الاستعلام: 805.00
[2025-06-01 05:49:30] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:49:30] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:30] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:30] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:49:30] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:30] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:30] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:30] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:30] ربط المعلمة: :payment_method = cash
[2025-06-01 05:49:30] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:30] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:30] نتيجة الاستعلام: 805.00
[2025-06-01 05:49:30] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:49:30] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:30] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:30] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:49:30] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:30] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:30] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:30] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:30] ربط المعلمة: :payment_method = card
[2025-06-01 05:49:30] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:30] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:30] نتيجة الاستعلام: 
[2025-06-01 05:49:30] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:49:30] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:30] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:30] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:49:30] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:30] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:30] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:30] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:30] ربط المعلمة: :payment_method = other
[2025-06-01 05:49:30] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:30] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:30] نتيجة الاستعلام: 
[2025-06-01 05:49:30] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:35] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:35] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:49:35] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:35] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:35] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:35] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:49:35] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01",":end_date":"2025-06-30"}
[2025-06-01 05:49:35] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:35] ربط المعلمة: :start_date = 2025-06-01
[2025-06-01 05:49:35] ربط المعلمة: :end_date = 2025-06-30
[2025-06-01 05:49:35] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:49:35] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:35] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:49:35] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:35] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:35] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:35] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:49:35] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01",":end_date":"2025-06-30"}
[2025-06-01 05:49:35] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:35] ربط المعلمة: :start_date = 2025-06-01
[2025-06-01 05:49:35] ربط المعلمة: :end_date = 2025-06-30
[2025-06-01 05:49:35] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:49:36] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:36] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:49:36] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:36] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:36] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:36] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:49:36] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01",":end_date":"2025-06-30"}
[2025-06-01 05:49:36] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:36] ربط المعلمة: :start_date = 2025-06-01
[2025-06-01 05:49:36] ربط المعلمة: :end_date = 2025-06-30
[2025-06-01 05:49:36] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:49:36] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:36] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:49:36] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:36] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:36] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:36] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:49:36] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01",":end_date":"2025-06-30"}
[2025-06-01 05:49:36] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:36] ربط المعلمة: :start_date = 2025-06-01
[2025-06-01 05:49:36] ربط المعلمة: :end_date = 2025-06-30
[2025-06-01 05:49:36] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:49:43] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:43] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:49:43] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:43] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:43] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:43] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:49:43] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01",":end_date":"2025-06-30"}
[2025-06-01 05:49:43] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:43] ربط المعلمة: :start_date = 2025-06-01
[2025-06-01 05:49:43] ربط المعلمة: :end_date = 2025-06-30
[2025-06-01 05:49:43] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:49:43] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:43] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:49:43] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:43] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:43] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:43] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:49:43] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01",":end_date":"2025-06-30"}
[2025-06-01 05:49:43] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:43] ربط المعلمة: :start_date = 2025-06-01
[2025-06-01 05:49:43] ربط المعلمة: :end_date = 2025-06-30
[2025-06-01 05:49:43] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:49:46] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:46] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:49:46] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:46] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:46] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:46] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:49:46] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01",":end_date":"2025-06-30"}
[2025-06-01 05:49:46] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:46] ربط المعلمة: :start_date = 2025-06-01
[2025-06-01 05:49:46] ربط المعلمة: :end_date = 2025-06-30
[2025-06-01 05:49:46] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:49:46] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:46] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:49:46] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:46] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:46] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:46] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:49:46] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01",":end_date":"2025-06-30"}
[2025-06-01 05:49:46] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:46] ربط المعلمة: :start_date = 2025-06-01
[2025-06-01 05:49:46] ربط المعلمة: :end_date = 2025-06-30
[2025-06-01 05:49:46] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:50:03] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:50:03] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:50:03] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:50:03] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:50:03] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:50:03] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:50:03] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01",":end_date":"2025-06-30"}
[2025-06-01 05:50:03] ربط المعلمة: :branch_id = 1
[2025-06-01 05:50:03] ربط المعلمة: :start_date = 2025-06-01
[2025-06-01 05:50:03] ربط المعلمة: :end_date = 2025-06-30
[2025-06-01 05:50:03] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:50:03] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:50:03] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:50:03] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:50:03] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:50:03] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:50:03] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:50:03] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01",":end_date":"2025-06-30"}
[2025-06-01 05:50:03] ربط المعلمة: :branch_id = 1
[2025-06-01 05:50:03] ربط المعلمة: :start_date = 2025-06-01
[2025-06-01 05:50:03] ربط المعلمة: :end_date = 2025-06-30
[2025-06-01 05:50:03] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:50:09] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:50:09] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:50:09] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:50:09] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:50:09] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:50:09] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:50:09] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01",":end_date":"2025-06-30"}
[2025-06-01 05:50:09] ربط المعلمة: :branch_id = 1
[2025-06-01 05:50:09] ربط المعلمة: :start_date = 2025-06-01
[2025-06-01 05:50:09] ربط المعلمة: :end_date = 2025-06-30
[2025-06-01 05:50:09] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:50:09] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:50:09] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:50:09] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:50:09] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:50:09] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:50:09] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:50:09] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01",":end_date":"2025-06-30"}
[2025-06-01 05:50:09] ربط المعلمة: :branch_id = 1
[2025-06-01 05:50:09] ربط المعلمة: :start_date = 2025-06-01
[2025-06-01 05:50:09] ربط المعلمة: :end_date = 2025-06-30
[2025-06-01 05:50:09] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:54:44] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:54:44] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:54:44] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:54:44] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:54:44] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:54:44] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:54:44] المعلمات المرتبطة: {":branch_id":1,":start_date":"2025-06-01",":end_date":"2025-06-30"}
[2025-06-01 05:54:44] ربط المعلمة: :branch_id = 1
[2025-06-01 05:54:44] ربط المعلمة: :start_date = 2025-06-01
[2025-06-01 05:54:44] ربط المعلمة: :end_date = 2025-06-30
[2025-06-01 05:54:44] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:54:44] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:54:44] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 05:54:44] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:54:44] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:54:44] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 05:54:44] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:54:44] المعلمات المرتبطة: {":branch_id":1,":start_date":"2025-05-01",":end_date":"2025-05-31"}
[2025-06-01 05:54:44] ربط المعلمة: :branch_id = 1
[2025-06-01 05:54:44] ربط المعلمة: :start_date = 2025-05-01
[2025-06-01 05:54:44] ربط المعلمة: :end_date = 2025-05-31
[2025-06-01 05:54:44] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:54:44] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:54:44] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:54:44] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:54:44] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:54:44] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:54:44] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:54:44] المعلمات المرتبطة: {":branch_id":1,":start_date":"2025-06-01",":end_date":"2025-06-30"}
[2025-06-01 05:54:44] ربط المعلمة: :branch_id = 1
[2025-06-01 05:54:44] ربط المعلمة: :start_date = 2025-06-01
[2025-06-01 05:54:44] ربط المعلمة: :end_date = 2025-06-30
[2025-06-01 05:54:44] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:55:50] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:55:50] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:55:50] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:55:50] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:55:50] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:55:50] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:55:50] المعلمات المرتبطة: {":branch_id":1,":date_start":"2025-06-01",":date_end":"2025-06-30"}
[2025-06-01 05:55:50] ربط المعلمة: :branch_id = 1
[2025-06-01 05:55:50] ربط المعلمة: :date_start = 2025-06-01
[2025-06-01 05:55:50] ربط المعلمة: :date_end = 2025-06-30
[2025-06-01 05:55:50] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:55:50] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:55:50] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 05:55:50] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:55:50] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:55:50] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 05:55:50] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:55:50] المعلمات المرتبطة: {":branch_id":1,":date_start":"2025-05-01",":date_end":"2025-05-31"}
[2025-06-01 05:55:50] ربط المعلمة: :branch_id = 1
[2025-06-01 05:55:50] ربط المعلمة: :date_start = 2025-05-01
[2025-06-01 05:55:50] ربط المعلمة: :date_end = 2025-05-31
[2025-06-01 05:55:50] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:55:50] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:55:50] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:55:50] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:55:50] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:55:50] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:55:50] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:55:50] المعلمات المرتبطة: {":branch_id":1,":date_start":"2025-06-01",":date_end":"2025-06-30"}
[2025-06-01 05:55:50] ربط المعلمة: :branch_id = 1
[2025-06-01 05:55:50] ربط المعلمة: :date_start = 2025-06-01
[2025-06-01 05:55:50] ربط المعلمة: :date_end = 2025-06-30
[2025-06-01 05:55:50] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:55:52] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:55:52] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:55:52] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:55:52] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:55:52] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:55:52] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:55:52] المعلمات المرتبطة: {":branch_id":1,":date_start":"2025-06-01",":date_end":"2025-06-30"}
[2025-06-01 05:55:52] ربط المعلمة: :branch_id = 1
[2025-06-01 05:55:52] ربط المعلمة: :date_start = 2025-06-01
[2025-06-01 05:55:52] ربط المعلمة: :date_end = 2025-06-30
[2025-06-01 05:55:52] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:55:52] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:55:52] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 05:55:52] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:55:52] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:55:52] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 05:55:52] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:55:52] المعلمات المرتبطة: {":branch_id":1,":date_start":"2025-05-01",":date_end":"2025-05-31"}
[2025-06-01 05:55:52] ربط المعلمة: :branch_id = 1
[2025-06-01 05:55:52] ربط المعلمة: :date_start = 2025-05-01
[2025-06-01 05:55:52] ربط المعلمة: :date_end = 2025-05-31
[2025-06-01 05:55:52] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:55:52] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:55:52] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:55:52] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:55:52] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:55:52] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:55:52] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:55:52] المعلمات المرتبطة: {":branch_id":1,":date_start":"2025-06-01",":date_end":"2025-06-30"}
[2025-06-01 05:55:52] ربط المعلمة: :branch_id = 1
[2025-06-01 05:55:52] ربط المعلمة: :date_start = 2025-06-01
[2025-06-01 05:55:52] ربط المعلمة: :date_end = 2025-06-30
[2025-06-01 05:55:52] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:55:53] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:55:53] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:55:53] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:55:53] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:55:53] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:55:53] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:55:53] المعلمات المرتبطة: {":branch_id":1,":date_start":"2025-06-01",":date_end":"2025-06-30"}
[2025-06-01 05:55:53] ربط المعلمة: :branch_id = 1
[2025-06-01 05:55:53] ربط المعلمة: :date_start = 2025-06-01
[2025-06-01 05:55:53] ربط المعلمة: :date_end = 2025-06-30
[2025-06-01 05:55:53] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:55:53] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:55:53] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 05:55:53] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:55:53] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:55:53] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 05:55:53] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:55:53] المعلمات المرتبطة: {":branch_id":1,":date_start":"2025-05-01",":date_end":"2025-05-31"}
[2025-06-01 05:55:53] ربط المعلمة: :branch_id = 1
[2025-06-01 05:55:53] ربط المعلمة: :date_start = 2025-05-01
[2025-06-01 05:55:53] ربط المعلمة: :date_end = 2025-05-31
[2025-06-01 05:55:53] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:55:53] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:55:53] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:55:53] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:55:53] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:55:53] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:55:53] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:55:53] المعلمات المرتبطة: {":branch_id":1,":date_start":"2025-06-01",":date_end":"2025-06-30"}
[2025-06-01 05:55:53] ربط المعلمة: :branch_id = 1
[2025-06-01 05:55:53] ربط المعلمة: :date_start = 2025-06-01
[2025-06-01 05:55:53] ربط المعلمة: :date_end = 2025-06-30
[2025-06-01 05:55:53] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:55:59] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:55:59] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:55:59] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:55:59] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:55:59] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:55:59] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:55:59] المعلمات المرتبطة: {":branch_id":1,":date_start":"2025-06-01",":date_end":"2025-06-30"}
[2025-06-01 05:55:59] ربط المعلمة: :branch_id = 1
[2025-06-01 05:55:59] ربط المعلمة: :date_start = 2025-06-01
[2025-06-01 05:55:59] ربط المعلمة: :date_end = 2025-06-30
[2025-06-01 05:55:59] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:55:59] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:55:59] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 05:55:59] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:55:59] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:55:59] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 05:55:59] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:55:59] المعلمات المرتبطة: {":branch_id":1,":date_start":"2025-05-01",":date_end":"2025-05-31"}
[2025-06-01 05:55:59] ربط المعلمة: :branch_id = 1
[2025-06-01 05:55:59] ربط المعلمة: :date_start = 2025-05-01
[2025-06-01 05:55:59] ربط المعلمة: :date_end = 2025-05-31
[2025-06-01 05:55:59] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:55:59] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:55:59] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:55:59] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:55:59] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:55:59] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:55:59] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:55:59] المعلمات المرتبطة: {":branch_id":1,":date_start":"2025-06-01",":date_end":"2025-06-30"}
[2025-06-01 05:55:59] ربط المعلمة: :branch_id = 1
[2025-06-01 05:55:59] ربط المعلمة: :date_start = 2025-06-01
[2025-06-01 05:55:59] ربط المعلمة: :date_end = 2025-06-30
[2025-06-01 05:55:59] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:56:31] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:56:31] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:56:31] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:56:31] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:56:31] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:56:31] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:56:31] المعلمات المرتبطة: {":branch_id":1,":date_start":"2025-06-01",":date_end":"2025-06-30"}
[2025-06-01 05:56:31] ربط المعلمة: :branch_id = 1
[2025-06-01 05:56:31] ربط المعلمة: :date_start = 2025-06-01
[2025-06-01 05:56:31] ربط المعلمة: :date_end = 2025-06-30
[2025-06-01 05:56:31] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:56:31] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:56:31] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 05:56:31] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:56:31] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:56:31] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 05:56:31] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:56:31] المعلمات المرتبطة: {":branch_id":1,":date_start":"2025-05-01",":date_end":"2025-05-31"}
[2025-06-01 05:56:31] ربط المعلمة: :branch_id = 1
[2025-06-01 05:56:31] ربط المعلمة: :date_start = 2025-05-01
[2025-06-01 05:56:31] ربط المعلمة: :date_end = 2025-05-31
[2025-06-01 05:56:31] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:56:31] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:56:31] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:56:31] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:56:31] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:56:31] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:56:31] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:56:31] المعلمات المرتبطة: {":branch_id":1,":date_start":"2025-06-01",":date_end":"2025-06-30"}
[2025-06-01 05:56:31] ربط المعلمة: :branch_id = 1
[2025-06-01 05:56:31] ربط المعلمة: :date_start = 2025-06-01
[2025-06-01 05:56:31] ربط المعلمة: :date_end = 2025-06-30
[2025-06-01 05:56:31] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:37] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:57:37] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:57:37] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:57:37] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:57:37] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:57:37] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:57:37] المعلمات المرتبطة: {":branch_id":1,":date_start":"2025-06-01",":date_end":"2025-06-30"}
[2025-06-01 05:57:37] ربط المعلمة: :branch_id = 1
[2025-06-01 05:57:37] ربط المعلمة: :date_start = 2025-06-01
[2025-06-01 05:57:37] ربط المعلمة: :date_end = 2025-06-30
[2025-06-01 05:57:37] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:37] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:57:37] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 05:57:37] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:57:37] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:57:37] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 05:57:37] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:57:37] المعلمات المرتبطة: {":branch_id":1,":date_start":"2025-05-01",":date_end":"2025-05-31"}
[2025-06-01 05:57:37] ربط المعلمة: :branch_id = 1
[2025-06-01 05:57:37] ربط المعلمة: :date_start = 2025-05-01
[2025-06-01 05:57:37] ربط المعلمة: :date_end = 2025-05-31
[2025-06-01 05:57:37] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:37] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:57:37] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:57:37] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:57:37] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:57:37] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:57:37] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:57:37] المعلمات المرتبطة: {":branch_id":1,":date_start":"2025-06-01",":date_end":"2025-06-30"}
[2025-06-01 05:57:37] ربط المعلمة: :branch_id = 1
[2025-06-01 05:57:37] ربط المعلمة: :date_start = 2025-06-01
[2025-06-01 05:57:37] ربط المعلمة: :date_end = 2025-06-30
[2025-06-01 05:57:37] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:38] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:57:38] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:57:38] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:57:38] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:57:38] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:57:38] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:57:38] المعلمات المرتبطة: {":branch_id":1,":date_start":"2025-06-01",":date_end":"2025-06-30"}
[2025-06-01 05:57:38] ربط المعلمة: :branch_id = 1
[2025-06-01 05:57:38] ربط المعلمة: :date_start = 2025-06-01
[2025-06-01 05:57:38] ربط المعلمة: :date_end = 2025-06-30
[2025-06-01 05:57:38] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:38] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:57:38] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 05:57:38] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:57:38] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:57:38] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 05:57:38] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:57:38] المعلمات المرتبطة: {":branch_id":1,":date_start":"2025-05-01",":date_end":"2025-05-31"}
[2025-06-01 05:57:38] ربط المعلمة: :branch_id = 1
[2025-06-01 05:57:38] ربط المعلمة: :date_start = 2025-05-01
[2025-06-01 05:57:38] ربط المعلمة: :date_end = 2025-05-31
[2025-06-01 05:57:38] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:38] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:57:38] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:57:38] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:57:38] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:57:38] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:57:38] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:57:38] المعلمات المرتبطة: {":branch_id":1,":date_start":"2025-06-01",":date_end":"2025-06-30"}
[2025-06-01 05:57:38] ربط المعلمة: :branch_id = 1
[2025-06-01 05:57:38] ربط المعلمة: :date_start = 2025-06-01
[2025-06-01 05:57:38] ربط المعلمة: :date_end = 2025-06-30
[2025-06-01 05:57:38] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:51] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:57:51] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:57:51] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:57:51] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:57:51] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:57:51] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:51] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 05:57:51] ربط المعلمة: :branch_id = 1
[2025-06-01 05:57:51] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 05:57:51] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 05:57:51] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:51] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:57:51] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 05:57:51] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:57:51] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:57:51] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 05:57:51] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:51] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-05-01",":sales_end_date":"2025-05-31"}
[2025-06-01 05:57:51] ربط المعلمة: :branch_id = 1
[2025-06-01 05:57:51] ربط المعلمة: :sales_start_date = 2025-05-01
[2025-06-01 05:57:51] ربط المعلمة: :sales_end_date = 2025-05-31
[2025-06-01 05:57:51] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:51] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:57:51] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:57:51] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:57:51] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:57:51] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:57:51] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:51] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 05:57:51] ربط المعلمة: :branch_id = 1
[2025-06-01 05:57:51] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 05:57:51] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 05:57:51] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:53] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:57:53] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:57:53] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:57:53] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:57:53] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:57:53] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:53] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 05:57:53] ربط المعلمة: :branch_id = 1
[2025-06-01 05:57:53] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 05:57:53] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 05:57:53] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:53] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:57:53] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 05:57:53] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:57:53] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:57:53] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 05:57:53] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:53] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-05-01",":sales_end_date":"2025-05-31"}
[2025-06-01 05:57:53] ربط المعلمة: :branch_id = 1
[2025-06-01 05:57:53] ربط المعلمة: :sales_start_date = 2025-05-01
[2025-06-01 05:57:53] ربط المعلمة: :sales_end_date = 2025-05-31
[2025-06-01 05:57:53] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:53] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:57:53] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:57:53] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:57:53] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:57:53] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:57:53] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:53] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 05:57:53] ربط المعلمة: :branch_id = 1
[2025-06-01 05:57:53] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 05:57:53] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 05:57:53] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:57:55] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:57:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:57:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:57:55] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:57:55] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:55] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 05:57:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:57:55] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 05:57:55] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 05:57:55] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:57:55] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 05:57:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:57:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:57:55] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 05:57:55] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:55] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-05-01",":sales_end_date":"2025-05-31"}
[2025-06-01 05:57:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:57:55] ربط المعلمة: :sales_start_date = 2025-05-01
[2025-06-01 05:57:55] ربط المعلمة: :sales_end_date = 2025-05-31
[2025-06-01 05:57:55] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:57:55] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:57:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:57:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:57:55] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:57:55] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:55] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 05:57:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:57:55] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 05:57:55] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 05:57:55] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:56] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:57:56] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:57:56] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:57:56] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:57:56] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:57:56] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:56] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 05:57:56] ربط المعلمة: :branch_id = 1
[2025-06-01 05:57:56] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 05:57:56] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 05:57:56] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:56] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:57:56] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 05:57:56] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:57:56] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:57:56] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 05:57:56] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:56] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-05-01",":sales_end_date":"2025-05-31"}
[2025-06-01 05:57:56] ربط المعلمة: :branch_id = 1
[2025-06-01 05:57:56] ربط المعلمة: :sales_start_date = 2025-05-01
[2025-06-01 05:57:56] ربط المعلمة: :sales_end_date = 2025-05-31
[2025-06-01 05:57:56] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:56] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:57:56] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:57:56] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:57:56] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:57:56] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:57:56] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:56] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 05:57:56] ربط المعلمة: :branch_id = 1
[2025-06-01 05:57:56] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 05:57:56] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 05:57:56] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:59] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:57:59] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:57:59] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:57:59] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:57:59] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:57:59] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:59] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 05:57:59] ربط المعلمة: :branch_id = 1
[2025-06-01 05:57:59] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 05:57:59] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 05:57:59] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:59] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:57:59] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 05:57:59] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:57:59] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:57:59] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 05:57:59] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:59] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-05-01",":sales_end_date":"2025-05-31"}
[2025-06-01 05:57:59] ربط المعلمة: :branch_id = 1
[2025-06-01 05:57:59] ربط المعلمة: :sales_start_date = 2025-05-01
[2025-06-01 05:57:59] ربط المعلمة: :sales_end_date = 2025-05-31
[2025-06-01 05:57:59] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:59] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:57:59] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:57:59] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:57:59] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:57:59] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:57:59] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:59] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 05:57:59] ربط المعلمة: :branch_id = 1
[2025-06-01 05:57:59] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 05:57:59] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 05:57:59] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:02] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:58:02] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:58:02] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:58:02] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:58:02] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:58:02] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:02] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 05:58:02] ربط المعلمة: :branch_id = 1
[2025-06-01 05:58:02] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 05:58:02] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 05:58:02] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:02] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:58:02] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 05:58:02] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:58:02] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:58:02] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 05:58:02] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:02] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-05-01",":sales_end_date":"2025-05-31"}
[2025-06-01 05:58:02] ربط المعلمة: :branch_id = 1
[2025-06-01 05:58:02] ربط المعلمة: :sales_start_date = 2025-05-01
[2025-06-01 05:58:02] ربط المعلمة: :sales_end_date = 2025-05-31
[2025-06-01 05:58:02] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:02] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:58:02] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:58:02] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:58:02] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:58:02] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:58:02] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:02] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 05:58:02] ربط المعلمة: :branch_id = 1
[2025-06-01 05:58:02] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 05:58:02] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 05:58:02] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:03] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:58:03] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:58:03] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:58:03] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:58:03] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:58:03] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:03] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 05:58:03] ربط المعلمة: :branch_id = 1
[2025-06-01 05:58:03] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 05:58:03] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 05:58:03] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:03] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:58:03] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 05:58:03] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:58:03] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:58:03] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 05:58:03] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:03] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-05-01",":sales_end_date":"2025-05-31"}
[2025-06-01 05:58:03] ربط المعلمة: :branch_id = 1
[2025-06-01 05:58:03] ربط المعلمة: :sales_start_date = 2025-05-01
[2025-06-01 05:58:03] ربط المعلمة: :sales_end_date = 2025-05-31
[2025-06-01 05:58:03] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:03] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:58:03] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:58:03] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:58:03] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:58:03] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:58:03] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:03] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 05:58:03] ربط المعلمة: :branch_id = 1
[2025-06-01 05:58:03] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 05:58:03] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 05:58:03] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:04] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:58:04] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:58:04] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:58:04] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:58:04] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:58:04] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:04] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 05:58:04] ربط المعلمة: :branch_id = 1
[2025-06-01 05:58:04] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 05:58:04] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 05:58:04] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:04] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:58:04] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 05:58:04] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:58:04] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:58:04] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 05:58:04] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:04] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-05-01",":sales_end_date":"2025-05-31"}
[2025-06-01 05:58:04] ربط المعلمة: :branch_id = 1
[2025-06-01 05:58:04] ربط المعلمة: :sales_start_date = 2025-05-01
[2025-06-01 05:58:04] ربط المعلمة: :sales_end_date = 2025-05-31
[2025-06-01 05:58:04] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:04] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:58:04] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:58:04] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:58:04] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:58:04] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:58:04] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:04] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 05:58:04] ربط المعلمة: :branch_id = 1
[2025-06-01 05:58:04] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 05:58:04] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 05:58:04] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:05] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:58:05] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:58:05] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:58:05] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:58:05] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:58:05] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:05] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 05:58:05] ربط المعلمة: :branch_id = 1
[2025-06-01 05:58:05] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 05:58:05] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 05:58:05] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:05] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:58:05] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 05:58:05] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:58:05] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:58:05] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 05:58:05] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:05] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-05-01",":sales_end_date":"2025-05-31"}
[2025-06-01 05:58:05] ربط المعلمة: :branch_id = 1
[2025-06-01 05:58:05] ربط المعلمة: :sales_start_date = 2025-05-01
[2025-06-01 05:58:05] ربط المعلمة: :sales_end_date = 2025-05-31
[2025-06-01 05:58:05] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:05] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:58:05] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:58:05] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:58:05] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:58:05] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:58:05] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:05] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 05:58:05] ربط المعلمة: :branch_id = 1
[2025-06-01 05:58:05] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 05:58:05] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 05:58:05] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:06] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:58:06] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:58:06] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:58:06] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:58:06] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:58:06] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:06] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 05:58:06] ربط المعلمة: :branch_id = 1
[2025-06-01 05:58:06] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 05:58:06] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 05:58:06] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:06] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:58:06] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 05:58:06] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:58:06] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:58:06] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 05:58:06] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:06] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-05-01",":sales_end_date":"2025-05-31"}
[2025-06-01 05:58:06] ربط المعلمة: :branch_id = 1
[2025-06-01 05:58:06] ربط المعلمة: :sales_start_date = 2025-05-01
[2025-06-01 05:58:06] ربط المعلمة: :sales_end_date = 2025-05-31
[2025-06-01 05:58:06] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:06] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:58:06] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:58:06] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:58:06] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:58:06] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:58:06] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:06] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 05:58:06] ربط المعلمة: :branch_id = 1
[2025-06-01 05:58:06] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 05:58:06] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 05:58:06] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:39] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:58:39] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:58:39] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:58:39] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:58:39] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:58:39] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:39] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 05:58:39] ربط المعلمة: :branch_id = 1
[2025-06-01 05:58:39] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 05:58:39] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 05:58:39] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:39] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:58:39] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 05:58:39] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:58:39] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:58:39] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 05:58:39] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:39] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-05-01",":sales_end_date":"2025-05-31"}
[2025-06-01 05:58:39] ربط المعلمة: :branch_id = 1
[2025-06-01 05:58:39] ربط المعلمة: :sales_start_date = 2025-05-01
[2025-06-01 05:58:39] ربط المعلمة: :sales_end_date = 2025-05-31
[2025-06-01 05:58:39] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:39] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:58:39] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 05:58:39] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:58:39] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:58:39] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:58:39] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:39] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 05:58:39] ربط المعلمة: :branch_id = 1
[2025-06-01 05:58:39] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 05:58:39] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 05:58:39] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:00:07] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:00:07] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 06:00:07] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:00:07] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:00:07] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 06:00:07] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:00:07] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-05-01",":sales_end_date":"2025-05-31"}
[2025-06-01 06:00:07] ربط المعلمة: :branch_id = 1
[2025-06-01 06:00:07] ربط المعلمة: :sales_start_date = 2025-05-01
[2025-06-01 06:00:07] ربط المعلمة: :sales_end_date = 2025-05-31
[2025-06-01 06:00:07] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:00:07] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:00:07] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 06:00:07] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:00:07] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:00:07] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:00:07] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:00:07] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 06:00:07] ربط المعلمة: :branch_id = 1
[2025-06-01 06:00:07] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 06:00:07] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 06:00:07] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:00:09] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:00:09] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 06:00:09] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:00:09] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:00:09] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 06:00:09] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:00:09] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-05-01",":sales_end_date":"2025-05-31"}
[2025-06-01 06:00:09] ربط المعلمة: :branch_id = 1
[2025-06-01 06:00:09] ربط المعلمة: :sales_start_date = 2025-05-01
[2025-06-01 06:00:09] ربط المعلمة: :sales_end_date = 2025-05-31
[2025-06-01 06:00:09] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:00:09] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:00:09] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 06:00:09] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:00:09] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:00:09] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:00:09] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:00:09] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 06:00:09] ربط المعلمة: :branch_id = 1
[2025-06-01 06:00:09] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 06:00:09] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 06:00:09] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:00:15] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:00:15] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 06:00:15] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:00:15] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:00:15] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 06:00:15] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:00:15] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-05-01",":sales_end_date":"2025-05-31"}
[2025-06-01 06:00:15] ربط المعلمة: :branch_id = 1
[2025-06-01 06:00:15] ربط المعلمة: :sales_start_date = 2025-05-01
[2025-06-01 06:00:15] ربط المعلمة: :sales_end_date = 2025-05-31
[2025-06-01 06:00:15] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:00:15] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:00:15] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 06:00:15] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:00:15] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:00:15] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:00:15] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:00:15] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 06:00:15] ربط المعلمة: :branch_id = 1
[2025-06-01 06:00:15] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 06:00:15] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 06:00:15] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:00:16] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:00:16] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 06:00:16] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:00:16] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:00:16] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 06:00:16] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:00:16] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-05-01",":sales_end_date":"2025-05-31"}
[2025-06-01 06:00:16] ربط المعلمة: :branch_id = 1
[2025-06-01 06:00:16] ربط المعلمة: :sales_start_date = 2025-05-01
[2025-06-01 06:00:16] ربط المعلمة: :sales_end_date = 2025-05-31
[2025-06-01 06:00:16] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:00:16] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:00:16] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 06:00:16] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:00:16] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:00:16] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:00:16] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:00:16] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 06:00:16] ربط المعلمة: :branch_id = 1
[2025-06-01 06:00:16] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 06:00:16] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 06:00:16] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:00:54] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:00:54] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 06:00:54] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:00:54] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:00:54] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 06:00:54] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:00:54] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-05-01",":sales_end_date":"2025-05-31"}
[2025-06-01 06:00:54] ربط المعلمة: :branch_id = 1
[2025-06-01 06:00:54] ربط المعلمة: :sales_start_date = 2025-05-01
[2025-06-01 06:00:54] ربط المعلمة: :sales_end_date = 2025-05-31
[2025-06-01 06:00:54] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:00:54] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:00:54] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 06:00:54] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:00:54] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:00:54] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:00:54] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:00:54] المعلمات المرتبطة: {":branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 06:00:54] ربط المعلمة: :branch_id = 1
[2025-06-01 06:00:54] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 06:00:54] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 06:00:54] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:01:27] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:01:27] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 06:01:27] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:01:27] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:01:27] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 06:01:27] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:01:27] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date":"2025-05-01",":sales_end_date":"2025-05-31"}
[2025-06-01 06:01:27] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:01:27] ربط المعلمة: :sales_start_date = 2025-05-01
[2025-06-01 06:01:27] ربط المعلمة: :sales_end_date = 2025-05-31
[2025-06-01 06:01:27] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:01:27] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:01:27] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 06:01:27] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:01:27] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:01:27] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:01:27] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:01:27] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 06:01:27] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:01:27] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 06:01:27] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 06:01:27] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:01:31] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:01:31] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 06:01:31] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:01:31] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:01:31] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 06:01:31] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:01:31] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date":"2025-05-01",":sales_end_date":"2025-05-31"}
[2025-06-01 06:01:31] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:01:31] ربط المعلمة: :sales_start_date = 2025-05-01
[2025-06-01 06:01:31] ربط المعلمة: :sales_end_date = 2025-05-31
[2025-06-01 06:01:31] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:01:31] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:01:31] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 06:01:31] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:01:31] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:01:31] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:01:31] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:01:31] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 06:01:31] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:01:31] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 06:01:31] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 06:01:31] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:01:47] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:01:47] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 06:01:47] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:01:47] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:01:47] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 06:01:47] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:01:47] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date":"2025-05-01",":sales_end_date":"2025-05-31"}
[2025-06-01 06:01:47] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:01:47] ربط المعلمة: :sales_start_date = 2025-05-01
[2025-06-01 06:01:47] ربط المعلمة: :sales_end_date = 2025-05-31
[2025-06-01 06:01:47] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:01:47] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:01:47] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 06:01:47] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:01:47] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:01:47] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:01:47] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:01:47] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 06:01:47] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:01:47] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 06:01:47] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 06:01:47] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:01:57] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:01:57] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 06:01:57] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:01:57] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:01:57] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 06:01:57] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:01:57] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date":"2025-05-01",":sales_end_date":"2025-05-31"}
[2025-06-01 06:01:57] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:01:57] ربط المعلمة: :sales_start_date = 2025-05-01
[2025-06-01 06:01:57] ربط المعلمة: :sales_end_date = 2025-05-31
[2025-06-01 06:01:57] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:01:57] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:01:57] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 06:01:57] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:01:57] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:01:57] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:01:57] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:01:57] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 06:01:57] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:01:57] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 06:01:57] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 06:01:57] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:01:58] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:01:58] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 06:01:58] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:01:58] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:01:58] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 06:01:58] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:01:58] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date":"2025-05-01",":sales_end_date":"2025-05-31"}
[2025-06-01 06:01:58] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:01:58] ربط المعلمة: :sales_start_date = 2025-05-01
[2025-06-01 06:01:58] ربط المعلمة: :sales_end_date = 2025-05-31
[2025-06-01 06:01:58] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:01:58] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:01:58] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 06:01:58] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:01:58] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:01:58] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:01:58] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:01:58] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 06:01:58] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:01:58] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 06:01:58] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 06:01:58] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:02:02] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:02:02] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 06:02:02] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:02:02] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:02:02] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 06:02:02] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:02:02] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date":"2025-05-01",":sales_end_date":"2025-05-31"}
[2025-06-01 06:02:02] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:02:02] ربط المعلمة: :sales_start_date = 2025-05-01
[2025-06-01 06:02:02] ربط المعلمة: :sales_end_date = 2025-05-31
[2025-06-01 06:02:02] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:02:02] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:02:02] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 06:02:02] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:02:02] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:02:02] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:02:02] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:02:02] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 06:02:02] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:02:02] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 06:02:02] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 06:02:02] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:02:07] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:02:07] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 06:02:07] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:02:07] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:02:07] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 06:02:07] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:02:07] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date":"2025-05-01",":sales_end_date":"2025-05-31"}
[2025-06-01 06:02:07] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:02:07] ربط المعلمة: :sales_start_date = 2025-05-01
[2025-06-01 06:02:07] ربط المعلمة: :sales_end_date = 2025-05-31
[2025-06-01 06:02:07] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:02:07] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:02:07] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 06:02:07] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:02:07] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:02:07] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:02:07] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:02:07] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 06:02:07] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:02:07] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 06:02:07] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 06:02:07] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:02:11] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:02:11] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 06:02:11] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:02:11] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:02:11] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 06:02:11] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:02:11] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date":"2025-05-01",":sales_end_date":"2025-05-31"}
[2025-06-01 06:02:11] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:02:11] ربط المعلمة: :sales_start_date = 2025-05-01
[2025-06-01 06:02:11] ربط المعلمة: :sales_end_date = 2025-05-31
[2025-06-01 06:02:11] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:02:11] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:02:11] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 06:02:11] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:02:11] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:02:11] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:02:11] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:02:11] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 06:02:11] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:02:11] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 06:02:11] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 06:02:11] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:02:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:02:29] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 06:02:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:02:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:02:29] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 06:02:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:02:29] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date":"2025-05-01",":sales_end_date":"2025-05-31"}
[2025-06-01 06:02:29] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:02:29] ربط المعلمة: :sales_start_date = 2025-05-01
[2025-06-01 06:02:29] ربط المعلمة: :sales_end_date = 2025-05-31
[2025-06-01 06:02:29] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:02:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:02:29] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 06:02:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:02:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:02:29] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:02:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:02:29] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 06:02:29] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:02:29] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 06:02:29] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 06:02:29] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:03:17] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:03:17] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 06:03:17] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:03:17] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:03:17] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 06:03:17] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:03:17] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date":"2025-05-01",":sales_end_date":"2025-05-31"}
[2025-06-01 06:03:17] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:03:17] ربط المعلمة: :sales_start_date = 2025-05-01
[2025-06-01 06:03:17] ربط المعلمة: :sales_end_date = 2025-05-31
[2025-06-01 06:03:17] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:03:17] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:03:17] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 06:03:17] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:03:17] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:03:17] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:03:17] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:03:17] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 06:03:17] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:03:17] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 06:03:17] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 06:03:17] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:03:23] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:03:23] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 06:03:23] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:03:23] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:03:23] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 06:03:23] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:03:23] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date":"2025-05-01",":sales_end_date":"2025-05-31"}
[2025-06-01 06:03:23] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:03:23] ربط المعلمة: :sales_start_date = 2025-05-01
[2025-06-01 06:03:23] ربط المعلمة: :sales_end_date = 2025-05-31
[2025-06-01 06:03:23] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:03:23] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:03:23] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 06:03:23] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:03:23] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:03:23] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:03:23] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:03:23] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 06:03:23] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:03:23] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 06:03:23] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 06:03:23] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:03:51] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:03:51] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 06:03:51] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:03:51] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:03:51] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:03:51] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:03:51] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 06:03:51] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:03:51] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 06:03:51] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 06:03:51] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:03:51] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:03:51] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 06:03:51] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:03:51] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:03:51] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 06:03:51] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:03:51] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date":"2025-05-01",":sales_end_date":"2025-05-31"}
[2025-06-01 06:03:51] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:03:51] ربط المعلمة: :sales_start_date = 2025-05-01
[2025-06-01 06:03:51] ربط المعلمة: :sales_end_date = 2025-05-31
[2025-06-01 06:03:51] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:03:51] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:03:51] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 06:03:51] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:03:51] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:03:51] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:03:51] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:03:51] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date":"2025-06-01",":sales_end_date":"2025-06-30"}
[2025-06-01 06:03:51] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:03:51] ربط المعلمة: :sales_start_date = 2025-06-01
[2025-06-01 06:03:51] ربط المعلمة: :sales_end_date = 2025-06-30
[2025-06-01 06:03:51] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:04:03] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:04:03] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 06:04:03] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:04:03] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:04:03] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:04:03] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:04:03] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date1":"2025-06-01",":sales_end_date1":"2025-06-30",":sales_start_date2":"2025-06-01",":sales_end_date2":"2025-06-30"}
[2025-06-01 06:04:03] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:04:03] ربط المعلمة: :sales_start_date1 = 2025-06-01
[2025-06-01 06:04:03] ربط المعلمة: :sales_end_date1 = 2025-06-30
[2025-06-01 06:04:03] ربط المعلمة: :sales_start_date2 = 2025-06-01
[2025-06-01 06:04:03] ربط المعلمة: :sales_end_date2 = 2025-06-30
[2025-06-01 06:04:03] نتيجة الاستعلام: 
[2025-06-01 06:04:03] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:04:03] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:04:03] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 06:04:03] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:04:03] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:04:03] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 06:04:03] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:04:03] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date1":"2025-05-01",":sales_end_date1":"2025-05-31",":sales_start_date2":"2025-05-01",":sales_end_date2":"2025-05-31"}
[2025-06-01 06:04:03] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:04:03] ربط المعلمة: :sales_start_date1 = 2025-05-01
[2025-06-01 06:04:03] ربط المعلمة: :sales_end_date1 = 2025-05-31
[2025-06-01 06:04:03] ربط المعلمة: :sales_start_date2 = 2025-05-01
[2025-06-01 06:04:03] ربط المعلمة: :sales_end_date2 = 2025-05-31
[2025-06-01 06:04:03] نتيجة الاستعلام: 43805.00
[2025-06-01 06:04:03] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:04:03] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:04:03] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 06:04:03] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:04:03] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:04:03] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:04:03] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:04:03] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date1":"2025-06-01",":sales_end_date1":"2025-06-30",":sales_start_date2":"2025-06-01",":sales_end_date2":"2025-06-30"}
[2025-06-01 06:04:03] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:04:03] ربط المعلمة: :sales_start_date1 = 2025-06-01
[2025-06-01 06:04:03] ربط المعلمة: :sales_end_date1 = 2025-06-30
[2025-06-01 06:04:03] ربط المعلمة: :sales_start_date2 = 2025-06-01
[2025-06-01 06:04:03] ربط المعلمة: :sales_end_date2 = 2025-06-30
[2025-06-01 06:04:03] نتيجة الاستعلام: 
[2025-06-01 06:04:03] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:04:13] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:04:13] الفلاتر المستخدمة: {"branch_id":1,"payment_status":"paid","end_day_id":20250476}
[2025-06-01 06:04:13] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:04:13] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:04:13] إضافة فلتر نهاية اليوم: i.end_day_id = 20250476
[2025-06-01 06:04:13] تم تحديد حالة الدفع في الفلاتر: paid
[2025-06-01 06:04:13] تمت إزالة شرط الدفع الافتراضي
[2025-06-01 06:04:13] إضافة شرط حالة الدفع الجديد: i.payment_status = paid
[2025-06-01 06:04:13] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.branch_id = :sales_branch_id AND i.end_day_id = :sales_end_day_id AND i.payment_status = :sales_payment_status
[2025-06-01 06:04:13] المعلمات المرتبطة: {":sales_branch_id":1,":sales_end_day_id":20250476,":sales_payment_status":"paid"}
[2025-06-01 06:04:13] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:04:13] ربط المعلمة: :sales_end_day_id = 20250476
[2025-06-01 06:04:13] ربط المعلمة: :sales_payment_status = paid
[2025-06-01 06:04:13] نتيجة الاستعلام: 805.00
[2025-06-01 06:04:13] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:04:13] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:04:13] الفلاتر المستخدمة: {"branch_id":1,"payment_status":"paid","end_day_id":20250476}
[2025-06-01 06:04:13] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:04:13] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:04:13] إضافة فلتر نهاية اليوم: i.end_day_id = 20250476
[2025-06-01 06:04:13] تم تحديد حالة الدفع في الفلاتر: paid
[2025-06-01 06:04:13] تمت إزالة شرط الدفع الافتراضي
[2025-06-01 06:04:13] إضافة شرط حالة الدفع الجديد: i.payment_status = paid
[2025-06-01 06:04:13] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.branch_id = :sales_branch_id AND i.end_day_id = :sales_end_day_id AND i.payment_status = :sales_payment_status
[2025-06-01 06:04:13] المعلمات المرتبطة: {":sales_branch_id":1,":sales_end_day_id":20250476,":sales_payment_status":"paid"}
[2025-06-01 06:04:13] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:04:13] ربط المعلمة: :sales_end_day_id = 20250476
[2025-06-01 06:04:13] ربط المعلمة: :sales_payment_status = paid
[2025-06-01 06:04:13] نتيجة الاستعلام: 805.00
[2025-06-01 06:04:13] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:04:13] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:04:13] الفلاتر المستخدمة: {"branch_id":1,"payment_status":"paid","end_day_id":20250476}
[2025-06-01 06:04:13] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:04:13] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:04:13] إضافة فلتر نهاية اليوم: i.end_day_id = 20250476
[2025-06-01 06:04:13] تم تحديد حالة الدفع في الفلاتر: paid
[2025-06-01 06:04:13] تمت إزالة شرط الدفع الافتراضي
[2025-06-01 06:04:13] إضافة شرط حالة الدفع الجديد: i.payment_status = paid
[2025-06-01 06:04:13] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.branch_id = :sales_branch_id AND i.end_day_id = :sales_end_day_id AND i.payment_status = :sales_payment_status
[2025-06-01 06:04:13] المعلمات المرتبطة: {":sales_branch_id":1,":sales_end_day_id":20250476,":sales_payment_status":"paid"}
[2025-06-01 06:04:13] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:04:13] ربط المعلمة: :sales_end_day_id = 20250476
[2025-06-01 06:04:13] ربط المعلمة: :sales_payment_status = paid
[2025-06-01 06:04:13] نتيجة الاستعلام: 805.00
[2025-06-01 06:04:13] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:04:13] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:04:13] الفلاتر المستخدمة: {"start_date":"2025-05-26","end_date":"2025-05-26","branch_id":1}
[2025-06-01 06:04:13] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:04:13] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:04:13] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-26 إلى 2025-05-26
[2025-06-01 06:04:13] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:04:13] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date1":"2025-05-26",":sales_end_date1":"2025-05-26",":sales_start_date2":"2025-05-26",":sales_end_date2":"2025-05-26"}
[2025-06-01 06:04:13] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:04:13] ربط المعلمة: :sales_start_date1 = 2025-05-26
[2025-06-01 06:04:13] ربط المعلمة: :sales_end_date1 = 2025-05-26
[2025-06-01 06:04:13] ربط المعلمة: :sales_start_date2 = 2025-05-26
[2025-06-01 06:04:13] ربط المعلمة: :sales_end_date2 = 2025-05-26
[2025-06-01 06:04:13] نتيجة الاستعلام: 580.00
[2025-06-01 06:04:13] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:04:13] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:04:13] الفلاتر المستخدمة: {"start_date":"2025-05-27","end_date":"2025-05-27","branch_id":1}
[2025-06-01 06:04:13] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:04:13] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:04:13] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-27 إلى 2025-05-27
[2025-06-01 06:04:13] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:04:13] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date1":"2025-05-27",":sales_end_date1":"2025-05-27",":sales_start_date2":"2025-05-27",":sales_end_date2":"2025-05-27"}
[2025-06-01 06:04:13] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:04:13] ربط المعلمة: :sales_start_date1 = 2025-05-27
[2025-06-01 06:04:13] ربط المعلمة: :sales_end_date1 = 2025-05-27
[2025-06-01 06:04:13] ربط المعلمة: :sales_start_date2 = 2025-05-27
[2025-06-01 06:04:13] ربط المعلمة: :sales_end_date2 = 2025-05-27
[2025-06-01 06:04:13] نتيجة الاستعلام: 1425.00
[2025-06-01 06:04:13] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:04:13] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:04:13] الفلاتر المستخدمة: {"start_date":"2025-05-28","end_date":"2025-05-28","branch_id":1}
[2025-06-01 06:04:13] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:04:13] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:04:13] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-28 إلى 2025-05-28
[2025-06-01 06:04:13] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:04:13] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date1":"2025-05-28",":sales_end_date1":"2025-05-28",":sales_start_date2":"2025-05-28",":sales_end_date2":"2025-05-28"}
[2025-06-01 06:04:13] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:04:13] ربط المعلمة: :sales_start_date1 = 2025-05-28
[2025-06-01 06:04:13] ربط المعلمة: :sales_end_date1 = 2025-05-28
[2025-06-01 06:04:13] ربط المعلمة: :sales_start_date2 = 2025-05-28
[2025-06-01 06:04:13] ربط المعلمة: :sales_end_date2 = 2025-05-28
[2025-06-01 06:04:13] نتيجة الاستعلام: 2280.00
[2025-06-01 06:04:13] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:04:13] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:04:13] الفلاتر المستخدمة: {"start_date":"2025-05-29","end_date":"2025-05-29","branch_id":1}
[2025-06-01 06:04:13] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:04:13] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:04:13] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-29 إلى 2025-05-29
[2025-06-01 06:04:13] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:04:13] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date1":"2025-05-29",":sales_end_date1":"2025-05-29",":sales_start_date2":"2025-05-29",":sales_end_date2":"2025-05-29"}
[2025-06-01 06:04:13] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:04:13] ربط المعلمة: :sales_start_date1 = 2025-05-29
[2025-06-01 06:04:13] ربط المعلمة: :sales_end_date1 = 2025-05-29
[2025-06-01 06:04:13] ربط المعلمة: :sales_start_date2 = 2025-05-29
[2025-06-01 06:04:13] ربط المعلمة: :sales_end_date2 = 2025-05-29
[2025-06-01 06:04:13] نتيجة الاستعلام: 1185.00
[2025-06-01 06:04:13] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:04:13] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:04:13] الفلاتر المستخدمة: {"start_date":"2025-05-30","end_date":"2025-05-30","branch_id":1}
[2025-06-01 06:04:13] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:04:13] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:04:13] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-30 إلى 2025-05-30
[2025-06-01 06:04:13] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:04:13] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date1":"2025-05-30",":sales_end_date1":"2025-05-30",":sales_start_date2":"2025-05-30",":sales_end_date2":"2025-05-30"}
[2025-06-01 06:04:13] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:04:13] ربط المعلمة: :sales_start_date1 = 2025-05-30
[2025-06-01 06:04:13] ربط المعلمة: :sales_end_date1 = 2025-05-30
[2025-06-01 06:04:13] ربط المعلمة: :sales_start_date2 = 2025-05-30
[2025-06-01 06:04:13] ربط المعلمة: :sales_end_date2 = 2025-05-30
[2025-06-01 06:04:13] نتيجة الاستعلام: 1465.00
[2025-06-01 06:04:13] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:04:13] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:04:13] الفلاتر المستخدمة: {"start_date":"2025-05-31","end_date":"2025-05-31","branch_id":1}
[2025-06-01 06:04:13] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:04:13] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:04:13] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-31 إلى 2025-05-31
[2025-06-01 06:04:13] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:04:13] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date1":"2025-05-31",":sales_end_date1":"2025-05-31",":sales_start_date2":"2025-05-31",":sales_end_date2":"2025-05-31"}
[2025-06-01 06:04:13] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:04:13] ربط المعلمة: :sales_start_date1 = 2025-05-31
[2025-06-01 06:04:13] ربط المعلمة: :sales_end_date1 = 2025-05-31
[2025-06-01 06:04:13] ربط المعلمة: :sales_start_date2 = 2025-05-31
[2025-06-01 06:04:13] ربط المعلمة: :sales_end_date2 = 2025-05-31
[2025-06-01 06:04:13] نتيجة الاستعلام: 805.00
[2025-06-01 06:04:13] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:04:13] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:04:13] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-01","branch_id":1}
[2025-06-01 06:04:13] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:04:13] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:04:13] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-01
[2025-06-01 06:04:13] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:04:13] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date1":"2025-06-01",":sales_end_date1":"2025-06-01",":sales_start_date2":"2025-06-01",":sales_end_date2":"2025-06-01"}
[2025-06-01 06:04:13] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:04:13] ربط المعلمة: :sales_start_date1 = 2025-06-01
[2025-06-01 06:04:13] ربط المعلمة: :sales_end_date1 = 2025-06-01
[2025-06-01 06:04:13] ربط المعلمة: :sales_start_date2 = 2025-06-01
[2025-06-01 06:04:13] ربط المعلمة: :sales_end_date2 = 2025-06-01
[2025-06-01 06:04:13] نتيجة الاستعلام: 
[2025-06-01 06:04:13] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:04:34] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:04:34] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 06:04:34] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:04:34] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:04:34] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:04:34] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:04:34] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date1":"2025-06-01",":sales_end_date1":"2025-06-30",":sales_start_date2":"2025-06-01",":sales_end_date2":"2025-06-30"}
[2025-06-01 06:04:34] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:04:34] ربط المعلمة: :sales_start_date1 = 2025-06-01
[2025-06-01 06:04:34] ربط المعلمة: :sales_end_date1 = 2025-06-30
[2025-06-01 06:04:34] ربط المعلمة: :sales_start_date2 = 2025-06-01
[2025-06-01 06:04:34] ربط المعلمة: :sales_end_date2 = 2025-06-30
[2025-06-01 06:04:34] نتيجة الاستعلام: 
[2025-06-01 06:04:34] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:04:34] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:04:34] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 06:04:34] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:04:34] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:04:34] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 06:04:34] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:04:34] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date1":"2025-05-01",":sales_end_date1":"2025-05-31",":sales_start_date2":"2025-05-01",":sales_end_date2":"2025-05-31"}
[2025-06-01 06:04:34] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:04:34] ربط المعلمة: :sales_start_date1 = 2025-05-01
[2025-06-01 06:04:34] ربط المعلمة: :sales_end_date1 = 2025-05-31
[2025-06-01 06:04:34] ربط المعلمة: :sales_start_date2 = 2025-05-01
[2025-06-01 06:04:34] ربط المعلمة: :sales_end_date2 = 2025-05-31
[2025-06-01 06:04:34] نتيجة الاستعلام: 43805.00
[2025-06-01 06:04:34] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:04:34] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:04:34] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 06:04:34] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:04:34] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:04:34] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:04:34] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:04:34] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date1":"2025-06-01",":sales_end_date1":"2025-06-30",":sales_start_date2":"2025-06-01",":sales_end_date2":"2025-06-30"}
[2025-06-01 06:04:34] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:04:34] ربط المعلمة: :sales_start_date1 = 2025-06-01
[2025-06-01 06:04:34] ربط المعلمة: :sales_end_date1 = 2025-06-30
[2025-06-01 06:04:34] ربط المعلمة: :sales_start_date2 = 2025-06-01
[2025-06-01 06:04:34] ربط المعلمة: :sales_end_date2 = 2025-06-30
[2025-06-01 06:04:34] نتيجة الاستعلام: 
[2025-06-01 06:04:34] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:06:16] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:06:16] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 06:06:16] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:06:16] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:06:16] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:06:16] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:06:16] المعلمات المرتبطة: {":sales_branch_id":"1",":sales_start_date1":"2025-06-01",":sales_end_date1":"2025-06-30",":sales_start_date2":"2025-06-01",":sales_end_date2":"2025-06-30"}
[2025-06-01 06:06:16] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:06:16] ربط المعلمة: :sales_start_date1 = 2025-06-01
[2025-06-01 06:06:16] ربط المعلمة: :sales_end_date1 = 2025-06-30
[2025-06-01 06:06:16] ربط المعلمة: :sales_start_date2 = 2025-06-01
[2025-06-01 06:06:16] ربط المعلمة: :sales_end_date2 = 2025-06-30
[2025-06-01 06:06:16] نتيجة الاستعلام: 
[2025-06-01 06:06:16] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:06:16] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:06:16] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 06:06:16] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:06:16] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:06:16] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 06:06:16] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:06:16] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:06:16] المعلمات المرتبطة: {":sales_branch_id":"1",":sales_payment_method":"cash",":sales_start_date1":"2025-06-01",":sales_end_date1":"2025-06-30",":sales_start_date2":"2025-06-01",":sales_end_date2":"2025-06-30"}
[2025-06-01 06:06:16] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:06:16] ربط المعلمة: :sales_payment_method = cash
[2025-06-01 06:06:16] ربط المعلمة: :sales_start_date1 = 2025-06-01
[2025-06-01 06:06:16] ربط المعلمة: :sales_end_date1 = 2025-06-30
[2025-06-01 06:06:16] ربط المعلمة: :sales_start_date2 = 2025-06-01
[2025-06-01 06:06:16] ربط المعلمة: :sales_end_date2 = 2025-06-30
[2025-06-01 06:06:16] نتيجة الاستعلام: 
[2025-06-01 06:06:16] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:06:16] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:06:16] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 06:06:16] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:06:16] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:06:16] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 06:06:16] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:06:16] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:06:16] المعلمات المرتبطة: {":sales_branch_id":"1",":sales_payment_method":"card",":sales_start_date1":"2025-06-01",":sales_end_date1":"2025-06-30",":sales_start_date2":"2025-06-01",":sales_end_date2":"2025-06-30"}
[2025-06-01 06:06:16] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:06:16] ربط المعلمة: :sales_payment_method = card
[2025-06-01 06:06:16] ربط المعلمة: :sales_start_date1 = 2025-06-01
[2025-06-01 06:06:16] ربط المعلمة: :sales_end_date1 = 2025-06-30
[2025-06-01 06:06:16] ربط المعلمة: :sales_start_date2 = 2025-06-01
[2025-06-01 06:06:16] ربط المعلمة: :sales_end_date2 = 2025-06-30
[2025-06-01 06:06:16] نتيجة الاستعلام: 
[2025-06-01 06:06:16] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:06:16] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:06:16] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 06:06:16] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:06:16] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:06:16] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 06:06:16] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:06:16] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:06:16] المعلمات المرتبطة: {":sales_branch_id":"1",":sales_payment_method":"other",":sales_start_date1":"2025-06-01",":sales_end_date1":"2025-06-30",":sales_start_date2":"2025-06-01",":sales_end_date2":"2025-06-30"}
[2025-06-01 06:06:16] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:06:16] ربط المعلمة: :sales_payment_method = other
[2025-06-01 06:06:16] ربط المعلمة: :sales_start_date1 = 2025-06-01
[2025-06-01 06:06:16] ربط المعلمة: :sales_end_date1 = 2025-06-30
[2025-06-01 06:06:16] ربط المعلمة: :sales_start_date2 = 2025-06-01
[2025-06-01 06:06:16] ربط المعلمة: :sales_end_date2 = 2025-06-30
[2025-06-01 06:06:16] نتيجة الاستعلام: 
[2025-06-01 06:06:16] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:06:16] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:06:16] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 06:06:16] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:06:16] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:06:16] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:06:16] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:06:16] المعلمات المرتبطة: {":sales_branch_id":"1",":sales_start_date1":"2025-06-01",":sales_end_date1":"2025-06-30",":sales_start_date2":"2025-06-01",":sales_end_date2":"2025-06-30"}
[2025-06-01 06:06:16] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:06:16] ربط المعلمة: :sales_start_date1 = 2025-06-01
[2025-06-01 06:06:16] ربط المعلمة: :sales_end_date1 = 2025-06-30
[2025-06-01 06:06:16] ربط المعلمة: :sales_start_date2 = 2025-06-01
[2025-06-01 06:06:16] ربط المعلمة: :sales_end_date2 = 2025-06-30
[2025-06-01 06:06:16] نتيجة الاستعلام: 
[2025-06-01 06:06:16] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:06:16] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:06:16] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 06:06:16] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:06:16] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:06:16] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 06:06:16] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:06:16] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:06:16] المعلمات المرتبطة: {":sales_branch_id":"1",":sales_payment_method":"cash",":sales_start_date1":"2025-06-01",":sales_end_date1":"2025-06-30",":sales_start_date2":"2025-06-01",":sales_end_date2":"2025-06-30"}
[2025-06-01 06:06:16] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:06:16] ربط المعلمة: :sales_payment_method = cash
[2025-06-01 06:06:16] ربط المعلمة: :sales_start_date1 = 2025-06-01
[2025-06-01 06:06:16] ربط المعلمة: :sales_end_date1 = 2025-06-30
[2025-06-01 06:06:16] ربط المعلمة: :sales_start_date2 = 2025-06-01
[2025-06-01 06:06:16] ربط المعلمة: :sales_end_date2 = 2025-06-30
[2025-06-01 06:06:16] نتيجة الاستعلام: 
[2025-06-01 06:06:16] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:06:16] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:06:16] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 06:06:16] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:06:16] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:06:16] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 06:06:16] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:06:16] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:06:16] المعلمات المرتبطة: {":sales_branch_id":"1",":sales_payment_method":"card",":sales_start_date1":"2025-06-01",":sales_end_date1":"2025-06-30",":sales_start_date2":"2025-06-01",":sales_end_date2":"2025-06-30"}
[2025-06-01 06:06:16] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:06:16] ربط المعلمة: :sales_payment_method = card
[2025-06-01 06:06:16] ربط المعلمة: :sales_start_date1 = 2025-06-01
[2025-06-01 06:06:16] ربط المعلمة: :sales_end_date1 = 2025-06-30
[2025-06-01 06:06:16] ربط المعلمة: :sales_start_date2 = 2025-06-01
[2025-06-01 06:06:16] ربط المعلمة: :sales_end_date2 = 2025-06-30
[2025-06-01 06:06:16] نتيجة الاستعلام: 
[2025-06-01 06:06:16] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:06:16] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:06:16] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 06:06:16] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:06:16] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:06:16] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 06:06:16] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:06:16] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:06:16] المعلمات المرتبطة: {":sales_branch_id":"1",":sales_payment_method":"other",":sales_start_date1":"2025-06-01",":sales_end_date1":"2025-06-30",":sales_start_date2":"2025-06-01",":sales_end_date2":"2025-06-30"}
[2025-06-01 06:06:16] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:06:16] ربط المعلمة: :sales_payment_method = other
[2025-06-01 06:06:16] ربط المعلمة: :sales_start_date1 = 2025-06-01
[2025-06-01 06:06:16] ربط المعلمة: :sales_end_date1 = 2025-06-30
[2025-06-01 06:06:16] ربط المعلمة: :sales_start_date2 = 2025-06-01
[2025-06-01 06:06:16] ربط المعلمة: :sales_end_date2 = 2025-06-30
[2025-06-01 06:06:16] نتيجة الاستعلام: 
[2025-06-01 06:06:16] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:06:22] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:06:22] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 06:06:22] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:06:22] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:06:22] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:06:22] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:06:22] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date1":"2025-06-01",":sales_end_date1":"2025-06-30",":sales_start_date2":"2025-06-01",":sales_end_date2":"2025-06-30"}
[2025-06-01 06:06:22] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:06:22] ربط المعلمة: :sales_start_date1 = 2025-06-01
[2025-06-01 06:06:22] ربط المعلمة: :sales_end_date1 = 2025-06-30
[2025-06-01 06:06:22] ربط المعلمة: :sales_start_date2 = 2025-06-01
[2025-06-01 06:06:22] ربط المعلمة: :sales_end_date2 = 2025-06-30
[2025-06-01 06:06:22] نتيجة الاستعلام: 
[2025-06-01 06:06:22] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:06:22] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:06:22] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","branch_id":1}
[2025-06-01 06:06:22] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:06:22] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:06:22] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 06:06:22] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:06:22] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date1":"2025-05-01",":sales_end_date1":"2025-05-31",":sales_start_date2":"2025-05-01",":sales_end_date2":"2025-05-31"}
[2025-06-01 06:06:22] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:06:22] ربط المعلمة: :sales_start_date1 = 2025-05-01
[2025-06-01 06:06:22] ربط المعلمة: :sales_end_date1 = 2025-05-31
[2025-06-01 06:06:22] ربط المعلمة: :sales_start_date2 = 2025-05-01
[2025-06-01 06:06:22] ربط المعلمة: :sales_end_date2 = 2025-05-31
[2025-06-01 06:06:22] نتيجة الاستعلام: 43805.00
[2025-06-01 06:06:22] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:06:22] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:06:22] الفلاتر المستخدمة: {"start_date":"2025-06-01","end_date":"2025-06-30","branch_id":1}
[2025-06-01 06:06:22] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:06:22] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:06:22] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:06:22] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:06:22] المعلمات المرتبطة: {":sales_branch_id":1,":sales_start_date1":"2025-06-01",":sales_end_date1":"2025-06-30",":sales_start_date2":"2025-06-01",":sales_end_date2":"2025-06-30"}
[2025-06-01 06:06:22] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:06:22] ربط المعلمة: :sales_start_date1 = 2025-06-01
[2025-06-01 06:06:22] ربط المعلمة: :sales_end_date1 = 2025-06-30
[2025-06-01 06:06:22] ربط المعلمة: :sales_start_date2 = 2025-06-01
[2025-06-01 06:06:22] ربط المعلمة: :sales_end_date2 = 2025-06-30
[2025-06-01 06:06:22] نتيجة الاستعلام: 
[2025-06-01 06:06:22] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:06:30] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:06:30] الفلاتر المستخدمة: {"date_range":"last_month","start_date":"2025-05-01","end_date":"2025-05-31","branch_id":"1"}
[2025-06-01 06:06:30] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:06:30] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:06:30] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 06:06:30] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:06:30] المعلمات المرتبطة: {":sales_branch_id":"1",":sales_start_date1":"2025-05-01",":sales_end_date1":"2025-05-31",":sales_start_date2":"2025-05-01",":sales_end_date2":"2025-05-31"}
[2025-06-01 06:06:30] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:06:30] ربط المعلمة: :sales_start_date1 = 2025-05-01
[2025-06-01 06:06:30] ربط المعلمة: :sales_end_date1 = 2025-05-31
[2025-06-01 06:06:30] ربط المعلمة: :sales_start_date2 = 2025-05-01
[2025-06-01 06:06:30] ربط المعلمة: :sales_end_date2 = 2025-05-31
[2025-06-01 06:06:30] نتيجة الاستعلام: 43805.00
[2025-06-01 06:06:30] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:06:30] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:06:30] الفلاتر المستخدمة: {"date_range":"last_month","start_date":"2025-05-01","end_date":"2025-05-31","branch_id":"1","payment_method":"cash"}
[2025-06-01 06:06:30] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:06:30] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:06:30] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 06:06:30] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 06:06:30] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:06:30] المعلمات المرتبطة: {":sales_branch_id":"1",":sales_payment_method":"cash",":sales_start_date1":"2025-05-01",":sales_end_date1":"2025-05-31",":sales_start_date2":"2025-05-01",":sales_end_date2":"2025-05-31"}
[2025-06-01 06:06:30] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:06:30] ربط المعلمة: :sales_payment_method = cash
[2025-06-01 06:06:30] ربط المعلمة: :sales_start_date1 = 2025-05-01
[2025-06-01 06:06:30] ربط المعلمة: :sales_end_date1 = 2025-05-31
[2025-06-01 06:06:30] ربط المعلمة: :sales_start_date2 = 2025-05-01
[2025-06-01 06:06:30] ربط المعلمة: :sales_end_date2 = 2025-05-31
[2025-06-01 06:06:30] نتيجة الاستعلام: 43805.00
[2025-06-01 06:06:30] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:06:30] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:06:30] الفلاتر المستخدمة: {"date_range":"last_month","start_date":"2025-05-01","end_date":"2025-05-31","branch_id":"1","payment_method":"card"}
[2025-06-01 06:06:30] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:06:30] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:06:30] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 06:06:30] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 06:06:30] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:06:30] المعلمات المرتبطة: {":sales_branch_id":"1",":sales_payment_method":"card",":sales_start_date1":"2025-05-01",":sales_end_date1":"2025-05-31",":sales_start_date2":"2025-05-01",":sales_end_date2":"2025-05-31"}
[2025-06-01 06:06:30] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:06:30] ربط المعلمة: :sales_payment_method = card
[2025-06-01 06:06:30] ربط المعلمة: :sales_start_date1 = 2025-05-01
[2025-06-01 06:06:30] ربط المعلمة: :sales_end_date1 = 2025-05-31
[2025-06-01 06:06:30] ربط المعلمة: :sales_start_date2 = 2025-05-01
[2025-06-01 06:06:30] ربط المعلمة: :sales_end_date2 = 2025-05-31
[2025-06-01 06:06:30] نتيجة الاستعلام: 
[2025-06-01 06:06:30] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:06:30] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:06:30] الفلاتر المستخدمة: {"date_range":"last_month","start_date":"2025-05-01","end_date":"2025-05-31","branch_id":"1","payment_method":"other"}
[2025-06-01 06:06:30] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:06:30] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:06:30] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 06:06:30] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 06:06:30] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:06:30] المعلمات المرتبطة: {":sales_branch_id":"1",":sales_payment_method":"other",":sales_start_date1":"2025-05-01",":sales_end_date1":"2025-05-31",":sales_start_date2":"2025-05-01",":sales_end_date2":"2025-05-31"}
[2025-06-01 06:06:30] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:06:30] ربط المعلمة: :sales_payment_method = other
[2025-06-01 06:06:30] ربط المعلمة: :sales_start_date1 = 2025-05-01
[2025-06-01 06:06:30] ربط المعلمة: :sales_end_date1 = 2025-05-31
[2025-06-01 06:06:30] ربط المعلمة: :sales_start_date2 = 2025-05-01
[2025-06-01 06:06:30] ربط المعلمة: :sales_end_date2 = 2025-05-31
[2025-06-01 06:06:30] نتيجة الاستعلام: 
[2025-06-01 06:06:30] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:06:30] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:06:30] الفلاتر المستخدمة: {"date_range":"last_month","start_date":"2025-05-01","end_date":"2025-05-31","branch_id":"1"}
[2025-06-01 06:06:30] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:06:30] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:06:30] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 06:06:30] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:06:30] المعلمات المرتبطة: {":sales_branch_id":"1",":sales_start_date1":"2025-05-01",":sales_end_date1":"2025-05-31",":sales_start_date2":"2025-05-01",":sales_end_date2":"2025-05-31"}
[2025-06-01 06:06:30] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:06:30] ربط المعلمة: :sales_start_date1 = 2025-05-01
[2025-06-01 06:06:30] ربط المعلمة: :sales_end_date1 = 2025-05-31
[2025-06-01 06:06:30] ربط المعلمة: :sales_start_date2 = 2025-05-01
[2025-06-01 06:06:30] ربط المعلمة: :sales_end_date2 = 2025-05-31
[2025-06-01 06:06:30] نتيجة الاستعلام: 43805.00
[2025-06-01 06:06:30] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:06:30] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:06:30] الفلاتر المستخدمة: {"date_range":"last_month","start_date":"2025-05-01","end_date":"2025-05-31","branch_id":"1","payment_method":"cash"}
[2025-06-01 06:06:30] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:06:30] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:06:30] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 06:06:30] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 06:06:30] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:06:30] المعلمات المرتبطة: {":sales_branch_id":"1",":sales_payment_method":"cash",":sales_start_date1":"2025-05-01",":sales_end_date1":"2025-05-31",":sales_start_date2":"2025-05-01",":sales_end_date2":"2025-05-31"}
[2025-06-01 06:06:30] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:06:30] ربط المعلمة: :sales_payment_method = cash
[2025-06-01 06:06:30] ربط المعلمة: :sales_start_date1 = 2025-05-01
[2025-06-01 06:06:30] ربط المعلمة: :sales_end_date1 = 2025-05-31
[2025-06-01 06:06:30] ربط المعلمة: :sales_start_date2 = 2025-05-01
[2025-06-01 06:06:30] ربط المعلمة: :sales_end_date2 = 2025-05-31
[2025-06-01 06:06:30] نتيجة الاستعلام: 43805.00
[2025-06-01 06:06:30] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:06:30] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:06:30] الفلاتر المستخدمة: {"date_range":"last_month","start_date":"2025-05-01","end_date":"2025-05-31","branch_id":"1","payment_method":"card"}
[2025-06-01 06:06:30] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:06:30] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:06:30] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 06:06:30] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 06:06:30] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:06:30] المعلمات المرتبطة: {":sales_branch_id":"1",":sales_payment_method":"card",":sales_start_date1":"2025-05-01",":sales_end_date1":"2025-05-31",":sales_start_date2":"2025-05-01",":sales_end_date2":"2025-05-31"}
[2025-06-01 06:06:30] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:06:30] ربط المعلمة: :sales_payment_method = card
[2025-06-01 06:06:30] ربط المعلمة: :sales_start_date1 = 2025-05-01
[2025-06-01 06:06:30] ربط المعلمة: :sales_end_date1 = 2025-05-31
[2025-06-01 06:06:30] ربط المعلمة: :sales_start_date2 = 2025-05-01
[2025-06-01 06:06:30] ربط المعلمة: :sales_end_date2 = 2025-05-31
[2025-06-01 06:06:30] نتيجة الاستعلام: 
[2025-06-01 06:06:30] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:06:30] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:06:30] الفلاتر المستخدمة: {"date_range":"last_month","start_date":"2025-05-01","end_date":"2025-05-31","branch_id":"1","payment_method":"other"}
[2025-06-01 06:06:30] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:06:30] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:06:30] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 06:06:30] إضافة فلتر التاريخ باستخدام end_day: من 2025-05-01 إلى 2025-05-31
[2025-06-01 06:06:30] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:06:30] المعلمات المرتبطة: {":sales_branch_id":"1",":sales_payment_method":"other",":sales_start_date1":"2025-05-01",":sales_end_date1":"2025-05-31",":sales_start_date2":"2025-05-01",":sales_end_date2":"2025-05-31"}
[2025-06-01 06:06:30] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:06:30] ربط المعلمة: :sales_payment_method = other
[2025-06-01 06:06:30] ربط المعلمة: :sales_start_date1 = 2025-05-01
[2025-06-01 06:06:30] ربط المعلمة: :sales_end_date1 = 2025-05-31
[2025-06-01 06:06:30] ربط المعلمة: :sales_start_date2 = 2025-05-01
[2025-06-01 06:06:30] ربط المعلمة: :sales_end_date2 = 2025-05-31
[2025-06-01 06:06:30] نتيجة الاستعلام: 
[2025-06-01 06:06:30] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:08:00] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:08:00] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 06:08:00] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:08:00] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:08:00] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:08:00] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:08:00] المعلمات المرتبطة: {":sales_branch_id":"1",":sales_start_date1":"2025-06-01",":sales_end_date1":"2025-06-30",":sales_start_date2":"2025-06-01",":sales_end_date2":"2025-06-30"}
[2025-06-01 06:08:00] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:08:00] ربط المعلمة: :sales_start_date1 = 2025-06-01
[2025-06-01 06:08:00] ربط المعلمة: :sales_end_date1 = 2025-06-30
[2025-06-01 06:08:00] ربط المعلمة: :sales_start_date2 = 2025-06-01
[2025-06-01 06:08:00] ربط المعلمة: :sales_end_date2 = 2025-06-30
[2025-06-01 06:08:00] نتيجة الاستعلام: 
[2025-06-01 06:08:00] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:08:00] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:08:00] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 06:08:00] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:08:00] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:08:00] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 06:08:00] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:08:00] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:08:00] المعلمات المرتبطة: {":sales_branch_id":"1",":sales_payment_method":"cash",":sales_start_date1":"2025-06-01",":sales_end_date1":"2025-06-30",":sales_start_date2":"2025-06-01",":sales_end_date2":"2025-06-30"}
[2025-06-01 06:08:00] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:08:00] ربط المعلمة: :sales_payment_method = cash
[2025-06-01 06:08:00] ربط المعلمة: :sales_start_date1 = 2025-06-01
[2025-06-01 06:08:00] ربط المعلمة: :sales_end_date1 = 2025-06-30
[2025-06-01 06:08:00] ربط المعلمة: :sales_start_date2 = 2025-06-01
[2025-06-01 06:08:00] ربط المعلمة: :sales_end_date2 = 2025-06-30
[2025-06-01 06:08:00] نتيجة الاستعلام: 
[2025-06-01 06:08:00] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:08:00] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:08:00] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 06:08:00] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:08:00] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:08:00] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 06:08:00] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:08:00] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:08:00] المعلمات المرتبطة: {":sales_branch_id":"1",":sales_payment_method":"card",":sales_start_date1":"2025-06-01",":sales_end_date1":"2025-06-30",":sales_start_date2":"2025-06-01",":sales_end_date2":"2025-06-30"}
[2025-06-01 06:08:00] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:08:00] ربط المعلمة: :sales_payment_method = card
[2025-06-01 06:08:00] ربط المعلمة: :sales_start_date1 = 2025-06-01
[2025-06-01 06:08:00] ربط المعلمة: :sales_end_date1 = 2025-06-30
[2025-06-01 06:08:00] ربط المعلمة: :sales_start_date2 = 2025-06-01
[2025-06-01 06:08:00] ربط المعلمة: :sales_end_date2 = 2025-06-30
[2025-06-01 06:08:00] نتيجة الاستعلام: 
[2025-06-01 06:08:00] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:08:00] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:08:00] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 06:08:00] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:08:00] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:08:00] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 06:08:00] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:08:00] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:08:00] المعلمات المرتبطة: {":sales_branch_id":"1",":sales_payment_method":"other",":sales_start_date1":"2025-06-01",":sales_end_date1":"2025-06-30",":sales_start_date2":"2025-06-01",":sales_end_date2":"2025-06-30"}
[2025-06-01 06:08:00] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:08:00] ربط المعلمة: :sales_payment_method = other
[2025-06-01 06:08:00] ربط المعلمة: :sales_start_date1 = 2025-06-01
[2025-06-01 06:08:00] ربط المعلمة: :sales_end_date1 = 2025-06-30
[2025-06-01 06:08:00] ربط المعلمة: :sales_start_date2 = 2025-06-01
[2025-06-01 06:08:00] ربط المعلمة: :sales_end_date2 = 2025-06-30
[2025-06-01 06:08:00] نتيجة الاستعلام: 
[2025-06-01 06:08:00] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:08:00] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:08:00] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 06:08:00] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:08:00] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:08:00] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:08:00] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:08:00] المعلمات المرتبطة: {":sales_branch_id":"1",":sales_start_date1":"2025-06-01",":sales_end_date1":"2025-06-30",":sales_start_date2":"2025-06-01",":sales_end_date2":"2025-06-30"}
[2025-06-01 06:08:00] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:08:00] ربط المعلمة: :sales_start_date1 = 2025-06-01
[2025-06-01 06:08:00] ربط المعلمة: :sales_end_date1 = 2025-06-30
[2025-06-01 06:08:00] ربط المعلمة: :sales_start_date2 = 2025-06-01
[2025-06-01 06:08:00] ربط المعلمة: :sales_end_date2 = 2025-06-30
[2025-06-01 06:08:00] نتيجة الاستعلام: 
[2025-06-01 06:08:00] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:08:00] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:08:00] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 06:08:00] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:08:00] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:08:00] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 06:08:00] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:08:00] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:08:00] المعلمات المرتبطة: {":sales_branch_id":"1",":sales_payment_method":"cash",":sales_start_date1":"2025-06-01",":sales_end_date1":"2025-06-30",":sales_start_date2":"2025-06-01",":sales_end_date2":"2025-06-30"}
[2025-06-01 06:08:00] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:08:00] ربط المعلمة: :sales_payment_method = cash
[2025-06-01 06:08:00] ربط المعلمة: :sales_start_date1 = 2025-06-01
[2025-06-01 06:08:00] ربط المعلمة: :sales_end_date1 = 2025-06-30
[2025-06-01 06:08:00] ربط المعلمة: :sales_start_date2 = 2025-06-01
[2025-06-01 06:08:00] ربط المعلمة: :sales_end_date2 = 2025-06-30
[2025-06-01 06:08:00] نتيجة الاستعلام: 
[2025-06-01 06:08:00] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:08:00] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:08:00] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 06:08:00] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:08:00] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:08:00] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 06:08:00] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:08:00] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:08:00] المعلمات المرتبطة: {":sales_branch_id":"1",":sales_payment_method":"card",":sales_start_date1":"2025-06-01",":sales_end_date1":"2025-06-30",":sales_start_date2":"2025-06-01",":sales_end_date2":"2025-06-30"}
[2025-06-01 06:08:00] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:08:00] ربط المعلمة: :sales_payment_method = card
[2025-06-01 06:08:00] ربط المعلمة: :sales_start_date1 = 2025-06-01
[2025-06-01 06:08:00] ربط المعلمة: :sales_end_date1 = 2025-06-30
[2025-06-01 06:08:00] ربط المعلمة: :sales_start_date2 = 2025-06-01
[2025-06-01 06:08:00] ربط المعلمة: :sales_end_date2 = 2025-06-30
[2025-06-01 06:08:00] نتيجة الاستعلام: 
[2025-06-01 06:08:00] === نهاية استدعاء getTotalSales() ====
[2025-06-01 06:08:00] === بداية استدعاء getTotalSales() ====
[2025-06-01 06:08:00] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 06:08:00] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 06:08:00] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 06:08:00] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 06:08:00] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 06:08:00] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[2025-06-01 06:08:00] المعلمات المرتبطة: {":sales_branch_id":"1",":sales_payment_method":"other",":sales_start_date1":"2025-06-01",":sales_end_date1":"2025-06-30",":sales_start_date2":"2025-06-01",":sales_end_date2":"2025-06-30"}
[2025-06-01 06:08:00] ربط المعلمة: :sales_branch_id = 1
[2025-06-01 06:08:00] ربط المعلمة: :sales_payment_method = other
[2025-06-01 06:08:00] ربط المعلمة: :sales_start_date1 = 2025-06-01
[2025-06-01 06:08:00] ربط المعلمة: :sales_end_date1 = 2025-06-30
[2025-06-01 06:08:00] ربط المعلمة: :sales_start_date2 = 2025-06-01
[2025-06-01 06:08:00] ربط المعلمة: :sales_end_date2 = 2025-06-30
[2025-06-01 06:08:00] نتيجة الاستعلام: 
[2025-06-01 06:08:00] === نهاية استدعاء getTotalSales() ====
