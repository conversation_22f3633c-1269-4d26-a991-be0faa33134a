[2025-06-01 05:35:32] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:35:32] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:35:32] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:35:32] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:35:32] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:35:32] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:35:32] ربط المعلمة: :branch_id = 1
[2025-06-01 05:35:32] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:35:32] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:35:32] نتيجة الاستعلام: 805.00
[2025-06-01 05:35:32] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:35:32] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:35:32] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:35:32] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:35:32] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:35:32] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:35:32] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:35:32] ربط المعلمة: :branch_id = 1
[2025-06-01 05:35:32] ربط المعلمة: :payment_method = cash
[2025-06-01 05:35:32] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:35:32] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:35:32] نتيجة الاستعلام: 805.00
[2025-06-01 05:35:32] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:35:32] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:35:32] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:35:32] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:35:32] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:35:32] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:35:32] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:35:32] ربط المعلمة: :branch_id = 1
[2025-06-01 05:35:32] ربط المعلمة: :payment_method = card
[2025-06-01 05:35:32] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:35:32] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:35:32] نتيجة الاستعلام: 
[2025-06-01 05:35:32] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:35:32] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:35:32] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:35:32] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:35:32] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:35:32] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:35:32] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:35:32] ربط المعلمة: :branch_id = 1
[2025-06-01 05:35:32] ربط المعلمة: :payment_method = other
[2025-06-01 05:35:32] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:35:32] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:35:32] نتيجة الاستعلام: 
[2025-06-01 05:35:32] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:35:32] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:35:32] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:35:32] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:35:32] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:35:32] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:35:32] ربط المعلمة: :branch_id = 1
[2025-06-01 05:35:32] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:35:32] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:35:32] نتيجة الاستعلام: 805.00
[2025-06-01 05:35:32] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:35:32] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:35:32] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:35:32] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:35:32] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:35:32] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:35:32] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:35:32] ربط المعلمة: :branch_id = 1
[2025-06-01 05:35:32] ربط المعلمة: :payment_method = cash
[2025-06-01 05:35:32] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:35:32] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:35:32] نتيجة الاستعلام: 805.00
[2025-06-01 05:35:32] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:35:32] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:35:32] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:35:32] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:35:32] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:35:32] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:35:32] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:35:32] ربط المعلمة: :branch_id = 1
[2025-06-01 05:35:32] ربط المعلمة: :payment_method = card
[2025-06-01 05:35:32] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:35:32] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:35:32] نتيجة الاستعلام: 
[2025-06-01 05:35:32] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:35:32] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:35:32] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:35:32] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:35:32] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:35:32] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:35:32] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:35:32] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:35:32] ربط المعلمة: :branch_id = 1
[2025-06-01 05:35:32] ربط المعلمة: :payment_method = other
[2025-06-01 05:35:32] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:35:32] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:35:32] نتيجة الاستعلام: 
[2025-06-01 05:35:32] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1"}
[2025-06-01 05:37:14] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:37:14] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:37:14] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:37:14] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:37:14] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:37:14] ربط المعلمة: :branch_id = 1
[2025-06-01 05:37:14] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:37:14] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:37:14] نتيجة الاستعلام: 43945.00
[2025-06-01 05:37:14] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:37:14] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:37:14] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:37:14] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:37:14] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:37:14] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:37:14] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:37:14] ربط المعلمة: :branch_id = 1
[2025-06-01 05:37:14] ربط المعلمة: :payment_method = cash
[2025-06-01 05:37:14] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:37:14] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:37:14] نتيجة الاستعلام: 43945.00
[2025-06-01 05:37:14] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1","payment_method":"card"}
[2025-06-01 05:37:14] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:37:14] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:37:14] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:37:14] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:37:14] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:37:14] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:37:14] ربط المعلمة: :branch_id = 1
[2025-06-01 05:37:14] ربط المعلمة: :payment_method = card
[2025-06-01 05:37:14] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:37:14] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:37:14] نتيجة الاستعلام: 
[2025-06-01 05:37:14] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1","payment_method":"other"}
[2025-06-01 05:37:14] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:37:14] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:37:14] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:37:14] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:37:14] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:37:14] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:37:14] ربط المعلمة: :branch_id = 1
[2025-06-01 05:37:14] ربط المعلمة: :payment_method = other
[2025-06-01 05:37:14] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:37:14] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:37:14] نتيجة الاستعلام: 
[2025-06-01 05:37:14] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1"}
[2025-06-01 05:37:14] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:37:14] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:37:14] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:37:14] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:37:14] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:37:14] ربط المعلمة: :branch_id = 1
[2025-06-01 05:37:14] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:37:14] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:37:14] نتيجة الاستعلام: 43945.00
[2025-06-01 05:37:14] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:37:14] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:37:14] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:37:14] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:37:14] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:37:14] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:37:14] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:37:14] ربط المعلمة: :branch_id = 1
[2025-06-01 05:37:14] ربط المعلمة: :payment_method = cash
[2025-06-01 05:37:14] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:37:14] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:37:14] نتيجة الاستعلام: 43945.00
[2025-06-01 05:37:14] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1","payment_method":"card"}
[2025-06-01 05:37:14] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:37:14] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:37:14] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:37:14] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:37:14] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:37:14] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:37:14] ربط المعلمة: :branch_id = 1
[2025-06-01 05:37:14] ربط المعلمة: :payment_method = card
[2025-06-01 05:37:14] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:37:14] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:37:14] نتيجة الاستعلام: 
[2025-06-01 05:37:14] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:37:14] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1","payment_method":"other"}
[2025-06-01 05:37:14] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:37:14] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:37:14] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:37:14] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:37:14] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:37:14] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:37:14] ربط المعلمة: :branch_id = 1
[2025-06-01 05:37:14] ربط المعلمة: :payment_method = other
[2025-06-01 05:37:14] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:37:14] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:37:14] نتيجة الاستعلام: 
[2025-06-01 05:37:14] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1"}
[2025-06-01 05:40:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:55] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:40:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:55] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:40:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:55] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:40:55] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:40:55] نتيجة الاستعلام: 43945.00
[2025-06-01 05:40:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:40:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:55] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:40:55] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:40:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:55] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:40:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:55] ربط المعلمة: :payment_method = cash
[2025-06-01 05:40:55] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:40:55] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:40:55] نتيجة الاستعلام: 43945.00
[2025-06-01 05:40:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1","payment_method":"card"}
[2025-06-01 05:40:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:55] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:40:55] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:40:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:55] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:40:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:55] ربط المعلمة: :payment_method = card
[2025-06-01 05:40:55] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:40:55] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:40:55] نتيجة الاستعلام: 
[2025-06-01 05:40:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1","payment_method":"other"}
[2025-06-01 05:40:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:55] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:40:55] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:40:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:55] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:40:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:55] ربط المعلمة: :payment_method = other
[2025-06-01 05:40:55] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:40:55] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:40:55] نتيجة الاستعلام: 
[2025-06-01 05:40:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1"}
[2025-06-01 05:40:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:55] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:40:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:55] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:40:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:55] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:40:55] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:40:55] نتيجة الاستعلام: 43945.00
[2025-06-01 05:40:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:40:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:55] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:40:55] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:40:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:55] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:40:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:55] ربط المعلمة: :payment_method = cash
[2025-06-01 05:40:55] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:40:55] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:40:55] نتيجة الاستعلام: 43945.00
[2025-06-01 05:40:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1","payment_method":"card"}
[2025-06-01 05:40:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:55] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:40:55] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:40:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:55] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:40:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:55] ربط المعلمة: :payment_method = card
[2025-06-01 05:40:55] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:40:55] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:40:55] نتيجة الاستعلام: 
[2025-06-01 05:40:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:55] الفلاتر المستخدمة: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1","payment_method":"other"}
[2025-06-01 05:40:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:55] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:40:55] إضافة فلتر نطاق التاريخ: 2025-05-01 إلى 2025-05-31
[2025-06-01 05:40:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:55] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-05-01 00:00:00",":end_date":"2025-05-31 23:59:59"}
[2025-06-01 05:40:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:55] ربط المعلمة: :payment_method = other
[2025-06-01 05:40:55] ربط المعلمة: :start_date = 2025-05-01 00:00:00
[2025-06-01 05:40:55] ربط المعلمة: :end_date = 2025-05-31 23:59:59
[2025-06-01 05:40:55] نتيجة الاستعلام: 
[2025-06-01 05:40:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:40:58] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:58] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:58] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:40:58] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:58] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:40:58] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:58] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:40:58] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:40:58] نتيجة الاستعلام: 805.00
[2025-06-01 05:40:58] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:40:58] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:58] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:58] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:40:58] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:40:58] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:58] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:40:58] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:58] ربط المعلمة: :payment_method = cash
[2025-06-01 05:40:58] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:40:58] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:40:58] نتيجة الاستعلام: 805.00
[2025-06-01 05:40:58] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:40:58] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:58] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:58] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:40:58] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:40:58] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:58] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:40:58] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:58] ربط المعلمة: :payment_method = card
[2025-06-01 05:40:58] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:40:58] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:40:58] نتيجة الاستعلام: 
[2025-06-01 05:40:58] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:40:58] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:58] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:58] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:40:58] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:40:58] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:58] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:40:58] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:58] ربط المعلمة: :payment_method = other
[2025-06-01 05:40:58] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:40:58] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:40:58] نتيجة الاستعلام: 
[2025-06-01 05:40:58] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:40:58] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:58] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:58] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:40:58] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:58] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:40:58] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:58] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:40:58] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:40:58] نتيجة الاستعلام: 805.00
[2025-06-01 05:40:58] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:40:58] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:58] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:58] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:40:58] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:40:58] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:58] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:40:58] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:58] ربط المعلمة: :payment_method = cash
[2025-06-01 05:40:58] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:40:58] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:40:58] نتيجة الاستعلام: 805.00
[2025-06-01 05:40:58] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:40:58] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:58] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:58] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:40:58] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:40:58] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:58] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:40:58] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:58] ربط المعلمة: :payment_method = card
[2025-06-01 05:40:58] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:40:58] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:40:58] نتيجة الاستعلام: 
[2025-06-01 05:40:58] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:40:58] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:40:58] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:40:58] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:40:58] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:40:58] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:40:58] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:40:58] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:40:58] ربط المعلمة: :branch_id = 1
[2025-06-01 05:40:58] ربط المعلمة: :payment_method = other
[2025-06-01 05:40:58] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:40:58] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:40:58] نتيجة الاستعلام: 
[2025-06-01 05:40:58] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:41:53] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:53] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:53] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:53] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:53] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:53] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:53] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:53] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:53] نتيجة الاستعلام: 805.00
[2025-06-01 05:41:53] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:41:53] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:53] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:53] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:41:53] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:53] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:53] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:53] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:53] ربط المعلمة: :payment_method = cash
[2025-06-01 05:41:53] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:53] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:53] نتيجة الاستعلام: 805.00
[2025-06-01 05:41:53] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:41:53] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:53] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:53] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:41:53] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:53] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:53] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:53] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:53] ربط المعلمة: :payment_method = card
[2025-06-01 05:41:53] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:53] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:53] نتيجة الاستعلام: 
[2025-06-01 05:41:53] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:41:53] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:53] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:53] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:41:53] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:53] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:53] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:53] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:53] ربط المعلمة: :payment_method = other
[2025-06-01 05:41:53] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:53] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:53] نتيجة الاستعلام: 
[2025-06-01 05:41:53] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:41:53] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:53] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:53] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:53] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:53] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:53] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:53] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:53] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:53] نتيجة الاستعلام: 805.00
[2025-06-01 05:41:53] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:41:53] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:53] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:53] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:41:53] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:53] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:53] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:53] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:53] ربط المعلمة: :payment_method = cash
[2025-06-01 05:41:53] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:53] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:53] نتيجة الاستعلام: 805.00
[2025-06-01 05:41:53] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:41:53] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:53] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:53] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:41:53] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:53] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:53] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:53] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:53] ربط المعلمة: :payment_method = card
[2025-06-01 05:41:53] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:53] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:53] نتيجة الاستعلام: 
[2025-06-01 05:41:53] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:53] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:41:53] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:53] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:53] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:41:53] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:53] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:53] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:53] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:53] ربط المعلمة: :payment_method = other
[2025-06-01 05:41:53] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:53] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:53] نتيجة الاستعلام: 
[2025-06-01 05:41:53] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:41:54] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:54] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:54] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:54] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:54] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:54] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:54] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:54] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:54] نتيجة الاستعلام: 805.00
[2025-06-01 05:41:54] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:41:54] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:54] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:54] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:41:54] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:54] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:54] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:54] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:54] ربط المعلمة: :payment_method = cash
[2025-06-01 05:41:54] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:54] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:54] نتيجة الاستعلام: 805.00
[2025-06-01 05:41:54] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:41:54] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:54] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:54] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:41:54] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:54] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:54] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:54] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:54] ربط المعلمة: :payment_method = card
[2025-06-01 05:41:54] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:54] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:54] نتيجة الاستعلام: 
[2025-06-01 05:41:54] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:41:54] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:54] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:54] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:41:54] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:54] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:54] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:54] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:54] ربط المعلمة: :payment_method = other
[2025-06-01 05:41:54] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:54] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:54] نتيجة الاستعلام: 
[2025-06-01 05:41:54] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:41:54] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:54] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:54] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:54] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:54] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:54] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:54] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:54] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:54] نتيجة الاستعلام: 805.00
[2025-06-01 05:41:54] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:41:54] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:54] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:54] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:41:54] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:54] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:54] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:54] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:54] ربط المعلمة: :payment_method = cash
[2025-06-01 05:41:54] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:54] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:54] نتيجة الاستعلام: 805.00
[2025-06-01 05:41:54] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:41:54] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:54] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:54] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:41:54] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:54] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:54] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:54] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:54] ربط المعلمة: :payment_method = card
[2025-06-01 05:41:54] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:54] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:54] نتيجة الاستعلام: 
[2025-06-01 05:41:54] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:54] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:41:54] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:54] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:54] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:41:54] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:54] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:54] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:54] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:54] ربط المعلمة: :payment_method = other
[2025-06-01 05:41:54] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:54] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:54] نتيجة الاستعلام: 
[2025-06-01 05:41:54] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:41:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:55] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:55] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:55] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:55] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:55] نتيجة الاستعلام: 805.00
[2025-06-01 05:41:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:41:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:55] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:41:55] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:55] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:55] ربط المعلمة: :payment_method = cash
[2025-06-01 05:41:55] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:55] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:55] نتيجة الاستعلام: 805.00
[2025-06-01 05:41:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:41:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:55] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:41:55] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:55] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:55] ربط المعلمة: :payment_method = card
[2025-06-01 05:41:55] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:55] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:55] نتيجة الاستعلام: 
[2025-06-01 05:41:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:41:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:55] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:41:55] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:55] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:55] ربط المعلمة: :payment_method = other
[2025-06-01 05:41:55] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:55] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:55] نتيجة الاستعلام: 
[2025-06-01 05:41:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:41:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:55] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:55] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:55] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:55] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:55] نتيجة الاستعلام: 805.00
[2025-06-01 05:41:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:41:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:55] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:41:55] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:55] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:55] ربط المعلمة: :payment_method = cash
[2025-06-01 05:41:55] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:55] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:55] نتيجة الاستعلام: 805.00
[2025-06-01 05:41:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:41:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:55] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:41:55] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:55] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:55] ربط المعلمة: :payment_method = card
[2025-06-01 05:41:55] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:55] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:55] نتيجة الاستعلام: 
[2025-06-01 05:41:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:41:55] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:41:55] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:41:55] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:41:55] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:41:55] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:41:55] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:41:55] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:41:55] ربط المعلمة: :branch_id = 1
[2025-06-01 05:41:55] ربط المعلمة: :payment_method = other
[2025-06-01 05:41:55] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:41:55] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:41:55] نتيجة الاستعلام: 
[2025-06-01 05:41:55] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:44:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:44:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:44:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:44:29] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:44:29] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:44:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:44:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:44:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:44:29] نتيجة الاستعلام: 805.00
[2025-06-01 05:44:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:44:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:44:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:44:29] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:44:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:44:29] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:44:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:44:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:44:29] ربط المعلمة: :payment_method = cash
[2025-06-01 05:44:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:44:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:44:29] نتيجة الاستعلام: 805.00
[2025-06-01 05:44:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:44:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:44:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:44:29] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:44:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:44:29] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:44:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:44:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:44:29] ربط المعلمة: :payment_method = card
[2025-06-01 05:44:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:44:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:44:29] نتيجة الاستعلام: 
[2025-06-01 05:44:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:44:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:44:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:44:29] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:44:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:44:29] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:44:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:44:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:44:29] ربط المعلمة: :payment_method = other
[2025-06-01 05:44:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:44:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:44:29] نتيجة الاستعلام: 
[2025-06-01 05:44:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:44:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:44:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:44:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:44:29] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:44:29] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:44:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:44:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:44:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:44:29] نتيجة الاستعلام: 805.00
[2025-06-01 05:44:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:44:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:44:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:44:29] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:44:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:44:29] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:44:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:44:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:44:29] ربط المعلمة: :payment_method = cash
[2025-06-01 05:44:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:44:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:44:29] نتيجة الاستعلام: 805.00
[2025-06-01 05:44:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:44:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:44:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:44:29] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:44:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:44:29] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:44:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:44:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:44:29] ربط المعلمة: :payment_method = card
[2025-06-01 05:44:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:44:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:44:29] نتيجة الاستعلام: 
[2025-06-01 05:44:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:44:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:44:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:44:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:44:29] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:44:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:44:29] الاستعلام النهائي: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:44:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:44:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:44:29] ربط المعلمة: :payment_method = other
[2025-06-01 05:44:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:44:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:44:29] نتيجة الاستعلام: 
[2025-06-01 05:44:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 805.00
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :payment_method = cash
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 805.00
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :payment_method = card
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :payment_method = other
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 805.00
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :payment_method = cash
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 805.00
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :payment_method = card
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :payment_method = other
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 805.00
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :payment_method = cash
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 805.00
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :payment_method = card
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :payment_method = other
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 805.00
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :payment_method = cash
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 805.00
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :payment_method = card
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:29] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:49:29] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:29] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:29] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:49:29] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:29] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:29] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:29] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:29] ربط المعلمة: :payment_method = other
[2025-06-01 05:49:29] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:29] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:29] نتيجة الاستعلام: 
[2025-06-01 05:49:29] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:49:30] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:30] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:30] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:30] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:30] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:30] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:30] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:30] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:30] نتيجة الاستعلام: 805.00
[2025-06-01 05:49:30] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:49:30] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:30] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:30] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:49:30] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:30] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:30] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:30] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:30] ربط المعلمة: :payment_method = cash
[2025-06-01 05:49:30] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:30] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:30] نتيجة الاستعلام: 805.00
[2025-06-01 05:49:30] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:49:30] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:30] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:30] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:49:30] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:30] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:30] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:30] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:30] ربط المعلمة: :payment_method = card
[2025-06-01 05:49:30] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:30] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:30] نتيجة الاستعلام: 
[2025-06-01 05:49:30] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:49:30] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:30] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:30] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:49:30] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:30] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:30] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:30] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:30] ربط المعلمة: :payment_method = other
[2025-06-01 05:49:30] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:30] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:30] نتيجة الاستعلام: 
[2025-06-01 05:49:30] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:49:30] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:30] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:30] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:30] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:30] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:30] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:30] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:30] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:30] نتيجة الاستعلام: 805.00
[2025-06-01 05:49:30] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"cash"}
[2025-06-01 05:49:30] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:30] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:30] إضافة فلتر طريقة الدفع: i.payment_method = cash
[2025-06-01 05:49:30] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:30] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:30] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"cash",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:30] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:30] ربط المعلمة: :payment_method = cash
[2025-06-01 05:49:30] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:30] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:30] نتيجة الاستعلام: 805.00
[2025-06-01 05:49:30] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"card"}
[2025-06-01 05:49:30] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:30] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:30] إضافة فلتر طريقة الدفع: i.payment_method = card
[2025-06-01 05:49:30] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:30] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:30] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"card",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:30] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:30] ربط المعلمة: :payment_method = card
[2025-06-01 05:49:30] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:30] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:30] نتيجة الاستعلام: 
[2025-06-01 05:49:30] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:30] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1","payment_method":"other"}
[2025-06-01 05:49:30] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:30] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:30] إضافة فلتر طريقة الدفع: i.payment_method = other
[2025-06-01 05:49:30] إضافة فلتر نطاق التاريخ: 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:30] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[2025-06-01 05:49:30] المعلمات المرتبطة: {":branch_id":"1",":payment_method":"other",":start_date":"2025-06-01 00:00:00",":end_date":"2025-06-30 23:59:59"}
[2025-06-01 05:49:30] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:30] ربط المعلمة: :payment_method = other
[2025-06-01 05:49:30] ربط المعلمة: :start_date = 2025-06-01 00:00:00
[2025-06-01 05:49:30] ربط المعلمة: :end_date = 2025-06-30 23:59:59
[2025-06-01 05:49:30] نتيجة الاستعلام: 
[2025-06-01 05:49:30] === نهاية استدعاء getTotalSales() ====
[2025-06-01 05:49:35] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:35] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:49:35] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:35] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:35] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:35] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:49:35] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01",":end_date":"2025-06-30"}
[2025-06-01 05:49:35] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:35] ربط المعلمة: :start_date = 2025-06-01
[2025-06-01 05:49:35] ربط المعلمة: :end_date = 2025-06-30
[2025-06-01 05:49:35] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:49:35] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:35] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:49:35] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:35] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:35] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:35] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:49:35] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01",":end_date":"2025-06-30"}
[2025-06-01 05:49:35] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:35] ربط المعلمة: :start_date = 2025-06-01
[2025-06-01 05:49:35] ربط المعلمة: :end_date = 2025-06-30
[2025-06-01 05:49:35] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:49:36] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:36] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:49:36] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:36] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:36] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:36] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:49:36] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01",":end_date":"2025-06-30"}
[2025-06-01 05:49:36] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:36] ربط المعلمة: :start_date = 2025-06-01
[2025-06-01 05:49:36] ربط المعلمة: :end_date = 2025-06-30
[2025-06-01 05:49:36] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:49:36] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:36] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:49:36] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:36] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:36] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:36] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:49:36] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01",":end_date":"2025-06-30"}
[2025-06-01 05:49:36] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:36] ربط المعلمة: :start_date = 2025-06-01
[2025-06-01 05:49:36] ربط المعلمة: :end_date = 2025-06-30
[2025-06-01 05:49:36] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:49:43] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:43] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:49:43] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:43] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:43] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:43] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:49:43] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01",":end_date":"2025-06-30"}
[2025-06-01 05:49:43] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:43] ربط المعلمة: :start_date = 2025-06-01
[2025-06-01 05:49:43] ربط المعلمة: :end_date = 2025-06-30
[2025-06-01 05:49:43] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:49:43] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:43] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:49:43] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:43] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:43] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:43] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:49:43] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01",":end_date":"2025-06-30"}
[2025-06-01 05:49:43] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:43] ربط المعلمة: :start_date = 2025-06-01
[2025-06-01 05:49:43] ربط المعلمة: :end_date = 2025-06-30
[2025-06-01 05:49:43] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:49:46] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:46] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:49:46] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:46] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:46] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:46] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:49:46] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01",":end_date":"2025-06-30"}
[2025-06-01 05:49:46] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:46] ربط المعلمة: :start_date = 2025-06-01
[2025-06-01 05:49:46] ربط المعلمة: :end_date = 2025-06-30
[2025-06-01 05:49:46] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:49:46] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:49:46] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:49:46] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:49:46] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:49:46] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:49:46] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:49:46] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01",":end_date":"2025-06-30"}
[2025-06-01 05:49:46] ربط المعلمة: :branch_id = 1
[2025-06-01 05:49:46] ربط المعلمة: :start_date = 2025-06-01
[2025-06-01 05:49:46] ربط المعلمة: :end_date = 2025-06-30
[2025-06-01 05:49:46] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:50:03] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:50:03] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:50:03] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:50:03] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:50:03] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:50:03] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:50:03] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01",":end_date":"2025-06-30"}
[2025-06-01 05:50:03] ربط المعلمة: :branch_id = 1
[2025-06-01 05:50:03] ربط المعلمة: :start_date = 2025-06-01
[2025-06-01 05:50:03] ربط المعلمة: :end_date = 2025-06-30
[2025-06-01 05:50:03] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:50:03] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:50:03] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:50:03] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:50:03] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:50:03] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:50:03] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:50:03] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01",":end_date":"2025-06-30"}
[2025-06-01 05:50:03] ربط المعلمة: :branch_id = 1
[2025-06-01 05:50:03] ربط المعلمة: :start_date = 2025-06-01
[2025-06-01 05:50:03] ربط المعلمة: :end_date = 2025-06-30
[2025-06-01 05:50:03] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:50:09] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:50:09] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:50:09] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:50:09] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:50:09] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:50:09] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:50:09] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01",":end_date":"2025-06-30"}
[2025-06-01 05:50:09] ربط المعلمة: :branch_id = 1
[2025-06-01 05:50:09] ربط المعلمة: :start_date = 2025-06-01
[2025-06-01 05:50:09] ربط المعلمة: :end_date = 2025-06-30
[2025-06-01 05:50:09] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:50:09] === بداية استدعاء getTotalSales() ====
[2025-06-01 05:50:09] الفلاتر المستخدمة: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[2025-06-01 05:50:09] إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'
[2025-06-01 05:50:09] إضافة فلتر الفرع: i.branch_id = 1
[2025-06-01 05:50:09] إضافة فلتر التاريخ باستخدام end_day: من 2025-06-01 إلى 2025-06-30
[2025-06-01 05:50:09] الاستعلام النهائي: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:50:09] المعلمات المرتبطة: {":branch_id":"1",":start_date":"2025-06-01",":end_date":"2025-06-30"}
[2025-06-01 05:50:09] ربط المعلمة: :branch_id = 1
[2025-06-01 05:50:09] ربط المعلمة: :start_date = 2025-06-01
[2025-06-01 05:50:09] ربط المعلمة: :end_date = 2025-06-30
[2025-06-01 05:50:09] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
