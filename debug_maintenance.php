<?php
/**
 * ملف تشخيص مشاكل نظام الصيانة
 */

session_start();

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head><meta charset='UTF-8'><title>تشخيص نظام الصيانة</title></head>";
echo "<body style='font-family: Arial, sans-serif; margin: 20px;'>";

echo "<h1>🔧 تشخيص نظام الصيانة</h1>";

// 1. معلومات الجلسة
echo "<h2>📋 معلومات الجلسة:</h2>";
echo "<ul>";
echo "<li><strong>معرف الجلسة:</strong> " . session_id() . "</li>";
echo "<li><strong>دور المستخدم:</strong> " . ($_SESSION['user_role'] ?? 'غير محدد') . "</li>";
echo "<li><strong>معرف المستخدم:</strong> " . ($_SESSION['user_id'] ?? 'غير محدد') . "</li>";
echo "<li><strong>اسم المستخدم:</strong> " . ($_SESSION['user_name'] ?? 'غير محدد') . "</li>";
echo "</ul>";

// 2. معلومات الملفات
echo "<h2>📁 معلومات الملفات:</h2>";

$files = [
    'config/maintenance_mode.txt' => 'ملف تفعيل الصيانة',
    'config/maintenance_config.json' => 'ملف إعدادات الصيانة',
    'includes/maintenance_check.php' => 'ملف التحقق من الصيانة',
    'admin/maintenance_control.php' => 'لوحة التحكم في الصيانة',
    'maintenance.php' => 'صفحة الصيانة'
];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>الملف</th><th>الوصف</th><th>موجود</th><th>قابل للقراءة</th><th>قابل للكتابة</th><th>الحجم</th></tr>";

foreach ($files as $file => $description) {
    $exists = file_exists($file);
    $readable = $exists ? is_readable($file) : false;
    $writable = $exists ? is_writable($file) : false;
    $size = $exists ? filesize($file) : 0;
    
    echo "<tr>";
    echo "<td>$file</td>";
    echo "<td>$description</td>";
    echo "<td>" . ($exists ? '✅' : '❌') . "</td>";
    echo "<td>" . ($readable ? '✅' : '❌') . "</td>";
    echo "<td>" . ($writable ? '✅' : '❌') . "</td>";
    echo "<td>$size بايت</td>";
    echo "</tr>";
}

echo "</table>";

// 3. اختبار إنشاء ملف الصيانة
echo "<h2>🧪 اختبار إنشاء ملف الصيانة:</h2>";

$maintenanceFile = 'config/maintenance_mode.txt';

// محاولة إنشاء الملف
echo "<p><strong>محاولة إنشاء الملف...</strong></p>";
$result = file_put_contents($maintenanceFile, 'test_' . date('Y-m-d H:i:s'));

if ($result !== false) {
    echo "<p>✅ تم إنشاء الملف بنجاح! ($result بايت)</p>";
    
    // التحقق من وجود الملف
    if (file_exists($maintenanceFile)) {
        echo "<p>✅ الملف موجود الآن</p>";
        echo "<p><strong>محتوى الملف:</strong> " . file_get_contents($maintenanceFile) . "</p>";
        
        // حذف الملف
        if (unlink($maintenanceFile)) {
            echo "<p>✅ تم حذف الملف بنجاح</p>";
        } else {
            echo "<p>❌ فشل في حذف الملف</p>";
        }
    } else {
        echo "<p>❌ الملف غير موجود رغم نجاح الإنشاء!</p>";
    }
} else {
    echo "<p>❌ فشل في إنشاء الملف</p>";
    
    // التحقق من صلاحيات المجلد
    $configDir = 'config/';
    echo "<p><strong>صلاحيات مجلد config:</strong></p>";
    echo "<ul>";
    echo "<li>موجود: " . (is_dir($configDir) ? '✅' : '❌') . "</li>";
    echo "<li>قابل للقراءة: " . (is_readable($configDir) ? '✅' : '❌') . "</li>";
    echo "<li>قابل للكتابة: " . (is_writable($configDir) ? '✅' : '❌') . "</li>";
    echo "</ul>";
}

// 4. اختبار دوال الصيانة
echo "<h2>⚙️ اختبار دوال الصيانة:</h2>";

// تضمين ملف التحقق
if (file_exists('includes/maintenance_check.php')) {
    require_once 'includes/maintenance_check.php';
    
    echo "<p><strong>اختبار الدوال:</strong></p>";
    echo "<ul>";
    
    // اختبار isMaintenanceMode
    try {
        $isMaintenanceMode = isMaintenanceMode();
        echo "<li>isMaintenanceMode(): " . ($isMaintenanceMode ? 'true' : 'false') . "</li>";
    } catch (Exception $e) {
        echo "<li>خطأ في isMaintenanceMode(): " . $e->getMessage() . "</li>";
    }
    
    // اختبار isMaintenanceException
    try {
        $isException = isMaintenanceException();
        echo "<li>isMaintenanceException(): " . ($isException ? 'true' : 'false') . "</li>";
    } catch (Exception $e) {
        echo "<li>خطأ في isMaintenanceException(): " . $e->getMessage() . "</li>";
    }
    
    echo "</ul>";
} else {
    echo "<p>❌ ملف maintenance_check.php غير موجود</p>";
}

// 5. معلومات الخادم
echo "<h2>🖥️ معلومات الخادم:</h2>";
echo "<ul>";
echo "<li><strong>نظام التشغيل:</strong> " . PHP_OS . "</li>";
echo "<li><strong>إصدار PHP:</strong> " . PHP_VERSION . "</li>";
echo "<li><strong>المجلد الحالي:</strong> " . getcwd() . "</li>";
echo "<li><strong>المستخدم الحالي:</strong> " . get_current_user() . "</li>";
echo "<li><strong>مجلد الجذر:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</li>";
echo "</ul>";

// 6. اختبار تفعيل الصيانة يدوياً
echo "<h2>🔧 اختبار تفعيل الصيانة:</h2>";

if (isset($_GET['action'])) {
    if ($_GET['action'] === 'enable') {
        echo "<p><strong>محاولة تفعيل الصيانة...</strong></p>";
        $result = file_put_contents($maintenanceFile, 'enabled_manually_' . date('Y-m-d H:i:s'));
        
        if ($result !== false) {
            echo "<p>✅ تم تفعيل الصيانة بنجاح!</p>";
            echo "<p><a href='?action=check'>🔍 فحص الحالة</a> | <a href='?action=disable'>❌ إلغاء الصيانة</a></p>";
        } else {
            echo "<p>❌ فشل في تفعيل الصيانة</p>";
        }
    } elseif ($_GET['action'] === 'disable') {
        echo "<p><strong>محاولة إلغاء الصيانة...</strong></p>";
        if (file_exists($maintenanceFile)) {
            if (unlink($maintenanceFile)) {
                echo "<p>✅ تم إلغاء الصيانة بنجاح!</p>";
            } else {
                echo "<p>❌ فشل في حذف ملف الصيانة</p>";
            }
        } else {
            echo "<p>ℹ️ ملف الصيانة غير موجود بالفعل</p>";
        }
        echo "<p><a href='?action=check'>🔍 فحص الحالة</a> | <a href='?action=enable'>✅ تفعيل الصيانة</a></p>";
    } elseif ($_GET['action'] === 'check') {
        echo "<p><strong>فحص حالة الصيانة...</strong></p>";
        if (file_exists($maintenanceFile)) {
            echo "<p>🟡 الصيانة مفعلة</p>";
            echo "<p><strong>محتوى الملف:</strong> " . file_get_contents($maintenanceFile) . "</p>";
            echo "<p><a href='?action=disable'>❌ إلغاء الصيانة</a></p>";
        } else {
            echo "<p>🟢 الصيانة غير مفعلة</p>";
            echo "<p><a href='?action=enable'>✅ تفعيل الصيانة</a></p>";
        }
    }
} else {
    echo "<p><strong>أزرار التحكم:</strong></p>";
    echo "<p>";
    echo "<a href='?action=enable' style='background: orange; color: white; padding: 10px; text-decoration: none; margin: 5px;'>✅ تفعيل الصيانة</a> ";
    echo "<a href='?action=disable' style='background: green; color: white; padding: 10px; text-decoration: none; margin: 5px;'>❌ إلغاء الصيانة</a> ";
    echo "<a href='?action=check' style='background: blue; color: white; padding: 10px; text-decoration: none; margin: 5px;'>🔍 فحص الحالة</a>";
    echo "</p>";
}

// 7. روابط مفيدة
echo "<h2>🔗 روابط مفيدة:</h2>";
echo "<ul>";
echo "<li><a href='maintenance.php' target='_blank'>صفحة الصيانة</a></li>";
echo "<li><a href='admin/maintenance_control.php' target='_blank'>لوحة التحكم في الصيانة</a></li>";
echo "<li><a href='pages/dashboard.php' target='_blank'>لوحة التحكم الرئيسية</a></li>";
echo "<li><a href='test_maintenance.php' target='_blank'>صفحة الاختبار الأخرى</a></li>";
echo "</ul>";

echo "</body></html>";
?>
