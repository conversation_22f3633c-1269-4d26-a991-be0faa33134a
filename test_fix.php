<?php
/**
 * ملف اختبار لتجربة الإصلاح الجديد للتقارير
 * يختبر حساب المبيعات باستخدام تاريخ end_day بدلاً من created_at
 */

require_once 'config/config.php';
require_once 'includes/classes/Database.php';
require_once 'includes/classes/Invoice.php';

// إنشاء اتصال قاعدة البيانات
$db = new Database();

// إنشاء نموذج الفواتير
$invoiceModel = new Invoice($db);

echo "<h1>اختبار إصلاح التقارير</h1>";

// اختبار 1: حساب إجمالي المبيعات لشهر يونيو 2025
echo "<h2>اختبار 1: إجمالي المبيعات لشهر يونيو 2025</h2>";

$filters = [
    'start_date' => '2025-06-01',
    'end_date' => '2025-06-30',
    'branch_id' => 1
];

try {
    $totalSales = $invoiceModel->getTotalSales($filters);
    echo "<p>إجمالي المبيعات لشهر يونيو 2025: <strong>" . number_format($totalSales, 2) . " جنيه</strong></p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
}

// اختبار 2: حساب إجمالي المبيعات لشهر مايو 2025
echo "<h2>اختبار 2: إجمالي المبيعات لشهر مايو 2025</h2>";

$filters = [
    'start_date' => '2025-05-01',
    'end_date' => '2025-05-31',
    'branch_id' => 1
];

try {
    $totalSales = $invoiceModel->getTotalSales($filters);
    echo "<p>إجمالي المبيعات لشهر مايو 2025: <strong>" . number_format($totalSales, 2) . " جنيه</strong></p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
}

// اختبار 3: فحص الفواتير المرتبطة بيوم 31/5/2025
echo "<h2>اختبار 3: فحص الفواتير المرتبطة بيوم 31/5/2025</h2>";

try {
    // البحث عن end_day_id ليوم 31/5/2025
    $db->prepare("SELECT id, date, closed_at FROM end_days WHERE date = '2025-05-31' AND branch_id = 1");
    $endDay = $db->fetch();
    
    if ($endDay) {
        echo "<p>تم العثور على يوم العمل: ID = {$endDay['id']}, التاريخ = {$endDay['date']}, تم الإقفال في = {$endDay['closed_at']}</p>";
        
        // البحث عن الفواتير المرتبطة بهذا اليوم
        $db->prepare("SELECT COUNT(*) as count, SUM(final_amount) as total FROM invoices WHERE end_day_id = :end_day_id AND payment_status = 'paid'");
        $db->bind(':end_day_id', $endDay['id']);
        $invoiceStats = $db->fetch();
        
        echo "<p>عدد الفواتير المرتبطة بهذا اليوم: {$invoiceStats['count']}</p>";
        echo "<p>إجمالي قيمة الفواتير: " . number_format($invoiceStats['total'], 2) . " جنيه</p>";
        
        // فحص تواريخ إنشاء هذه الفواتير
        $db->prepare("SELECT DATE(created_at) as creation_date, COUNT(*) as count, SUM(final_amount) as total 
                     FROM invoices 
                     WHERE end_day_id = :end_day_id AND payment_status = 'paid' 
                     GROUP BY DATE(created_at) 
                     ORDER BY creation_date");
        $db->bind(':end_day_id', $endDay['id']);
        $creationDates = $db->fetchAll();
        
        echo "<h3>توزيع الفواتير حسب تاريخ الإنشاء:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>تاريخ الإنشاء</th><th>عدد الفواتير</th><th>إجمالي القيمة</th></tr>";
        foreach ($creationDates as $date) {
            echo "<tr>";
            echo "<td>{$date['creation_date']}</td>";
            echo "<td>{$date['count']}</td>";
            echo "<td>" . number_format($date['total'], 2) . " جنيه</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } else {
        echo "<p style='color: red;'>لم يتم العثور على يوم العمل لتاريخ 31/5/2025</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
}

// اختبار 4: مقارنة النتائج قبل وبعد الإصلاح
echo "<h2>اختبار 4: مقارنة النتائج</h2>";

try {
    // الطريقة القديمة (باستخدام created_at فقط)
    $db->prepare("SELECT SUM(final_amount) as total FROM invoices 
                 WHERE DATE(created_at) BETWEEN '2025-06-01' AND '2025-06-30' 
                 AND payment_status = 'paid' AND branch_id = 1");
    $oldMethod = $db->fetch();
    
    echo "<p>الطريقة القديمة (created_at): " . number_format($oldMethod['total'], 2) . " جنيه</p>";
    
    // الطريقة الجديدة (باستخدام end_day)
    $newMethodTotal = $invoiceModel->getTotalSales([
        'start_date' => '2025-06-01',
        'end_date' => '2025-06-30',
        'branch_id' => 1
    ]);
    
    echo "<p>الطريقة الجديدة (end_day): " . number_format($newMethodTotal, 2) . " جنيه</p>";
    
    $difference = $newMethodTotal - $oldMethod['total'];
    echo "<p>الفرق: " . number_format($difference, 2) . " جنيه</p>";
    
    if ($difference != 0) {
        echo "<p style='color: green;'><strong>تم إصلاح المشكلة! الآن التقارير تعتمد على تاريخ إقفال اليوم وليس تاريخ إنشاء الفاتورة.</strong></p>";
    } else {
        echo "<p style='color: orange;'>لا يوجد فرق في هذه الحالة، قد تحتاج لاختبار فترة أخرى.</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
}

echo "<h2>ملاحظات مهمة:</h2>";
echo "<ul>";
echo "<li>الآن النظام يستخدم تاريخ إقفال اليوم (end_day.date) بدلاً من تاريخ إنشاء الفاتورة (created_at)</li>";
echo "<li>هذا يعني أن الفواتير التي تم إنشاؤها في 1/6 ولكن تنتمي ليوم العمل 31/5 ستظهر في تقرير مايو وليس يونيو</li>";
echo "<li>للفواتير غير المرتبطة بيوم عمل، سيتم استخدام تاريخ الإنشاء كما هو</li>";
echo "</ul>";
?>
