[01-Jun-2025 05:35:32 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:35:32 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:35:32 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:35:32 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:35:32 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:35:32 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total sales result: 0
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total sales result: 0
[01-Jun-2025 05:35:32 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:35:32 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:35:32 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:35:32 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:35:32 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:35:32 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total sales result: 0
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total sales result: 0
[01-Jun-2025 05:36:21 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:36:21 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:37:14 africa/cairo] Start date after conversion: 2025-05-01
[01-Jun-2025 05:37:14 africa/cairo] End date after conversion: 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Date range 'custom' converted to: 2025-05-01 - 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Final report filters: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1"}
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total sales result: 43945.00
[01-Jun-2025 05:37:14 africa/cairo] Searching for salaries between 2025-05-01 and 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:37:14 africa/cairo] Total found: 0 salary records for period 2025-05-01 to 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:37:14 africa/cairo] Searching for salaries between 2025-05-01 and 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:37:14 africa/cairo] Total found: 0 salary records for period 2025-05-01 to 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:37:14 africa/cairo] Total discounts: 205.00
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total sales result: 43945.00
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total sales result: 0
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total sales result: 0
[01-Jun-2025 05:37:14 africa/cairo] Start date after conversion: 2025-05-01
[01-Jun-2025 05:37:14 africa/cairo] End date after conversion: 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Date range 'custom' converted to: 2025-05-01 - 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Final report filters: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1"}
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total sales result: 43945.00
[01-Jun-2025 05:37:14 africa/cairo] Searching for salaries between 2025-05-01 and 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:37:14 africa/cairo] Total found: 0 salary records for period 2025-05-01 to 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:37:14 africa/cairo] Searching for salaries between 2025-05-01 and 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:37:14 africa/cairo] Total found: 0 salary records for period 2025-05-01 to 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:37:14 africa/cairo] Total discounts: 205.00
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total sales result: 43945.00
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total sales result: 0
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total sales result: 0
[01-Jun-2025 05:40:55 africa/cairo] Start date after conversion: 2025-05-01
[01-Jun-2025 05:40:55 africa/cairo] End date after conversion: 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Date range 'custom' converted to: 2025-05-01 - 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Final report filters: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1"}
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total sales result: 43945.00
[01-Jun-2025 05:40:55 africa/cairo] Searching for salaries between 2025-05-01 and 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:40:55 africa/cairo] Total found: 0 salary records for period 2025-05-01 to 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:40:55 africa/cairo] Searching for salaries between 2025-05-01 and 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:40:55 africa/cairo] Total found: 0 salary records for period 2025-05-01 to 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:40:55 africa/cairo] Total discounts: 205.00
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total sales result: 43945.00
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total sales result: 0
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total sales result: 0
[01-Jun-2025 05:40:55 africa/cairo] Start date after conversion: 2025-05-01
[01-Jun-2025 05:40:55 africa/cairo] End date after conversion: 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Date range 'custom' converted to: 2025-05-01 - 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Final report filters: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1"}
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total sales result: 43945.00
[01-Jun-2025 05:40:55 africa/cairo] Searching for salaries between 2025-05-01 and 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:40:55 africa/cairo] Total found: 0 salary records for period 2025-05-01 to 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:40:55 africa/cairo] Searching for salaries between 2025-05-01 and 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:40:55 africa/cairo] Total found: 0 salary records for period 2025-05-01 to 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:40:55 africa/cairo] Total discounts: 205.00
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total sales result: 43945.00
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total sales result: 0
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total sales result: 0
[01-Jun-2025 05:40:58 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:40:58 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:40:58 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:40:58 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:40:58 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:40:58 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total sales result: 0
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total sales result: 0
[01-Jun-2025 05:40:58 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:40:58 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:40:58 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:40:58 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:40:58 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:40:58 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total sales result: 0
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:21 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:41:21 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:41:53 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:41:53 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:53 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:41:53 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:41:53 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:41:53 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:41:53 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:41:53 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:41:53 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:41:53 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:53 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:41:53 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:53 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:41:53 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:41:53 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:41:53 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:41:53 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:41:53 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:41:53 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:41:53 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:54 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:41:54 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:54 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:41:54 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:41:54 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:41:54 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:41:54 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:41:54 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:41:54 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:41:54 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:54 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:41:54 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:54 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:41:54 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:41:54 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:41:54 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:41:54 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:41:54 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:41:54 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:41:54 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:55 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:41:55 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:55 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:41:55 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:41:55 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:41:55 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:41:55 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:41:55 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:41:55 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:41:55 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:55 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:41:55 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:55 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:41:55 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:41:55 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:41:55 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:41:55 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:41:55 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:41:55 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:41:55 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total sales result: 0
[01-Jun-2025 05:44:29 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:44:29 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:44:29 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:44:29 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:44:29 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:44:29 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total sales result: 0
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total sales result: 0
[01-Jun-2025 05:44:29 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:44:29 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:44:29 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:44:29 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:44:29 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:44:29 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total sales result: 0
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total sales result: 0
[01-Jun-2025 05:46:21 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:46:21 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:49:28 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:49:28 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:49:29 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:49:29 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:49:29 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 0
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 0
[01-Jun-2025 05:49:29 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:49:29 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:49:29 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:49:29 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 0
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 0
[01-Jun-2025 05:49:29 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:49:29 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:49:29 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:49:29 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 0
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 0
[01-Jun-2025 05:49:29 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:49:29 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:49:29 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:49:29 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 0
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 0
[01-Jun-2025 05:49:30 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:49:30 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:49:30 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:30 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:30 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:30 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:30 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:49:30 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:49:30 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:49:30 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:49:30 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:49:30 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:49:30 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:49:30 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:49:30 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:49:30 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:49:30 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:49:30 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:49:30 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:49:30 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:49:30 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:49:30 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:30 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:30 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:30 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:30 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:49:30 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:49:30 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:30 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:30 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:49:30 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:30 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:30 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:49:30 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:30 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:30 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:49:30 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:30 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:30 africa/cairo] Total sales result: 0
[01-Jun-2025 05:49:30 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:30 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:30 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:49:30 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:30 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:30 africa/cairo] Total sales result: 0
[01-Jun-2025 05:49:30 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:49:30 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:49:30 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:30 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:30 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:30 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:30 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:49:30 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:49:30 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:49:30 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:49:30 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:49:30 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:49:30 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:49:30 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:49:30 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:49:30 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:49:30 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:49:30 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:49:30 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:49:30 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:49:30 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:49:30 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:30 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:30 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:30 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:30 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:49:30 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:49:30 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:30 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:30 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:49:30 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:30 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:30 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:49:30 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:30 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:30 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:49:30 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:30 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:30 africa/cairo] Total sales result: 0
[01-Jun-2025 05:49:30 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:30 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:30 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:49:30 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:30 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:30 africa/cairo] Total sales result: 0
[01-Jun-2025 05:49:35 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:49:35 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:49:35 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:49:35 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:35 africa/cairo] Param :start_date = 2025-06-01
[01-Jun-2025 05:49:35 africa/cairo] Param :end_date = 2025-06-30
[01-Jun-2025 05:49:35 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:49:35 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:49:35 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:49:35 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:49:35 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:35 africa/cairo] Param :start_date = 2025-06-01
[01-Jun-2025 05:49:35 africa/cairo] Param :end_date = 2025-06-30
[01-Jun-2025 05:49:35 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:49:36 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:49:36 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:49:36 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:49:36 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:36 africa/cairo] Param :start_date = 2025-06-01
[01-Jun-2025 05:49:36 africa/cairo] Param :end_date = 2025-06-30
[01-Jun-2025 05:49:36 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:49:36 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:49:36 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:49:36 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:49:36 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:36 africa/cairo] Param :start_date = 2025-06-01
[01-Jun-2025 05:49:36 africa/cairo] Param :end_date = 2025-06-30
[01-Jun-2025 05:49:36 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:49:43 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:49:43 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:49:43 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:49:43 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:43 africa/cairo] Param :start_date = 2025-06-01
[01-Jun-2025 05:49:43 africa/cairo] Param :end_date = 2025-06-30
[01-Jun-2025 05:49:43 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:49:43 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:49:43 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:49:43 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:49:43 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:43 africa/cairo] Param :start_date = 2025-06-01
[01-Jun-2025 05:49:43 africa/cairo] Param :end_date = 2025-06-30
[01-Jun-2025 05:49:43 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:49:46 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:49:46 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:49:46 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:49:46 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:46 africa/cairo] Param :start_date = 2025-06-01
[01-Jun-2025 05:49:46 africa/cairo] Param :end_date = 2025-06-30
[01-Jun-2025 05:49:46 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:49:46 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:49:46 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:49:46 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:49:46 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:46 africa/cairo] Param :start_date = 2025-06-01
[01-Jun-2025 05:49:46 africa/cairo] Param :end_date = 2025-06-30
[01-Jun-2025 05:49:46 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:49:57 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:49:57 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:50:00 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:50:00 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:50:00 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:50:00 africa/cairo] API: طلب التحقق من أرقام هواتف المدراء
[01-Jun-2025 05:50:00 africa/cairo] API: التحقق من إعداد notification_admin_phone_numbers: 201022429488
201009036186
201032648474
[01-Jun-2025 05:50:00 africa/cairo] API: تم العثور على 3 رقم هاتف من الإعدادات باستخدام المفتاح notification_admin_phone_numbers
[01-Jun-2025 05:50:00 africa/cairo] API: التحقق من إعداد admin_phone_numbers: NULL
[01-Jun-2025 05:50:00 africa/cairo] API: التحقق من إعداد dmin_phone_numbers: NULL
[01-Jun-2025 05:50:00 africa/cairo] API: تم العثور على إجمالي 3 رقم هاتف من الإعدادات
[01-Jun-2025 05:50:00 africa/cairo] API: إرجاع 3 رقم هاتف للمدراء
[01-Jun-2025 05:50:00 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:50:00 africa/cairo] Background Tasks Settings: {"enable_background_tasks":"0","notification_appointment_reminder":"1","notification_enable_whatsapp":"1","whatsapp_enabled":"1"}
[01-Jun-2025 05:50:02 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:50:02 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:50:03 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:50:03 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:50:03 africa/cairo] Param :start_date = 2025-06-01
[01-Jun-2025 05:50:03 africa/cairo] Param :end_date = 2025-06-30
[01-Jun-2025 05:50:03 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:50:03 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:50:03 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:50:03 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:50:03 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:50:03 africa/cairo] Param :start_date = 2025-06-01
[01-Jun-2025 05:50:03 africa/cairo] Param :end_date = 2025-06-30
[01-Jun-2025 05:50:03 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:50:08 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:50:08 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:50:09 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:50:09 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:50:09 africa/cairo] Param :start_date = 2025-06-01
[01-Jun-2025 05:50:09 africa/cairo] Param :end_date = 2025-06-30
[01-Jun-2025 05:50:09 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:50:09 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:50:09 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:50:09 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:50:09 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:50:09 africa/cairo] Param :start_date = 2025-06-01
[01-Jun-2025 05:50:09 africa/cairo] Param :end_date = 2025-06-30
[01-Jun-2025 05:50:09 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:50:43 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:50:46 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:50:46 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:50:46 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:50:46 africa/cairo] API: طلب التحقق من أرقام هواتف المدراء
[01-Jun-2025 05:50:46 africa/cairo] API: التحقق من إعداد notification_admin_phone_numbers: 201022429488
201009036186
201032648474
[01-Jun-2025 05:50:46 africa/cairo] API: تم العثور على 3 رقم هاتف من الإعدادات باستخدام المفتاح notification_admin_phone_numbers
[01-Jun-2025 05:50:46 africa/cairo] API: التحقق من إعداد admin_phone_numbers: NULL
[01-Jun-2025 05:50:46 africa/cairo] API: التحقق من إعداد dmin_phone_numbers: NULL
[01-Jun-2025 05:50:46 africa/cairo] API: تم العثور على إجمالي 3 رقم هاتف من الإعدادات
[01-Jun-2025 05:50:46 africa/cairo] API: إرجاع 3 رقم هاتف للمدراء
[01-Jun-2025 05:50:46 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:50:46 africa/cairo] Background Tasks Settings: {"enable_background_tasks":"0","notification_appointment_reminder":"1","notification_enable_whatsapp":"1","whatsapp_enabled":"1"}
[01-Jun-2025 05:50:48 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:50:48 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:50:48 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:50:48 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:50:52 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:50:52 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:50:52 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:50:52 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:50:53 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:50:53 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:50:53 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:50:53 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:51:11 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:51:13 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:51:13 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:51:13 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:51:13 africa/cairo] API: طلب التحقق من أرقام هواتف المدراء
[01-Jun-2025 05:51:13 africa/cairo] API: التحقق من إعداد notification_admin_phone_numbers: 201022429488
201009036186
201032648474
[01-Jun-2025 05:51:13 africa/cairo] API: تم العثور على 3 رقم هاتف من الإعدادات باستخدام المفتاح notification_admin_phone_numbers
[01-Jun-2025 05:51:13 africa/cairo] API: التحقق من إعداد admin_phone_numbers: NULL
[01-Jun-2025 05:51:13 africa/cairo] API: التحقق من إعداد dmin_phone_numbers: NULL
[01-Jun-2025 05:51:13 africa/cairo] API: تم العثور على إجمالي 3 رقم هاتف من الإعدادات
[01-Jun-2025 05:51:13 africa/cairo] API: إرجاع 3 رقم هاتف للمدراء
[01-Jun-2025 05:51:13 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:51:13 africa/cairo] Background Tasks Settings: {"enable_background_tasks":"0","notification_appointment_reminder":"1","notification_enable_whatsapp":"1","whatsapp_enabled":"1"}
[01-Jun-2025 05:51:22 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:51:22 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:51:22 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:51:22 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:54:44 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:54:44 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:54:44 africa/cairo] Param :start_date = 2025-06-01
[01-Jun-2025 05:54:44 africa/cairo] Param :end_date = 2025-06-30
[01-Jun-2025 05:54:44 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:54:44 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:54:44 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:54:44 africa/cairo] Param :start_date = 2025-05-01
[01-Jun-2025 05:54:44 africa/cairo] Param :end_date = 2025-05-31
[01-Jun-2025 05:54:44 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:54:44 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:54:44 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:54:44 africa/cairo] Param :start_date = 2025-06-01
[01-Jun-2025 05:54:44 africa/cairo] Param :end_date = 2025-06-30
[01-Jun-2025 05:54:44 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:55:50 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:55:50 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:55:50 africa/cairo] Param :date_start = 2025-06-01
[01-Jun-2025 05:55:50 africa/cairo] Param :date_end = 2025-06-30
[01-Jun-2025 05:55:50 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:55:50 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:55:50 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:55:50 africa/cairo] Param :date_start = 2025-05-01
[01-Jun-2025 05:55:50 africa/cairo] Param :date_end = 2025-05-31
[01-Jun-2025 05:55:50 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:55:50 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:55:50 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:55:50 africa/cairo] Param :date_start = 2025-06-01
[01-Jun-2025 05:55:50 africa/cairo] Param :date_end = 2025-06-30
[01-Jun-2025 05:55:50 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:55:52 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:55:52 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:55:52 africa/cairo] Param :date_start = 2025-06-01
[01-Jun-2025 05:55:52 africa/cairo] Param :date_end = 2025-06-30
[01-Jun-2025 05:55:52 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:55:52 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:55:52 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:55:52 africa/cairo] Param :date_start = 2025-05-01
[01-Jun-2025 05:55:52 africa/cairo] Param :date_end = 2025-05-31
[01-Jun-2025 05:55:52 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:55:52 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:55:52 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:55:52 africa/cairo] Param :date_start = 2025-06-01
[01-Jun-2025 05:55:52 africa/cairo] Param :date_end = 2025-06-30
[01-Jun-2025 05:55:52 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:55:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:55:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:55:53 africa/cairo] Param :date_start = 2025-06-01
[01-Jun-2025 05:55:53 africa/cairo] Param :date_end = 2025-06-30
[01-Jun-2025 05:55:53 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:55:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:55:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:55:53 africa/cairo] Param :date_start = 2025-05-01
[01-Jun-2025 05:55:53 africa/cairo] Param :date_end = 2025-05-31
[01-Jun-2025 05:55:53 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:55:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:55:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:55:53 africa/cairo] Param :date_start = 2025-06-01
[01-Jun-2025 05:55:53 africa/cairo] Param :date_end = 2025-06-30
[01-Jun-2025 05:55:53 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:55:59 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:55:59 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:55:59 africa/cairo] Param :date_start = 2025-06-01
[01-Jun-2025 05:55:59 africa/cairo] Param :date_end = 2025-06-30
[01-Jun-2025 05:55:59 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:55:59 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:55:59 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:55:59 africa/cairo] Param :date_start = 2025-05-01
[01-Jun-2025 05:55:59 africa/cairo] Param :date_end = 2025-05-31
[01-Jun-2025 05:55:59 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:55:59 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:55:59 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:55:59 africa/cairo] Param :date_start = 2025-06-01
[01-Jun-2025 05:55:59 africa/cairo] Param :date_end = 2025-06-30
[01-Jun-2025 05:55:59 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:56:31 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:56:31 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:56:31 africa/cairo] Param :date_start = 2025-06-01
[01-Jun-2025 05:56:31 africa/cairo] Param :date_end = 2025-06-30
[01-Jun-2025 05:56:31 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:56:31 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:56:31 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:56:31 africa/cairo] Param :date_start = 2025-05-01
[01-Jun-2025 05:56:31 africa/cairo] Param :date_end = 2025-05-31
[01-Jun-2025 05:56:31 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:56:31 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:56:31 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:56:31 africa/cairo] Param :date_start = 2025-06-01
[01-Jun-2025 05:56:31 africa/cairo] Param :date_end = 2025-06-30
[01-Jun-2025 05:56:31 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
