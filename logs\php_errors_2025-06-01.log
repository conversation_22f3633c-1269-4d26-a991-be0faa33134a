[01-Jun-2025 05:35:32 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:35:32 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:35:32 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:35:32 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:35:32 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:35:32 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total sales result: 0
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total sales result: 0
[01-Jun-2025 05:35:32 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:35:32 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:35:32 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:35:32 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:35:32 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:35:32 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total sales result: 0
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total sales result: 0
[01-Jun-2025 05:36:21 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:36:21 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:37:14 africa/cairo] Start date after conversion: 2025-05-01
[01-Jun-2025 05:37:14 africa/cairo] End date after conversion: 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Date range 'custom' converted to: 2025-05-01 - 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Final report filters: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1"}
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total sales result: 43945.00
[01-Jun-2025 05:37:14 africa/cairo] Searching for salaries between 2025-05-01 and 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:37:14 africa/cairo] Total found: 0 salary records for period 2025-05-01 to 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:37:14 africa/cairo] Searching for salaries between 2025-05-01 and 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:37:14 africa/cairo] Total found: 0 salary records for period 2025-05-01 to 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:37:14 africa/cairo] Total discounts: 205.00
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total sales result: 43945.00
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total sales result: 0
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total sales result: 0
[01-Jun-2025 05:37:14 africa/cairo] Start date after conversion: 2025-05-01
[01-Jun-2025 05:37:14 africa/cairo] End date after conversion: 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Date range 'custom' converted to: 2025-05-01 - 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Final report filters: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1"}
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total sales result: 43945.00
[01-Jun-2025 05:37:14 africa/cairo] Searching for salaries between 2025-05-01 and 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:37:14 africa/cairo] Total found: 0 salary records for period 2025-05-01 to 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:37:14 africa/cairo] Searching for salaries between 2025-05-01 and 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:37:14 africa/cairo] Total found: 0 salary records for period 2025-05-01 to 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:37:14 africa/cairo] Total discounts: 205.00
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total sales result: 43945.00
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total sales result: 0
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total sales result: 0
[01-Jun-2025 05:40:55 africa/cairo] Start date after conversion: 2025-05-01
[01-Jun-2025 05:40:55 africa/cairo] End date after conversion: 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Date range 'custom' converted to: 2025-05-01 - 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Final report filters: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1"}
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total sales result: 43945.00
[01-Jun-2025 05:40:55 africa/cairo] Searching for salaries between 2025-05-01 and 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:40:55 africa/cairo] Total found: 0 salary records for period 2025-05-01 to 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:40:55 africa/cairo] Searching for salaries between 2025-05-01 and 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:40:55 africa/cairo] Total found: 0 salary records for period 2025-05-01 to 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:40:55 africa/cairo] Total discounts: 205.00
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total sales result: 43945.00
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total sales result: 0
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total sales result: 0
[01-Jun-2025 05:40:55 africa/cairo] Start date after conversion: 2025-05-01
[01-Jun-2025 05:40:55 africa/cairo] End date after conversion: 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Date range 'custom' converted to: 2025-05-01 - 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Final report filters: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1"}
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total sales result: 43945.00
[01-Jun-2025 05:40:55 africa/cairo] Searching for salaries between 2025-05-01 and 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:40:55 africa/cairo] Total found: 0 salary records for period 2025-05-01 to 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:40:55 africa/cairo] Searching for salaries between 2025-05-01 and 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:40:55 africa/cairo] Total found: 0 salary records for period 2025-05-01 to 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:40:55 africa/cairo] Total discounts: 205.00
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total sales result: 43945.00
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total sales result: 0
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total sales result: 0
[01-Jun-2025 05:40:58 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:40:58 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:40:58 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:40:58 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:40:58 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:40:58 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total sales result: 0
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total sales result: 0
[01-Jun-2025 05:40:58 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:40:58 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:40:58 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:40:58 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:40:58 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:40:58 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total sales result: 0
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:21 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:41:21 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:41:53 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:41:53 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:53 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:41:53 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:41:53 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:41:53 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:41:53 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:41:53 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:41:53 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:41:53 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:53 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:41:53 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:53 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:41:53 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:41:53 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:41:53 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:41:53 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:41:53 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:41:53 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:41:53 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:54 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:41:54 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:54 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:41:54 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:41:54 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:41:54 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:41:54 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:41:54 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:41:54 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:41:54 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:54 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:41:54 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:54 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:41:54 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:41:54 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:41:54 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:41:54 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:41:54 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:41:54 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:41:54 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:55 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:41:55 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:55 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:41:55 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:41:55 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:41:55 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:41:55 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:41:55 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:41:55 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:41:55 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:55 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:41:55 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:55 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:41:55 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:41:55 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:41:55 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:41:55 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:41:55 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:41:55 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:41:55 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total sales result: 0
[01-Jun-2025 05:44:29 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:44:29 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:44:29 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:44:29 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:44:29 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:44:29 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total sales result: 0
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total sales result: 0
[01-Jun-2025 05:44:29 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:44:29 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:44:29 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:44:29 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:44:29 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:44:29 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total sales result: 0
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total sales result: 0
[01-Jun-2025 05:46:21 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:46:21 africa/cairo] Loaded 52 settings into cache
