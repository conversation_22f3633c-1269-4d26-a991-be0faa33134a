[01-Jun-2025 05:35:32 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:35:32 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:35:32 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:35:32 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:35:32 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:35:32 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total sales result: 0
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total sales result: 0
[01-Jun-2025 05:35:32 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:35:32 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:35:32 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:35:32 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:35:32 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:35:32 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:35:32 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:35:32 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total sales result: 0
[01-Jun-2025 05:35:32 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:35:32 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:35:32 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:35:32 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:35:32 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:35:32 africa/cairo] Total sales result: 0
[01-Jun-2025 05:36:21 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:36:21 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:37:14 africa/cairo] Start date after conversion: 2025-05-01
[01-Jun-2025 05:37:14 africa/cairo] End date after conversion: 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Date range 'custom' converted to: 2025-05-01 - 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Final report filters: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1"}
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total sales result: 43945.00
[01-Jun-2025 05:37:14 africa/cairo] Searching for salaries between 2025-05-01 and 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:37:14 africa/cairo] Total found: 0 salary records for period 2025-05-01 to 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:37:14 africa/cairo] Searching for salaries between 2025-05-01 and 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:37:14 africa/cairo] Total found: 0 salary records for period 2025-05-01 to 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:37:14 africa/cairo] Total discounts: 205.00
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total sales result: 43945.00
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total sales result: 0
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total sales result: 0
[01-Jun-2025 05:37:14 africa/cairo] Start date after conversion: 2025-05-01
[01-Jun-2025 05:37:14 africa/cairo] End date after conversion: 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Date range 'custom' converted to: 2025-05-01 - 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Final report filters: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1"}
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total sales result: 43945.00
[01-Jun-2025 05:37:14 africa/cairo] Searching for salaries between 2025-05-01 and 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:37:14 africa/cairo] Total found: 0 salary records for period 2025-05-01 to 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:37:14 africa/cairo] Searching for salaries between 2025-05-01 and 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:37:14 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:37:14 africa/cairo] Total found: 0 salary records for period 2025-05-01 to 2025-05-31
[01-Jun-2025 05:37:14 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:37:14 africa/cairo] Total discounts: 205.00
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total sales result: 43945.00
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total sales result: 0
[01-Jun-2025 05:37:14 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:37:14 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:37:14 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:37:14 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:37:14 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:37:14 africa/cairo] Total sales result: 0
[01-Jun-2025 05:40:55 africa/cairo] Start date after conversion: 2025-05-01
[01-Jun-2025 05:40:55 africa/cairo] End date after conversion: 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Date range 'custom' converted to: 2025-05-01 - 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Final report filters: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1"}
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total sales result: 43945.00
[01-Jun-2025 05:40:55 africa/cairo] Searching for salaries between 2025-05-01 and 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:40:55 africa/cairo] Total found: 0 salary records for period 2025-05-01 to 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:40:55 africa/cairo] Searching for salaries between 2025-05-01 and 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:40:55 africa/cairo] Total found: 0 salary records for period 2025-05-01 to 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:40:55 africa/cairo] Total discounts: 205.00
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total sales result: 43945.00
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total sales result: 0
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total sales result: 0
[01-Jun-2025 05:40:55 africa/cairo] Start date after conversion: 2025-05-01
[01-Jun-2025 05:40:55 africa/cairo] End date after conversion: 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Date range 'custom' converted to: 2025-05-01 - 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Final report filters: {"start_date":"2025-05-01","end_date":"2025-05-31","date_range":"custom","branch_id":"1"}
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total sales result: 43945.00
[01-Jun-2025 05:40:55 africa/cairo] Searching for salaries between 2025-05-01 and 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:40:55 africa/cairo] Total found: 0 salary records for period 2025-05-01 to 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:40:55 africa/cairo] Searching for salaries between 2025-05-01 and 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:40:55 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:40:55 africa/cairo] Total found: 0 salary records for period 2025-05-01 to 2025-05-31
[01-Jun-2025 05:40:55 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:40:55 africa/cairo] Total discounts: 205.00
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total sales result: 43945.00
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total sales result: 0
[01-Jun-2025 05:40:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:55 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:40:55 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 05:40:55 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 05:40:55 africa/cairo] Total sales result: 0
[01-Jun-2025 05:40:58 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:40:58 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:40:58 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:40:58 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:40:58 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:40:58 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total sales result: 0
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total sales result: 0
[01-Jun-2025 05:40:58 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:40:58 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:40:58 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:40:58 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:40:58 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:40:58 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:40:58 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:40:58 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total sales result: 0
[01-Jun-2025 05:40:58 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:40:58 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:40:58 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:40:58 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:40:58 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:40:58 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:21 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:41:21 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:41:53 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:41:53 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:53 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:41:53 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:41:53 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:41:53 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:41:53 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:41:53 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:41:53 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:41:53 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:53 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:41:53 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:53 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:41:53 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:41:53 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:41:53 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:41:53 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:41:53 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:41:53 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:41:53 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:53 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:41:53 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:53 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:53 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:54 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:41:54 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:54 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:41:54 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:41:54 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:41:54 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:41:54 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:41:54 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:41:54 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:41:54 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:54 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:41:54 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:54 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:41:54 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:41:54 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:41:54 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:41:54 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:41:54 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:41:54 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:41:54 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:54 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:54 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:41:54 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:54 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:54 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:55 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:41:55 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:55 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:41:55 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:41:55 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:41:55 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:41:55 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:41:55 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:41:55 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:41:55 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:55 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:41:55 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:55 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:41:55 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:41:55 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:41:55 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:41:55 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:41:55 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:41:55 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:41:55 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total sales result: 0
[01-Jun-2025 05:41:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:41:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:41:55 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:41:55 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:41:55 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:41:55 africa/cairo] Total sales result: 0
[01-Jun-2025 05:44:29 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:44:29 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:44:29 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:44:29 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:44:29 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:44:29 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total sales result: 0
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total sales result: 0
[01-Jun-2025 05:44:29 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:44:29 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:44:29 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:44:29 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:44:29 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:44:29 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:44:29 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:44:29 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total sales result: 0
[01-Jun-2025 05:44:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(final_amount) FROM invoices i WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:44:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:44:29 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:44:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:44:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:44:29 africa/cairo] Total sales result: 0
[01-Jun-2025 05:46:21 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:46:21 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:49:28 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:49:28 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:49:29 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:49:29 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:49:29 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 0
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 0
[01-Jun-2025 05:49:29 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:49:29 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:49:29 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:49:29 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 0
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 0
[01-Jun-2025 05:49:29 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:49:29 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:49:29 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:49:29 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 0
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 0
[01-Jun-2025 05:49:29 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:49:29 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:49:29 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:49:29 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:49:29 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:49:29 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:49:29 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 0
[01-Jun-2025 05:49:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:29 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:29 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:49:29 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:29 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:29 africa/cairo] Total sales result: 0
[01-Jun-2025 05:49:30 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:49:30 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:49:30 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:30 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:30 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:30 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:30 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:49:30 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:49:30 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:49:30 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:49:30 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:49:30 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:49:30 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:49:30 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:49:30 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:49:30 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:49:30 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:49:30 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:49:30 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:49:30 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:49:30 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:49:30 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:30 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:30 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:30 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:30 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:49:30 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:49:30 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:30 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:30 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:49:30 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:30 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:30 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:49:30 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:30 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:30 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:49:30 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:30 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:30 africa/cairo] Total sales result: 0
[01-Jun-2025 05:49:30 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:30 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:30 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:49:30 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:30 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:30 africa/cairo] Total sales result: 0
[01-Jun-2025 05:49:30 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:49:30 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:49:30 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:30 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:30 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:30 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:30 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:49:30 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:49:30 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:49:30 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:49:30 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:49:30 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:49:30 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:49:30 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:49:30 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 05:49:30 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 05:49:30 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 05:49:30 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 05:49:30 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 05:49:30 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 05:49:30 africa/cairo] Total salaries amount: 0
[01-Jun-2025 05:49:30 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:30 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:30 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:30 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:30 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 05:49:30 africa/cairo] Total discounts: 0.00
[01-Jun-2025 05:49:30 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:30 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:30 africa/cairo] Param :payment_method = cash
[01-Jun-2025 05:49:30 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:30 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:30 africa/cairo] Total sales result: 805.00
[01-Jun-2025 05:49:30 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:30 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:30 africa/cairo] Param :payment_method = card
[01-Jun-2025 05:49:30 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:30 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:30 africa/cairo] Total sales result: 0
[01-Jun-2025 05:49:30 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND i.payment_method = :payment_method AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 05:49:30 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:30 africa/cairo] Param :payment_method = other
[01-Jun-2025 05:49:30 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 05:49:30 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 05:49:30 africa/cairo] Total sales result: 0
[01-Jun-2025 05:49:35 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:49:35 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:49:35 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:49:35 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:35 africa/cairo] Param :start_date = 2025-06-01
[01-Jun-2025 05:49:35 africa/cairo] Param :end_date = 2025-06-30
[01-Jun-2025 05:49:35 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:49:35 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:49:35 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:49:35 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:49:35 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:35 africa/cairo] Param :start_date = 2025-06-01
[01-Jun-2025 05:49:35 africa/cairo] Param :end_date = 2025-06-30
[01-Jun-2025 05:49:35 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:49:36 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:49:36 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:49:36 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:49:36 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:36 africa/cairo] Param :start_date = 2025-06-01
[01-Jun-2025 05:49:36 africa/cairo] Param :end_date = 2025-06-30
[01-Jun-2025 05:49:36 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:49:36 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:49:36 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:49:36 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:49:36 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:36 africa/cairo] Param :start_date = 2025-06-01
[01-Jun-2025 05:49:36 africa/cairo] Param :end_date = 2025-06-30
[01-Jun-2025 05:49:36 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:49:43 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:49:43 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:49:43 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:49:43 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:43 africa/cairo] Param :start_date = 2025-06-01
[01-Jun-2025 05:49:43 africa/cairo] Param :end_date = 2025-06-30
[01-Jun-2025 05:49:43 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:49:43 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:49:43 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:49:43 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:49:43 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:43 africa/cairo] Param :start_date = 2025-06-01
[01-Jun-2025 05:49:43 africa/cairo] Param :end_date = 2025-06-30
[01-Jun-2025 05:49:43 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:49:46 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:49:46 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:49:46 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:49:46 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:46 africa/cairo] Param :start_date = 2025-06-01
[01-Jun-2025 05:49:46 africa/cairo] Param :end_date = 2025-06-30
[01-Jun-2025 05:49:46 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:49:46 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:49:46 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:49:46 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:49:46 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:49:46 africa/cairo] Param :start_date = 2025-06-01
[01-Jun-2025 05:49:46 africa/cairo] Param :end_date = 2025-06-30
[01-Jun-2025 05:49:46 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:49:57 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:49:57 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:50:00 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:50:00 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:50:00 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:50:00 africa/cairo] API: طلب التحقق من أرقام هواتف المدراء
[01-Jun-2025 05:50:00 africa/cairo] API: التحقق من إعداد notification_admin_phone_numbers: 201022429488
201009036186
201032648474
[01-Jun-2025 05:50:00 africa/cairo] API: تم العثور على 3 رقم هاتف من الإعدادات باستخدام المفتاح notification_admin_phone_numbers
[01-Jun-2025 05:50:00 africa/cairo] API: التحقق من إعداد admin_phone_numbers: NULL
[01-Jun-2025 05:50:00 africa/cairo] API: التحقق من إعداد dmin_phone_numbers: NULL
[01-Jun-2025 05:50:00 africa/cairo] API: تم العثور على إجمالي 3 رقم هاتف من الإعدادات
[01-Jun-2025 05:50:00 africa/cairo] API: إرجاع 3 رقم هاتف للمدراء
[01-Jun-2025 05:50:00 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:50:00 africa/cairo] Background Tasks Settings: {"enable_background_tasks":"0","notification_appointment_reminder":"1","notification_enable_whatsapp":"1","whatsapp_enabled":"1"}
[01-Jun-2025 05:50:02 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:50:02 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:50:03 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:50:03 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:50:03 africa/cairo] Param :start_date = 2025-06-01
[01-Jun-2025 05:50:03 africa/cairo] Param :end_date = 2025-06-30
[01-Jun-2025 05:50:03 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:50:03 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:50:03 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:50:03 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:50:03 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:50:03 africa/cairo] Param :start_date = 2025-06-01
[01-Jun-2025 05:50:03 africa/cairo] Param :end_date = 2025-06-30
[01-Jun-2025 05:50:03 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:50:08 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:50:08 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:50:09 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:50:09 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:50:09 africa/cairo] Param :start_date = 2025-06-01
[01-Jun-2025 05:50:09 africa/cairo] Param :end_date = 2025-06-30
[01-Jun-2025 05:50:09 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:50:09 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:50:09 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:50:09 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:50:09 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:50:09 africa/cairo] Param :start_date = 2025-06-01
[01-Jun-2025 05:50:09 africa/cairo] Param :end_date = 2025-06-30
[01-Jun-2025 05:50:09 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:50:43 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:50:46 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:50:46 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:50:46 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:50:46 africa/cairo] API: طلب التحقق من أرقام هواتف المدراء
[01-Jun-2025 05:50:46 africa/cairo] API: التحقق من إعداد notification_admin_phone_numbers: 201022429488
201009036186
201032648474
[01-Jun-2025 05:50:46 africa/cairo] API: تم العثور على 3 رقم هاتف من الإعدادات باستخدام المفتاح notification_admin_phone_numbers
[01-Jun-2025 05:50:46 africa/cairo] API: التحقق من إعداد admin_phone_numbers: NULL
[01-Jun-2025 05:50:46 africa/cairo] API: التحقق من إعداد dmin_phone_numbers: NULL
[01-Jun-2025 05:50:46 africa/cairo] API: تم العثور على إجمالي 3 رقم هاتف من الإعدادات
[01-Jun-2025 05:50:46 africa/cairo] API: إرجاع 3 رقم هاتف للمدراء
[01-Jun-2025 05:50:46 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:50:46 africa/cairo] Background Tasks Settings: {"enable_background_tasks":"0","notification_appointment_reminder":"1","notification_enable_whatsapp":"1","whatsapp_enabled":"1"}
[01-Jun-2025 05:50:48 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:50:48 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:50:48 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:50:48 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:50:52 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:50:52 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:50:52 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:50:52 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:50:53 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:50:53 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:50:53 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:50:53 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:51:11 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:51:13 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:51:13 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:51:13 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:51:13 africa/cairo] API: طلب التحقق من أرقام هواتف المدراء
[01-Jun-2025 05:51:13 africa/cairo] API: التحقق من إعداد notification_admin_phone_numbers: 201022429488
201009036186
201032648474
[01-Jun-2025 05:51:13 africa/cairo] API: تم العثور على 3 رقم هاتف من الإعدادات باستخدام المفتاح notification_admin_phone_numbers
[01-Jun-2025 05:51:13 africa/cairo] API: التحقق من إعداد admin_phone_numbers: NULL
[01-Jun-2025 05:51:13 africa/cairo] API: التحقق من إعداد dmin_phone_numbers: NULL
[01-Jun-2025 05:51:13 africa/cairo] API: تم العثور على إجمالي 3 رقم هاتف من الإعدادات
[01-Jun-2025 05:51:13 africa/cairo] API: إرجاع 3 رقم هاتف للمدراء
[01-Jun-2025 05:51:13 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 05:51:13 africa/cairo] Background Tasks Settings: {"enable_background_tasks":"0","notification_appointment_reminder":"1","notification_enable_whatsapp":"1","whatsapp_enabled":"1"}
[01-Jun-2025 05:51:22 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:51:22 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:51:22 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 05:51:22 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 05:54:44 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:54:44 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:54:44 africa/cairo] Param :start_date = 2025-06-01
[01-Jun-2025 05:54:44 africa/cairo] Param :end_date = 2025-06-30
[01-Jun-2025 05:54:44 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:54:44 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:54:44 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:54:44 africa/cairo] Param :start_date = 2025-05-01
[01-Jun-2025 05:54:44 africa/cairo] Param :end_date = 2025-05-31
[01-Jun-2025 05:54:44 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:54:44 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[01-Jun-2025 05:54:44 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:54:44 africa/cairo] Param :start_date = 2025-06-01
[01-Jun-2025 05:54:44 africa/cairo] Param :end_date = 2025-06-30
[01-Jun-2025 05:54:44 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:55:50 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:55:50 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:55:50 africa/cairo] Param :date_start = 2025-06-01
[01-Jun-2025 05:55:50 africa/cairo] Param :date_end = 2025-06-30
[01-Jun-2025 05:55:50 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:55:50 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:55:50 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:55:50 africa/cairo] Param :date_start = 2025-05-01
[01-Jun-2025 05:55:50 africa/cairo] Param :date_end = 2025-05-31
[01-Jun-2025 05:55:50 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:55:50 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:55:50 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:55:50 africa/cairo] Param :date_start = 2025-06-01
[01-Jun-2025 05:55:50 africa/cairo] Param :date_end = 2025-06-30
[01-Jun-2025 05:55:50 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:55:52 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:55:52 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:55:52 africa/cairo] Param :date_start = 2025-06-01
[01-Jun-2025 05:55:52 africa/cairo] Param :date_end = 2025-06-30
[01-Jun-2025 05:55:52 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:55:52 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:55:52 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:55:52 africa/cairo] Param :date_start = 2025-05-01
[01-Jun-2025 05:55:52 africa/cairo] Param :date_end = 2025-05-31
[01-Jun-2025 05:55:52 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:55:52 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:55:52 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:55:52 africa/cairo] Param :date_start = 2025-06-01
[01-Jun-2025 05:55:52 africa/cairo] Param :date_end = 2025-06-30
[01-Jun-2025 05:55:52 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:55:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:55:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:55:53 africa/cairo] Param :date_start = 2025-06-01
[01-Jun-2025 05:55:53 africa/cairo] Param :date_end = 2025-06-30
[01-Jun-2025 05:55:53 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:55:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:55:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:55:53 africa/cairo] Param :date_start = 2025-05-01
[01-Jun-2025 05:55:53 africa/cairo] Param :date_end = 2025-05-31
[01-Jun-2025 05:55:53 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:55:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:55:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:55:53 africa/cairo] Param :date_start = 2025-06-01
[01-Jun-2025 05:55:53 africa/cairo] Param :date_end = 2025-06-30
[01-Jun-2025 05:55:53 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:55:59 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:55:59 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:55:59 africa/cairo] Param :date_start = 2025-06-01
[01-Jun-2025 05:55:59 africa/cairo] Param :date_end = 2025-06-30
[01-Jun-2025 05:55:59 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:55:59 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:55:59 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:55:59 africa/cairo] Param :date_start = 2025-05-01
[01-Jun-2025 05:55:59 africa/cairo] Param :date_end = 2025-05-31
[01-Jun-2025 05:55:59 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:55:59 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:55:59 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:55:59 africa/cairo] Param :date_start = 2025-06-01
[01-Jun-2025 05:55:59 africa/cairo] Param :date_end = 2025-06-30
[01-Jun-2025 05:55:59 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:56:31 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:56:31 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:56:31 africa/cairo] Param :date_start = 2025-06-01
[01-Jun-2025 05:56:31 africa/cairo] Param :date_end = 2025-06-30
[01-Jun-2025 05:56:31 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:56:31 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:56:31 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:56:31 africa/cairo] Param :date_start = 2025-05-01
[01-Jun-2025 05:56:31 africa/cairo] Param :date_end = 2025-05-31
[01-Jun-2025 05:56:31 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:56:31 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:56:31 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:56:31 africa/cairo] Param :date_start = 2025-06-01
[01-Jun-2025 05:56:31 africa/cairo] Param :date_end = 2025-06-30
[01-Jun-2025 05:56:31 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:57:37 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:57:37 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:57:37 africa/cairo] Param :date_start = 2025-06-01
[01-Jun-2025 05:57:37 africa/cairo] Param :date_end = 2025-06-30
[01-Jun-2025 05:57:37 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:57:37 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:57:37 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:57:37 africa/cairo] Param :date_start = 2025-05-01
[01-Jun-2025 05:57:37 africa/cairo] Param :date_end = 2025-05-31
[01-Jun-2025 05:57:37 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:57:37 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:57:37 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:57:37 africa/cairo] Param :date_start = 2025-06-01
[01-Jun-2025 05:57:37 africa/cairo] Param :date_end = 2025-06-30
[01-Jun-2025 05:57:37 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:57:38 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:57:38 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:57:38 africa/cairo] Param :date_start = 2025-06-01
[01-Jun-2025 05:57:38 africa/cairo] Param :date_end = 2025-06-30
[01-Jun-2025 05:57:38 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:57:38 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:57:38 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:57:38 africa/cairo] Param :date_start = 2025-05-01
[01-Jun-2025 05:57:38 africa/cairo] Param :date_end = 2025-05-31
[01-Jun-2025 05:57:38 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:57:38 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[01-Jun-2025 05:57:38 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:57:38 africa/cairo] Param :date_start = 2025-06-01
[01-Jun-2025 05:57:38 africa/cairo] Param :date_end = 2025-06-30
[01-Jun-2025 05:57:38 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:57:51 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:57:51 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:57:51 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 05:57:51 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 05:57:51 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:57:51 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:57:51 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:57:51 africa/cairo] Param :sales_start_date = 2025-05-01
[01-Jun-2025 05:57:51 africa/cairo] Param :sales_end_date = 2025-05-31
[01-Jun-2025 05:57:51 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:57:51 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:57:51 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:57:51 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 05:57:51 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 05:57:51 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:57:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:57:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:57:53 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 05:57:53 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 05:57:53 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:57:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:57:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:57:53 africa/cairo] Param :sales_start_date = 2025-05-01
[01-Jun-2025 05:57:53 africa/cairo] Param :sales_end_date = 2025-05-31
[01-Jun-2025 05:57:53 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:57:53 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:57:53 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:57:53 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 05:57:53 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 05:57:53 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:57:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:57:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:57:55 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 05:57:55 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 05:57:55 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:57:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:57:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:57:55 africa/cairo] Param :sales_start_date = 2025-05-01
[01-Jun-2025 05:57:55 africa/cairo] Param :sales_end_date = 2025-05-31
[01-Jun-2025 05:57:55 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:57:55 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:57:55 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:57:55 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 05:57:55 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 05:57:55 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:57:56 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:57:56 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:57:56 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 05:57:56 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 05:57:56 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:57:56 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:57:56 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:57:56 africa/cairo] Param :sales_start_date = 2025-05-01
[01-Jun-2025 05:57:56 africa/cairo] Param :sales_end_date = 2025-05-31
[01-Jun-2025 05:57:56 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:57:56 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:57:56 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:57:56 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 05:57:56 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 05:57:56 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:57:59 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:57:59 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:57:59 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 05:57:59 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 05:57:59 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:57:59 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:57:59 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:57:59 africa/cairo] Param :sales_start_date = 2025-05-01
[01-Jun-2025 05:57:59 africa/cairo] Param :sales_end_date = 2025-05-31
[01-Jun-2025 05:57:59 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:57:59 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:57:59 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:57:59 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 05:57:59 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 05:57:59 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:58:02 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:58:02 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:58:02 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 05:58:02 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 05:58:02 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:58:02 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:58:02 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:58:02 africa/cairo] Param :sales_start_date = 2025-05-01
[01-Jun-2025 05:58:02 africa/cairo] Param :sales_end_date = 2025-05-31
[01-Jun-2025 05:58:02 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:58:02 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:58:02 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:58:02 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 05:58:02 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 05:58:02 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:58:03 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:58:03 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:58:03 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 05:58:03 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 05:58:03 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:58:03 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:58:03 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:58:03 africa/cairo] Param :sales_start_date = 2025-05-01
[01-Jun-2025 05:58:03 africa/cairo] Param :sales_end_date = 2025-05-31
[01-Jun-2025 05:58:03 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:58:03 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:58:03 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:58:03 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 05:58:03 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 05:58:03 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:58:04 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:58:04 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:58:04 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 05:58:04 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 05:58:04 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:58:04 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:58:04 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:58:04 africa/cairo] Param :sales_start_date = 2025-05-01
[01-Jun-2025 05:58:04 africa/cairo] Param :sales_end_date = 2025-05-31
[01-Jun-2025 05:58:04 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:58:04 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:58:04 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:58:04 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 05:58:04 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 05:58:04 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:58:05 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:58:05 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:58:05 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 05:58:05 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 05:58:05 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:58:05 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:58:05 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:58:05 africa/cairo] Param :sales_start_date = 2025-05-01
[01-Jun-2025 05:58:05 africa/cairo] Param :sales_end_date = 2025-05-31
[01-Jun-2025 05:58:05 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:58:05 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:58:05 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:58:05 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 05:58:05 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 05:58:05 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:58:06 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:58:06 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:58:06 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 05:58:06 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 05:58:06 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:58:06 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:58:06 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:58:06 africa/cairo] Param :sales_start_date = 2025-05-01
[01-Jun-2025 05:58:06 africa/cairo] Param :sales_end_date = 2025-05-31
[01-Jun-2025 05:58:06 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:58:06 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:58:06 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:58:06 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 05:58:06 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 05:58:06 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:58:39 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:58:39 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:58:39 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 05:58:39 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 05:58:39 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:58:39 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:58:39 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:58:39 africa/cairo] Param :sales_start_date = 2025-05-01
[01-Jun-2025 05:58:39 africa/cairo] Param :sales_end_date = 2025-05-31
[01-Jun-2025 05:58:39 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 05:58:39 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 05:58:39 africa/cairo] Param :branch_id = 1
[01-Jun-2025 05:58:39 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 05:58:39 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 05:58:39 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:00:07 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:00:07 africa/cairo] Param :branch_id = 1
[01-Jun-2025 06:00:07 africa/cairo] Param :sales_start_date = 2025-05-01
[01-Jun-2025 06:00:07 africa/cairo] Param :sales_end_date = 2025-05-31
[01-Jun-2025 06:00:07 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:00:07 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:00:07 africa/cairo] Param :branch_id = 1
[01-Jun-2025 06:00:07 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 06:00:07 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 06:00:07 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:00:09 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:00:09 africa/cairo] Param :branch_id = 1
[01-Jun-2025 06:00:09 africa/cairo] Param :sales_start_date = 2025-05-01
[01-Jun-2025 06:00:09 africa/cairo] Param :sales_end_date = 2025-05-31
[01-Jun-2025 06:00:09 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:00:09 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:00:09 africa/cairo] Param :branch_id = 1
[01-Jun-2025 06:00:09 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 06:00:09 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 06:00:09 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:00:15 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:00:15 africa/cairo] Param :branch_id = 1
[01-Jun-2025 06:00:15 africa/cairo] Param :sales_start_date = 2025-05-01
[01-Jun-2025 06:00:15 africa/cairo] Param :sales_end_date = 2025-05-31
[01-Jun-2025 06:00:15 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:00:15 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:00:15 africa/cairo] Param :branch_id = 1
[01-Jun-2025 06:00:15 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 06:00:15 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 06:00:15 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:00:16 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:00:16 africa/cairo] Param :branch_id = 1
[01-Jun-2025 06:00:16 africa/cairo] Param :sales_start_date = 2025-05-01
[01-Jun-2025 06:00:16 africa/cairo] Param :sales_end_date = 2025-05-31
[01-Jun-2025 06:00:16 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:00:16 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:00:16 africa/cairo] Param :branch_id = 1
[01-Jun-2025 06:00:16 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 06:00:16 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 06:00:16 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:00:54 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:00:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 06:00:54 africa/cairo] Param :sales_start_date = 2025-05-01
[01-Jun-2025 06:00:54 africa/cairo] Param :sales_end_date = 2025-05-31
[01-Jun-2025 06:00:54 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:00:54 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:00:54 africa/cairo] Param :branch_id = 1
[01-Jun-2025 06:00:54 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 06:00:54 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 06:00:54 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:01:27 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:01:27 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:01:27 africa/cairo] Param :sales_start_date = 2025-05-01
[01-Jun-2025 06:01:27 africa/cairo] Param :sales_end_date = 2025-05-31
[01-Jun-2025 06:01:27 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:01:27 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:01:27 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:01:27 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 06:01:27 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 06:01:27 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:01:31 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:01:31 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:01:31 africa/cairo] Param :sales_start_date = 2025-05-01
[01-Jun-2025 06:01:31 africa/cairo] Param :sales_end_date = 2025-05-31
[01-Jun-2025 06:01:31 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:01:31 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:01:31 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:01:31 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 06:01:31 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 06:01:31 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:01:47 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:01:47 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:01:47 africa/cairo] Param :sales_start_date = 2025-05-01
[01-Jun-2025 06:01:47 africa/cairo] Param :sales_end_date = 2025-05-31
[01-Jun-2025 06:01:47 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:01:47 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:01:47 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:01:47 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 06:01:47 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 06:01:47 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:01:57 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:01:57 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:01:57 africa/cairo] Param :sales_start_date = 2025-05-01
[01-Jun-2025 06:01:57 africa/cairo] Param :sales_end_date = 2025-05-31
[01-Jun-2025 06:01:57 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:01:57 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:01:57 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:01:57 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 06:01:57 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 06:01:57 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:01:58 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:01:58 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:01:58 africa/cairo] Param :sales_start_date = 2025-05-01
[01-Jun-2025 06:01:58 africa/cairo] Param :sales_end_date = 2025-05-31
[01-Jun-2025 06:01:58 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:01:58 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:01:58 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:01:58 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 06:01:58 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 06:01:58 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:02:02 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:02:02 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:02:02 africa/cairo] Param :sales_start_date = 2025-05-01
[01-Jun-2025 06:02:02 africa/cairo] Param :sales_end_date = 2025-05-31
[01-Jun-2025 06:02:02 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:02:02 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:02:02 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:02:02 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 06:02:02 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 06:02:02 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:02:07 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:02:07 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:02:07 africa/cairo] Param :sales_start_date = 2025-05-01
[01-Jun-2025 06:02:07 africa/cairo] Param :sales_end_date = 2025-05-31
[01-Jun-2025 06:02:07 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:02:07 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:02:07 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:02:07 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 06:02:07 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 06:02:07 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:02:11 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:02:11 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:02:11 africa/cairo] Param :sales_start_date = 2025-05-01
[01-Jun-2025 06:02:11 africa/cairo] Param :sales_end_date = 2025-05-31
[01-Jun-2025 06:02:11 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:02:11 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:02:11 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:02:11 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 06:02:11 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 06:02:11 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:02:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:02:29 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:02:29 africa/cairo] Param :sales_start_date = 2025-05-01
[01-Jun-2025 06:02:29 africa/cairo] Param :sales_end_date = 2025-05-31
[01-Jun-2025 06:02:29 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:02:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:02:29 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:02:29 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 06:02:29 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 06:02:29 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:03:17 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:03:17 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:03:17 africa/cairo] Param :sales_start_date = 2025-05-01
[01-Jun-2025 06:03:17 africa/cairo] Param :sales_end_date = 2025-05-31
[01-Jun-2025 06:03:17 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:03:17 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:03:17 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:03:17 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 06:03:17 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 06:03:17 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:03:23 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:03:23 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:03:23 africa/cairo] Param :sales_start_date = 2025-05-01
[01-Jun-2025 06:03:23 africa/cairo] Param :sales_end_date = 2025-05-31
[01-Jun-2025 06:03:23 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:03:23 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:03:23 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:03:23 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 06:03:23 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 06:03:23 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:03:51 africa/cairo] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php on line 154
[01-Jun-2025 06:03:51 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:03:51 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:03:51 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 06:03:51 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 06:03:51 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:03:51 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:03:51 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:03:51 africa/cairo] Param :sales_start_date = 2025-05-01
[01-Jun-2025 06:03:51 africa/cairo] Param :sales_end_date = 2025-05-31
[01-Jun-2025 06:03:51 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:03:51 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[01-Jun-2025 06:03:51 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:03:51 africa/cairo] Param :sales_start_date = 2025-06-01
[01-Jun-2025 06:03:51 africa/cairo] Param :sales_end_date = 2025-06-30
[01-Jun-2025 06:03:51 africa/cairo] خطأ أثناء حساب إجمالي المبيعات: SQLSTATE[HY093]: Invalid parameter number
[01-Jun-2025 06:04:03 africa/cairo] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php on line 154
[01-Jun-2025 06:04:03 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:04:03 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:04:03 africa/cairo] Param :sales_start_date1 = 2025-06-01
[01-Jun-2025 06:04:03 africa/cairo] Param :sales_end_date1 = 2025-06-30
[01-Jun-2025 06:04:03 africa/cairo] Param :sales_start_date2 = 2025-06-01
[01-Jun-2025 06:04:03 africa/cairo] Param :sales_end_date2 = 2025-06-30
[01-Jun-2025 06:04:03 africa/cairo] Total sales result: 0
[01-Jun-2025 06:04:03 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:04:03 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:04:03 africa/cairo] Param :sales_start_date1 = 2025-05-01
[01-Jun-2025 06:04:03 africa/cairo] Param :sales_end_date1 = 2025-05-31
[01-Jun-2025 06:04:03 africa/cairo] Param :sales_start_date2 = 2025-05-01
[01-Jun-2025 06:04:03 africa/cairo] Param :sales_end_date2 = 2025-05-31
[01-Jun-2025 06:04:03 africa/cairo] Total sales result: 43805.00
[01-Jun-2025 06:04:03 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:04:03 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:04:03 africa/cairo] Param :sales_start_date1 = 2025-06-01
[01-Jun-2025 06:04:03 africa/cairo] Param :sales_end_date1 = 2025-06-30
[01-Jun-2025 06:04:03 africa/cairo] Param :sales_start_date2 = 2025-06-01
[01-Jun-2025 06:04:03 africa/cairo] Param :sales_end_date2 = 2025-06-30
[01-Jun-2025 06:04:03 africa/cairo] Total sales result: 0
[01-Jun-2025 06:04:13 africa/cairo] Dashboard - User role: admin, isCashier: false, isAdmin: true
[01-Jun-2025 06:04:13 africa/cairo] Dashboard - Getting open day for branch ID: 1
[01-Jun-2025 06:04:13 africa/cairo] Dashboard - No open day found, looking for last closed day
[01-Jun-2025 06:04:13 africa/cairo] Dashboard - Last closed day found: ID=20250476, Date=2025-05-31
[01-Jun-2025 06:04:13 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.branch_id = :sales_branch_id AND i.end_day_id = :sales_end_day_id AND i.payment_status = :sales_payment_status
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_end_day_id = 20250476
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_payment_status = paid
[01-Jun-2025 06:04:13 africa/cairo] Total sales result: 805.00
[01-Jun-2025 06:04:13 africa/cairo] Dashboard - Total sales for last closed day (ID=20250476): 805
[01-Jun-2025 06:04:13 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.branch_id = :sales_branch_id AND i.end_day_id = :sales_end_day_id AND i.payment_status = :sales_payment_status
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_end_day_id = 20250476
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_payment_status = paid
[01-Jun-2025 06:04:13 africa/cairo] Total sales result: 805.00
[01-Jun-2025 06:04:13 africa/cairo] Dashboard - Weekly sales for last closed day (ID=20250476): 805
[01-Jun-2025 06:04:13 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.branch_id = :sales_branch_id AND i.end_day_id = :sales_end_day_id AND i.payment_status = :sales_payment_status
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_end_day_id = 20250476
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_payment_status = paid
[01-Jun-2025 06:04:13 africa/cairo] Total sales result: 805.00
[01-Jun-2025 06:04:13 africa/cairo] Dashboard - Monthly sales for last closed day (ID=20250476): 805
[01-Jun-2025 06:04:13 africa/cairo] Dashboard - Expenses for last closed day (ID=20250476): 145
[01-Jun-2025 06:04:13 africa/cairo] Dashboard - Preparing chart data, hasOpenDay: false, isCashier: false, isAdmin: true
[01-Jun-2025 06:04:13 africa/cairo] Dashboard - Chart data for last closed day: 805
[01-Jun-2025 06:04:13 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_start_date1 = 2025-05-26
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_end_date1 = 2025-05-26
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_start_date2 = 2025-05-26
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_end_date2 = 2025-05-26
[01-Jun-2025 06:04:13 africa/cairo] Total sales result: 580.00
[01-Jun-2025 06:04:13 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_start_date1 = 2025-05-27
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_end_date1 = 2025-05-27
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_start_date2 = 2025-05-27
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_end_date2 = 2025-05-27
[01-Jun-2025 06:04:13 africa/cairo] Total sales result: 1425.00
[01-Jun-2025 06:04:13 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_start_date1 = 2025-05-28
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_end_date1 = 2025-05-28
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_start_date2 = 2025-05-28
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_end_date2 = 2025-05-28
[01-Jun-2025 06:04:13 africa/cairo] Total sales result: 2280.00
[01-Jun-2025 06:04:13 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_start_date1 = 2025-05-29
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_end_date1 = 2025-05-29
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_start_date2 = 2025-05-29
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_end_date2 = 2025-05-29
[01-Jun-2025 06:04:13 africa/cairo] Total sales result: 1185.00
[01-Jun-2025 06:04:13 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_start_date1 = 2025-05-30
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_end_date1 = 2025-05-30
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_start_date2 = 2025-05-30
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_end_date2 = 2025-05-30
[01-Jun-2025 06:04:13 africa/cairo] Total sales result: 1465.00
[01-Jun-2025 06:04:13 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_start_date1 = 2025-05-31
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_end_date1 = 2025-05-31
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_start_date2 = 2025-05-31
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_end_date2 = 2025-05-31
[01-Jun-2025 06:04:13 africa/cairo] Total sales result: 805.00
[01-Jun-2025 06:04:13 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_start_date1 = 2025-06-01
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_end_date1 = 2025-06-01
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_start_date2 = 2025-06-01
[01-Jun-2025 06:04:13 africa/cairo] Param :sales_end_date2 = 2025-06-01
[01-Jun-2025 06:04:13 africa/cairo] Total sales result: 0
[01-Jun-2025 06:04:13 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:04:13 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:04:13 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:04:13 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:04:13 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:04:13 africa/cairo] Background Tasks Settings: {"enable_background_tasks":"0","notification_appointment_reminder":"1","notification_enable_whatsapp":"1","whatsapp_enabled":"1"}
[01-Jun-2025 06:04:15 africa/cairo] EndDay Index - User role: admin, isCashier: false
[01-Jun-2025 06:04:15 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:04:15 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:04:15 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:04:15 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:04:15 africa/cairo] API: طلب التحقق من أرقام هواتف المدراء
[01-Jun-2025 06:04:15 africa/cairo] API: التحقق من إعداد notification_admin_phone_numbers: 201022429488
201009036186
201032648474
[01-Jun-2025 06:04:15 africa/cairo] API: تم العثور على 3 رقم هاتف من الإعدادات باستخدام المفتاح notification_admin_phone_numbers
[01-Jun-2025 06:04:15 africa/cairo] API: التحقق من إعداد admin_phone_numbers: NULL
[01-Jun-2025 06:04:15 africa/cairo] API: التحقق من إعداد dmin_phone_numbers: NULL
[01-Jun-2025 06:04:15 africa/cairo] API: تم العثور على إجمالي 3 رقم هاتف من الإعدادات
[01-Jun-2025 06:04:15 africa/cairo] API: إرجاع 3 رقم هاتف للمدراء
[01-Jun-2025 06:04:15 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:04:15 africa/cairo] Background Tasks Settings: {"enable_background_tasks":"0","notification_appointment_reminder":"1","notification_enable_whatsapp":"1","whatsapp_enabled":"1"}
[01-Jun-2025 06:04:17 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:04:18 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:04:18 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:04:18 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:04:18 africa/cairo] API: طلب التحقق من أرقام هواتف المدراء
[01-Jun-2025 06:04:18 africa/cairo] API: التحقق من إعداد notification_admin_phone_numbers: 201022429488
201009036186
201032648474
[01-Jun-2025 06:04:18 africa/cairo] API: تم العثور على 3 رقم هاتف من الإعدادات باستخدام المفتاح notification_admin_phone_numbers
[01-Jun-2025 06:04:18 africa/cairo] API: التحقق من إعداد admin_phone_numbers: NULL
[01-Jun-2025 06:04:18 africa/cairo] API: التحقق من إعداد dmin_phone_numbers: NULL
[01-Jun-2025 06:04:18 africa/cairo] API: تم العثور على إجمالي 3 رقم هاتف من الإعدادات
[01-Jun-2025 06:04:18 africa/cairo] API: إرجاع 3 رقم هاتف للمدراء
[01-Jun-2025 06:04:18 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:04:18 africa/cairo] Background Tasks Settings: {"enable_background_tasks":"0","notification_appointment_reminder":"1","notification_enable_whatsapp":"1","whatsapp_enabled":"1"}
[01-Jun-2025 06:04:20 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 06:04:20 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 06:04:20 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 06:04:20 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 06:04:26 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 06:04:26 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 06:04:26 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 06:04:26 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 06:04:34 africa/cairo] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php on line 154
[01-Jun-2025 06:04:34 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:04:34 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:04:34 africa/cairo] Param :sales_start_date1 = 2025-06-01
[01-Jun-2025 06:04:34 africa/cairo] Param :sales_end_date1 = 2025-06-30
[01-Jun-2025 06:04:34 africa/cairo] Param :sales_start_date2 = 2025-06-01
[01-Jun-2025 06:04:34 africa/cairo] Param :sales_end_date2 = 2025-06-30
[01-Jun-2025 06:04:34 africa/cairo] Total sales result: 0
[01-Jun-2025 06:04:34 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:04:34 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:04:34 africa/cairo] Param :sales_start_date1 = 2025-05-01
[01-Jun-2025 06:04:34 africa/cairo] Param :sales_end_date1 = 2025-05-31
[01-Jun-2025 06:04:34 africa/cairo] Param :sales_start_date2 = 2025-05-01
[01-Jun-2025 06:04:34 africa/cairo] Param :sales_end_date2 = 2025-05-31
[01-Jun-2025 06:04:34 africa/cairo] Total sales result: 43805.00
[01-Jun-2025 06:04:34 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:04:34 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:04:34 africa/cairo] Param :sales_start_date1 = 2025-06-01
[01-Jun-2025 06:04:34 africa/cairo] Param :sales_end_date1 = 2025-06-30
[01-Jun-2025 06:04:34 africa/cairo] Param :sales_start_date2 = 2025-06-01
[01-Jun-2025 06:04:34 africa/cairo] Param :sales_end_date2 = 2025-06-30
[01-Jun-2025 06:04:34 africa/cairo] Total sales result: 0
[01-Jun-2025 06:04:56 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 06:04:56 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 06:04:56 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 06:04:56 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 06:04:59 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 06:04:59 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 06:04:59 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 06:04:59 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 06:05:13 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 06:05:13 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 06:05:13 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 06:05:13 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 06:05:20 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 06:05:20 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 06:05:20 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 06:05:20 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 06:06:08 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 06:06:08 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 06:06:08 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 06:06:08 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 06:06:16 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 06:06:16 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 06:06:16 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_start_date1 = 2025-06-01
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_end_date1 = 2025-06-30
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_start_date2 = 2025-06-01
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_end_date2 = 2025-06-30
[01-Jun-2025 06:06:16 africa/cairo] Total sales result: 0
[01-Jun-2025 06:06:16 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 06:06:16 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 06:06:16 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 06:06:16 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 06:06:16 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 06:06:16 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 06:06:16 africa/cairo] Total salaries amount: 0
[01-Jun-2025 06:06:16 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 06:06:16 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 06:06:16 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 06:06:16 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 06:06:16 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 06:06:16 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 06:06:16 africa/cairo] Total salaries amount: 0
[01-Jun-2025 06:06:16 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 06:06:16 africa/cairo] Param :branch_id = 1
[01-Jun-2025 06:06:16 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 06:06:16 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 06:06:16 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 06:06:16 africa/cairo] Total discounts: 0.00
[01-Jun-2025 06:06:16 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_payment_method = cash
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_start_date1 = 2025-06-01
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_end_date1 = 2025-06-30
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_start_date2 = 2025-06-01
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_end_date2 = 2025-06-30
[01-Jun-2025 06:06:16 africa/cairo] Total sales result: 0
[01-Jun-2025 06:06:16 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_payment_method = card
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_start_date1 = 2025-06-01
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_end_date1 = 2025-06-30
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_start_date2 = 2025-06-01
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_end_date2 = 2025-06-30
[01-Jun-2025 06:06:16 africa/cairo] Total sales result: 0
[01-Jun-2025 06:06:16 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_payment_method = other
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_start_date1 = 2025-06-01
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_end_date1 = 2025-06-30
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_start_date2 = 2025-06-01
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_end_date2 = 2025-06-30
[01-Jun-2025 06:06:16 africa/cairo] Total sales result: 0
[01-Jun-2025 06:06:16 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 06:06:16 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 06:06:16 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_start_date1 = 2025-06-01
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_end_date1 = 2025-06-30
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_start_date2 = 2025-06-01
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_end_date2 = 2025-06-30
[01-Jun-2025 06:06:16 africa/cairo] Total sales result: 0
[01-Jun-2025 06:06:16 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 06:06:16 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 06:06:16 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 06:06:16 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 06:06:16 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 06:06:16 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 06:06:16 africa/cairo] Total salaries amount: 0
[01-Jun-2025 06:06:16 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 06:06:16 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 06:06:16 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 06:06:16 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 06:06:16 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 06:06:16 africa/cairo] Total found: 0 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 06:06:16 africa/cairo] Total salaries amount: 0
[01-Jun-2025 06:06:16 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 06:06:16 africa/cairo] Param :branch_id = 1
[01-Jun-2025 06:06:16 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 06:06:16 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 06:06:16 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 06:06:16 africa/cairo] Total discounts: 0.00
[01-Jun-2025 06:06:16 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_payment_method = cash
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_start_date1 = 2025-06-01
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_end_date1 = 2025-06-30
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_start_date2 = 2025-06-01
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_end_date2 = 2025-06-30
[01-Jun-2025 06:06:16 africa/cairo] Total sales result: 0
[01-Jun-2025 06:06:16 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_payment_method = card
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_start_date1 = 2025-06-01
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_end_date1 = 2025-06-30
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_start_date2 = 2025-06-01
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_end_date2 = 2025-06-30
[01-Jun-2025 06:06:16 africa/cairo] Total sales result: 0
[01-Jun-2025 06:06:16 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_payment_method = other
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_start_date1 = 2025-06-01
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_end_date1 = 2025-06-30
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_start_date2 = 2025-06-01
[01-Jun-2025 06:06:16 africa/cairo] Param :sales_end_date2 = 2025-06-30
[01-Jun-2025 06:06:16 africa/cairo] Total sales result: 0
[01-Jun-2025 06:06:22 africa/cairo] PHP Deprecated:  number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php on line 154
[01-Jun-2025 06:06:22 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:06:22 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:06:22 africa/cairo] Param :sales_start_date1 = 2025-06-01
[01-Jun-2025 06:06:22 africa/cairo] Param :sales_end_date1 = 2025-06-30
[01-Jun-2025 06:06:22 africa/cairo] Param :sales_start_date2 = 2025-06-01
[01-Jun-2025 06:06:22 africa/cairo] Param :sales_end_date2 = 2025-06-30
[01-Jun-2025 06:06:22 africa/cairo] Total sales result: 0
[01-Jun-2025 06:06:22 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:06:22 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:06:22 africa/cairo] Param :sales_start_date1 = 2025-05-01
[01-Jun-2025 06:06:22 africa/cairo] Param :sales_end_date1 = 2025-05-31
[01-Jun-2025 06:06:22 africa/cairo] Param :sales_start_date2 = 2025-05-01
[01-Jun-2025 06:06:22 africa/cairo] Param :sales_end_date2 = 2025-05-31
[01-Jun-2025 06:06:22 africa/cairo] Total sales result: 43805.00
[01-Jun-2025 06:06:22 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:06:22 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:06:22 africa/cairo] Param :sales_start_date1 = 2025-06-01
[01-Jun-2025 06:06:22 africa/cairo] Param :sales_end_date1 = 2025-06-30
[01-Jun-2025 06:06:22 africa/cairo] Param :sales_start_date2 = 2025-06-01
[01-Jun-2025 06:06:22 africa/cairo] Param :sales_end_date2 = 2025-06-30
[01-Jun-2025 06:06:22 africa/cairo] Total sales result: 0
[01-Jun-2025 06:06:30 africa/cairo] Date range 'last_month' converted to: 2025-05-01 - 2025-05-31
[01-Jun-2025 06:06:30 africa/cairo] Final report filters: {"date_range":"last_month","start_date":"2025-05-01","end_date":"2025-05-31","branch_id":"1"}
[01-Jun-2025 06:06:30 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_start_date1 = 2025-05-01
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_end_date1 = 2025-05-31
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_start_date2 = 2025-05-01
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_end_date2 = 2025-05-31
[01-Jun-2025 06:06:30 africa/cairo] Total sales result: 43805.00
[01-Jun-2025 06:06:30 africa/cairo] Searching for salaries between 2025-05-01 and 2025-05-31
[01-Jun-2025 06:06:30 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 06:06:30 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 06:06:30 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 06:06:30 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 06:06:30 africa/cairo] Total found: 0 salary records for period 2025-05-01 to 2025-05-31
[01-Jun-2025 06:06:30 africa/cairo] Total salaries amount: 0
[01-Jun-2025 06:06:30 africa/cairo] Searching for salaries between 2025-05-01 and 2025-05-31
[01-Jun-2025 06:06:30 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 06:06:30 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 06:06:30 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 06:06:30 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 06:06:30 africa/cairo] Total found: 0 salary records for period 2025-05-01 to 2025-05-31
[01-Jun-2025 06:06:30 africa/cairo] Total salaries amount: 0
[01-Jun-2025 06:06:30 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 06:06:30 africa/cairo] Param :branch_id = 1
[01-Jun-2025 06:06:30 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 06:06:30 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 06:06:30 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 06:06:30 africa/cairo] Total discounts: 205.00
[01-Jun-2025 06:06:30 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_payment_method = cash
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_start_date1 = 2025-05-01
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_end_date1 = 2025-05-31
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_start_date2 = 2025-05-01
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_end_date2 = 2025-05-31
[01-Jun-2025 06:06:30 africa/cairo] Total sales result: 43805.00
[01-Jun-2025 06:06:30 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_payment_method = card
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_start_date1 = 2025-05-01
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_end_date1 = 2025-05-31
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_start_date2 = 2025-05-01
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_end_date2 = 2025-05-31
[01-Jun-2025 06:06:30 africa/cairo] Total sales result: 0
[01-Jun-2025 06:06:30 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_payment_method = other
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_start_date1 = 2025-05-01
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_end_date1 = 2025-05-31
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_start_date2 = 2025-05-01
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_end_date2 = 2025-05-31
[01-Jun-2025 06:06:30 africa/cairo] Total sales result: 0
[01-Jun-2025 06:06:30 africa/cairo] Date range 'last_month' converted to: 2025-05-01 - 2025-05-31
[01-Jun-2025 06:06:30 africa/cairo] Final report filters: {"date_range":"last_month","start_date":"2025-05-01","end_date":"2025-05-31","branch_id":"1"}
[01-Jun-2025 06:06:30 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_start_date1 = 2025-05-01
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_end_date1 = 2025-05-31
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_start_date2 = 2025-05-01
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_end_date2 = 2025-05-31
[01-Jun-2025 06:06:30 africa/cairo] Total sales result: 43805.00
[01-Jun-2025 06:06:30 africa/cairo] Searching for salaries between 2025-05-01 and 2025-05-31
[01-Jun-2025 06:06:30 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 06:06:30 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 06:06:30 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 06:06:30 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 06:06:30 africa/cairo] Total found: 0 salary records for period 2025-05-01 to 2025-05-31
[01-Jun-2025 06:06:30 africa/cairo] Total salaries amount: 0
[01-Jun-2025 06:06:30 africa/cairo] Searching for salaries between 2025-05-01 and 2025-05-31
[01-Jun-2025 06:06:30 africa/cairo] Found 0 records in employee_salaries table
[01-Jun-2025 06:06:30 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 06:06:30 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 06:06:30 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 06:06:30 africa/cairo] Total found: 0 salary records for period 2025-05-01 to 2025-05-31
[01-Jun-2025 06:06:30 africa/cairo] Total salaries amount: 0
[01-Jun-2025 06:06:30 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 06:06:30 africa/cairo] Param :branch_id = 1
[01-Jun-2025 06:06:30 africa/cairo] Param :start_date = 2025-05-01 00:00:00
[01-Jun-2025 06:06:30 africa/cairo] Param :end_date = 2025-05-31 23:59:59
[01-Jun-2025 06:06:30 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 06:06:30 africa/cairo] Total discounts: 205.00
[01-Jun-2025 06:06:30 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_payment_method = cash
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_start_date1 = 2025-05-01
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_end_date1 = 2025-05-31
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_start_date2 = 2025-05-01
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_end_date2 = 2025-05-31
[01-Jun-2025 06:06:30 africa/cairo] Total sales result: 43805.00
[01-Jun-2025 06:06:30 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_payment_method = card
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_start_date1 = 2025-05-01
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_end_date1 = 2025-05-31
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_start_date2 = 2025-05-01
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_end_date2 = 2025-05-31
[01-Jun-2025 06:06:30 africa/cairo] Total sales result: 0
[01-Jun-2025 06:06:30 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_payment_method = other
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_start_date1 = 2025-05-01
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_end_date1 = 2025-05-31
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_start_date2 = 2025-05-01
[01-Jun-2025 06:06:30 africa/cairo] Param :sales_end_date2 = 2025-05-31
[01-Jun-2025 06:06:30 africa/cairo] Total sales result: 0
[01-Jun-2025 06:07:34 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:07:34 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:07:34 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:07:34 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:07:34 africa/cairo] Background Tasks Settings: {"enable_background_tasks":"0","notification_appointment_reminder":"1","notification_enable_whatsapp":"1","whatsapp_enabled":"1"}
[01-Jun-2025 06:07:38 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:07:38 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:07:38 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:07:38 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:07:38 africa/cairo] Background Tasks Settings: {"enable_background_tasks":"0","notification_appointment_reminder":"1","notification_enable_whatsapp":"1","whatsapp_enabled":"1"}
[01-Jun-2025 06:07:59 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:07:59 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:07:59 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:07:59 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:07:59 africa/cairo] API: طلب التحقق من أرقام هواتف المدراء
[01-Jun-2025 06:07:59 africa/cairo] API: التحقق من إعداد notification_admin_phone_numbers: 201022429488
201009036186
201032648474
[01-Jun-2025 06:07:59 africa/cairo] API: تم العثور على 3 رقم هاتف من الإعدادات باستخدام المفتاح notification_admin_phone_numbers
[01-Jun-2025 06:07:59 africa/cairo] API: التحقق من إعداد admin_phone_numbers: NULL
[01-Jun-2025 06:07:59 africa/cairo] API: التحقق من إعداد dmin_phone_numbers: NULL
[01-Jun-2025 06:07:59 africa/cairo] API: تم العثور على إجمالي 3 رقم هاتف من الإعدادات
[01-Jun-2025 06:07:59 africa/cairo] API: إرجاع 3 رقم هاتف للمدراء
[01-Jun-2025 06:07:59 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:07:59 africa/cairo] Background Tasks Settings: {"enable_background_tasks":"0","notification_appointment_reminder":"1","notification_enable_whatsapp":"1","whatsapp_enabled":"1"}
[01-Jun-2025 06:08:00 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 06:08:00 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 06:08:00 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_start_date1 = 2025-06-01
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_end_date1 = 2025-06-30
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_start_date2 = 2025-06-01
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_end_date2 = 2025-06-30
[01-Jun-2025 06:08:00 africa/cairo] Total sales result: 0
[01-Jun-2025 06:08:00 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 06:08:00 africa/cairo] Found 1 records in employee_salaries table
[01-Jun-2025 06:08:00 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 06:08:00 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 06:08:00 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 06:08:00 africa/cairo] Total found: 1 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 06:08:00 africa/cairo] Total salaries amount: 3274
[01-Jun-2025 06:08:00 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 06:08:00 africa/cairo] Found 1 records in employee_salaries table
[01-Jun-2025 06:08:00 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 06:08:00 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 06:08:00 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 06:08:00 africa/cairo] Total found: 1 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 06:08:00 africa/cairo] Total salaries amount: 3274
[01-Jun-2025 06:08:00 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 06:08:00 africa/cairo] Param :branch_id = 1
[01-Jun-2025 06:08:00 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 06:08:00 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 06:08:00 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 06:08:00 africa/cairo] Total discounts: 0.00
[01-Jun-2025 06:08:00 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_payment_method = cash
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_start_date1 = 2025-06-01
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_end_date1 = 2025-06-30
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_start_date2 = 2025-06-01
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_end_date2 = 2025-06-30
[01-Jun-2025 06:08:00 africa/cairo] Total sales result: 0
[01-Jun-2025 06:08:00 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_payment_method = card
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_start_date1 = 2025-06-01
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_end_date1 = 2025-06-30
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_start_date2 = 2025-06-01
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_end_date2 = 2025-06-30
[01-Jun-2025 06:08:00 africa/cairo] Total sales result: 0
[01-Jun-2025 06:08:00 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_payment_method = other
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_start_date1 = 2025-06-01
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_end_date1 = 2025-06-30
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_start_date2 = 2025-06-01
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_end_date2 = 2025-06-30
[01-Jun-2025 06:08:00 africa/cairo] Total sales result: 0
[01-Jun-2025 06:08:00 africa/cairo] Date range 'this_month' converted to: 2025-06-01 - 2025-06-30
[01-Jun-2025 06:08:00 africa/cairo] Final report filters: {"date_range":"this_month","start_date":"2025-06-01","end_date":"2025-06-30","branch_id":"1"}
[01-Jun-2025 06:08:00 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_start_date1 = 2025-06-01
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_end_date1 = 2025-06-30
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_start_date2 = 2025-06-01
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_end_date2 = 2025-06-30
[01-Jun-2025 06:08:00 africa/cairo] Total sales result: 0
[01-Jun-2025 06:08:00 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 06:08:00 africa/cairo] Found 1 records in employee_salaries table
[01-Jun-2025 06:08:00 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 06:08:00 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 06:08:00 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 06:08:00 africa/cairo] Total found: 1 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 06:08:00 africa/cairo] Total salaries amount: 3274
[01-Jun-2025 06:08:00 africa/cairo] Searching for salaries between 2025-06-01 and 2025-06-30
[01-Jun-2025 06:08:00 africa/cairo] Found 1 records in employee_salaries table
[01-Jun-2025 06:08:00 africa/cairo] Error fetching from salaries table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.salaries' doesn't exist
[01-Jun-2025 06:08:00 africa/cairo] Error fetching from expenses table: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'e.employee_id' in 'on clause'
[01-Jun-2025 06:08:00 africa/cairo] Error fetching from employee_payments table: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'if0_38692463_hok.employee_payments' doesn't exist
[01-Jun-2025 06:08:00 africa/cairo] Total found: 1 salary records for period 2025-06-01 to 2025-06-30
[01-Jun-2025 06:08:00 africa/cairo] Total salaries amount: 3274
[01-Jun-2025 06:08:00 africa/cairo] SQL Query for getTotalPartialPaidInvoices: SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i WHERE i.payment_status = 'partial' AND i.branch_id = :branch_id AND i.created_at BETWEEN :start_date AND :end_date
[01-Jun-2025 06:08:00 africa/cairo] Param :branch_id = 1
[01-Jun-2025 06:08:00 africa/cairo] Param :start_date = 2025-06-01 00:00:00
[01-Jun-2025 06:08:00 africa/cairo] Param :end_date = 2025-06-30 23:59:59
[01-Jun-2025 06:08:00 africa/cairo] Total partial paid invoices result: 0
[01-Jun-2025 06:08:00 africa/cairo] Total discounts: 0.00
[01-Jun-2025 06:08:00 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_payment_method = cash
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_start_date1 = 2025-06-01
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_end_date1 = 2025-06-30
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_start_date2 = 2025-06-01
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_end_date2 = 2025-06-30
[01-Jun-2025 06:08:00 africa/cairo] Total sales result: 0
[01-Jun-2025 06:08:00 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_payment_method = card
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_start_date1 = 2025-06-01
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_end_date1 = 2025-06-30
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_start_date2 = 2025-06-01
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_end_date2 = 2025-06-30
[01-Jun-2025 06:08:00 africa/cairo] Total sales result: 0
[01-Jun-2025 06:08:00 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND i.payment_method = :sales_payment_method AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_payment_method = other
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_start_date1 = 2025-06-01
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_end_date1 = 2025-06-30
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_start_date2 = 2025-06-01
[01-Jun-2025 06:08:00 africa/cairo] Param :sales_end_date2 = 2025-06-30
[01-Jun-2025 06:08:00 africa/cairo] Total sales result: 0
[01-Jun-2025 06:12:59 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:12:59 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:17:59 africa/cairo] Maintenance check: Current script = notifications.php, User role = admin
[01-Jun-2025 06:17:59 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = false
[01-Jun-2025 06:17:59 africa/cairo] Maintenance mode is NOT active
[01-Jun-2025 06:17:59 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:17:59 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:18:12 africa/cairo] Maintenance check: Current script = test_maintenance.php, User role = admin
[01-Jun-2025 06:18:12 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = false
[01-Jun-2025 06:18:12 africa/cairo] Maintenance mode is NOT active
[01-Jun-2025 06:18:12 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = false
[01-Jun-2025 06:18:18 africa/cairo] Maintenance check: Current script = maintenance_control.php, User role = admin
[01-Jun-2025 06:18:18 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = false
[01-Jun-2025 06:18:18 africa/cairo] Maintenance mode is NOT active
[01-Jun-2025 06:18:23 africa/cairo] Maintenance check: Current script = maintenance_control.php, User role = admin
[01-Jun-2025 06:18:23 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = false
[01-Jun-2025 06:18:23 africa/cairo] Maintenance mode is NOT active
[01-Jun-2025 06:18:23 africa/cairo] Maintenance enable: File = ../config/maintenance_mode.txt, Result = success
[01-Jun-2025 06:18:23 africa/cairo] Maintenance mode enabled successfully
[01-Jun-2025 06:18:28 africa/cairo] Maintenance check: Current script = login.php, User role = admin
[01-Jun-2025 06:18:28 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = true
[01-Jun-2025 06:18:28 africa/cairo] Maintenance mode is ACTIVE
[01-Jun-2025 06:18:28 africa/cairo] User is exempt from maintenance mode
[01-Jun-2025 06:18:28 africa/cairo] Maintenance check: Current script = dashboard.php, User role = admin
[01-Jun-2025 06:18:28 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = true
[01-Jun-2025 06:18:28 africa/cairo] Maintenance mode is ACTIVE
[01-Jun-2025 06:18:28 africa/cairo] User is exempt from maintenance mode
[01-Jun-2025 06:18:29 africa/cairo] Dashboard - User role: admin, isCashier: false, isAdmin: true
[01-Jun-2025 06:18:29 africa/cairo] Dashboard - Getting open day for branch ID: 1
[01-Jun-2025 06:18:29 africa/cairo] Dashboard - No open day found, looking for last closed day
[01-Jun-2025 06:18:29 africa/cairo] Dashboard - Last closed day found: ID=20250476, Date=2025-05-31
[01-Jun-2025 06:18:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.branch_id = :sales_branch_id AND i.end_day_id = :sales_end_day_id AND i.payment_status = :sales_payment_status
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_end_day_id = 20250476
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_payment_status = paid
[01-Jun-2025 06:18:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 06:18:29 africa/cairo] Dashboard - Total sales for last closed day (ID=20250476): 805
[01-Jun-2025 06:18:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.branch_id = :sales_branch_id AND i.end_day_id = :sales_end_day_id AND i.payment_status = :sales_payment_status
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_end_day_id = 20250476
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_payment_status = paid
[01-Jun-2025 06:18:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 06:18:29 africa/cairo] Dashboard - Weekly sales for last closed day (ID=20250476): 805
[01-Jun-2025 06:18:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.branch_id = :sales_branch_id AND i.end_day_id = :sales_end_day_id AND i.payment_status = :sales_payment_status
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_end_day_id = 20250476
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_payment_status = paid
[01-Jun-2025 06:18:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 06:18:29 africa/cairo] Dashboard - Monthly sales for last closed day (ID=20250476): 805
[01-Jun-2025 06:18:29 africa/cairo] Dashboard - Expenses for last closed day (ID=20250476): 145
[01-Jun-2025 06:18:29 africa/cairo] Dashboard - Preparing chart data, hasOpenDay: false, isCashier: false, isAdmin: true
[01-Jun-2025 06:18:29 africa/cairo] Dashboard - Chart data for last closed day: 805
[01-Jun-2025 06:18:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_start_date1 = 2025-05-26
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_end_date1 = 2025-05-26
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_start_date2 = 2025-05-26
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_end_date2 = 2025-05-26
[01-Jun-2025 06:18:29 africa/cairo] Total sales result: 580.00
[01-Jun-2025 06:18:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_start_date1 = 2025-05-27
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_end_date1 = 2025-05-27
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_start_date2 = 2025-05-27
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_end_date2 = 2025-05-27
[01-Jun-2025 06:18:29 africa/cairo] Total sales result: 1425.00
[01-Jun-2025 06:18:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_start_date1 = 2025-05-28
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_end_date1 = 2025-05-28
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_start_date2 = 2025-05-28
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_end_date2 = 2025-05-28
[01-Jun-2025 06:18:29 africa/cairo] Total sales result: 2280.00
[01-Jun-2025 06:18:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_start_date1 = 2025-05-29
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_end_date1 = 2025-05-29
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_start_date2 = 2025-05-29
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_end_date2 = 2025-05-29
[01-Jun-2025 06:18:29 africa/cairo] Total sales result: 1185.00
[01-Jun-2025 06:18:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_start_date1 = 2025-05-30
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_end_date1 = 2025-05-30
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_start_date2 = 2025-05-30
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_end_date2 = 2025-05-30
[01-Jun-2025 06:18:29 africa/cairo] Total sales result: 1465.00
[01-Jun-2025 06:18:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_start_date1 = 2025-05-31
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_end_date1 = 2025-05-31
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_start_date2 = 2025-05-31
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_end_date2 = 2025-05-31
[01-Jun-2025 06:18:29 africa/cairo] Total sales result: 805.00
[01-Jun-2025 06:18:29 africa/cairo] SQL Query for getTotalSales: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date1 AND :sales_end_date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date2 AND :sales_end_date2)
                )
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_branch_id = 1
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_start_date1 = 2025-06-01
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_end_date1 = 2025-06-01
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_start_date2 = 2025-06-01
[01-Jun-2025 06:18:29 africa/cairo] Param :sales_end_date2 = 2025-06-01
[01-Jun-2025 06:18:29 africa/cairo] Total sales result: 0
[01-Jun-2025 06:18:29 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:18:29 africa/cairo] Maintenance check: Current script = create_user_notifications.php, User role = admin
[01-Jun-2025 06:18:29 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = true
[01-Jun-2025 06:18:29 africa/cairo] Maintenance mode is ACTIVE
[01-Jun-2025 06:18:29 africa/cairo] User is exempt from maintenance mode
[01-Jun-2025 06:18:29 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:18:29 africa/cairo] Maintenance check: Current script = notifications.php, User role = admin
[01-Jun-2025 06:18:29 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = true
[01-Jun-2025 06:18:29 africa/cairo] Maintenance mode is ACTIVE
[01-Jun-2025 06:18:29 africa/cairo] User is exempt from maintenance mode
[01-Jun-2025 06:18:29 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:18:29 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:18:29 africa/cairo] Maintenance check: Current script = notifications.php, User role = admin
[01-Jun-2025 06:18:29 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = true
[01-Jun-2025 06:18:29 africa/cairo] Maintenance mode is ACTIVE
[01-Jun-2025 06:18:29 africa/cairo] User is exempt from maintenance mode
[01-Jun-2025 06:18:29 africa/cairo] Maintenance check: Current script = settings.php, User role = admin
[01-Jun-2025 06:18:29 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = true
[01-Jun-2025 06:18:29 africa/cairo] Maintenance mode is ACTIVE
[01-Jun-2025 06:18:29 africa/cairo] User is exempt from maintenance mode
[01-Jun-2025 06:18:29 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:18:29 africa/cairo] Background Tasks Settings: {"enable_background_tasks":"0","notification_appointment_reminder":"1","notification_enable_whatsapp":"1","whatsapp_enabled":"1"}
[01-Jun-2025 06:18:47 africa/cairo] Maintenance check: Current script = logout.php, User role = admin
[01-Jun-2025 06:18:47 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = true
[01-Jun-2025 06:18:47 africa/cairo] Maintenance mode is ACTIVE
[01-Jun-2025 06:18:47 africa/cairo] User is exempt from maintenance mode
[01-Jun-2025 06:18:47 africa/cairo] خطأ أثناء تحديث وقت الخروج: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'last_logout' in 'field list'
[01-Jun-2025 06:18:47 africa/cairo] Maintenance check: Current script = login.php, User role = غير محدد
[01-Jun-2025 06:18:47 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = true
[01-Jun-2025 06:18:47 africa/cairo] Maintenance mode is ACTIVE
[01-Jun-2025 06:18:47 africa/cairo] User is exempt from maintenance mode
[01-Jun-2025 06:18:57 africa/cairo] Maintenance check: Current script = login.php, User role = غير محدد
[01-Jun-2025 06:18:57 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = true
[01-Jun-2025 06:18:57 africa/cairo] Maintenance mode is ACTIVE
[01-Jun-2025 06:18:57 africa/cairo] User is exempt from maintenance mode
[01-Jun-2025 06:18:57 africa/cairo] Maintenance check: Current script = dashboard.php, User role = cashier
[01-Jun-2025 06:18:57 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = true
[01-Jun-2025 06:18:57 africa/cairo] Maintenance mode is ACTIVE
[01-Jun-2025 06:18:57 africa/cairo] User is NOT exempt, redirecting to maintenance page
[01-Jun-2025 06:20:15 africa/cairo] Maintenance check: Current script = login.php, User role = cashier
[01-Jun-2025 06:20:15 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = false
[01-Jun-2025 06:20:15 africa/cairo] Maintenance mode is NOT active
[01-Jun-2025 06:20:15 africa/cairo] Maintenance check: Current script = dashboard.php, User role = cashier
[01-Jun-2025 06:20:15 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = false
[01-Jun-2025 06:20:15 africa/cairo] Maintenance mode is NOT active
[01-Jun-2025 06:20:16 africa/cairo] Dashboard - User role: cashier, isCashier: true, isAdmin: false
[01-Jun-2025 06:20:16 africa/cairo] Dashboard - Getting open day for branch ID: 1
[01-Jun-2025 06:20:16 africa/cairo] Dashboard - No open day found, looking for last closed day
[01-Jun-2025 06:20:16 africa/cairo] Dashboard - Last closed day found: ID=20250476, Date=2025-05-31
[01-Jun-2025 06:20:16 africa/cairo] Dashboard - No open day or last closed day, showing zero sales
[01-Jun-2025 06:20:16 africa/cairo] Dashboard - No open day or last closed day, showing zero weekly sales
[01-Jun-2025 06:20:16 africa/cairo] Dashboard - No open day or last closed day, showing zero monthly sales
[01-Jun-2025 06:20:16 africa/cairo] Dashboard - No open day or last closed day, showing zero expenses
[01-Jun-2025 06:20:16 africa/cairo] Dashboard - Preparing chart data, hasOpenDay: false, isCashier: true, isAdmin: false
[01-Jun-2025 06:20:16 africa/cairo] Dashboard - Chart data for no open day: 0
[01-Jun-2025 06:20:16 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:20:16 africa/cairo] Maintenance check: Current script = create_user_notifications.php, User role = cashier
[01-Jun-2025 06:20:16 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = false
[01-Jun-2025 06:20:16 africa/cairo] Maintenance mode is NOT active
[01-Jun-2025 06:20:16 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:20:16 africa/cairo] Maintenance check: Current script = notifications.php, User role = cashier
[01-Jun-2025 06:20:16 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = false
[01-Jun-2025 06:20:16 africa/cairo] Maintenance mode is NOT active
[01-Jun-2025 06:20:16 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:20:16 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:20:16 africa/cairo] Maintenance check: Current script = notifications.php, User role = cashier
[01-Jun-2025 06:20:16 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = false
[01-Jun-2025 06:20:16 africa/cairo] Maintenance mode is NOT active
[01-Jun-2025 06:20:16 africa/cairo] Maintenance check: Current script = settings.php, User role = cashier
[01-Jun-2025 06:20:16 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = false
[01-Jun-2025 06:20:16 africa/cairo] Maintenance mode is NOT active
[01-Jun-2025 06:20:16 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:20:16 africa/cairo] Background Tasks Settings: {"enable_background_tasks":"0","notification_appointment_reminder":"1","notification_enable_whatsapp":"1","whatsapp_enabled":"1"}
[01-Jun-2025 06:20:55 africa/cairo] Maintenance check: Current script = dashboard.php, User role = cashier
[01-Jun-2025 06:20:55 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = false
[01-Jun-2025 06:20:55 africa/cairo] Maintenance mode is NOT active
[01-Jun-2025 06:20:55 africa/cairo] Dashboard - User role: cashier, isCashier: true, isAdmin: false
[01-Jun-2025 06:20:55 africa/cairo] Dashboard - Getting open day for branch ID: 1
[01-Jun-2025 06:20:55 africa/cairo] Dashboard - No open day found, looking for last closed day
[01-Jun-2025 06:20:55 africa/cairo] Dashboard - Last closed day found: ID=20250476, Date=2025-05-31
[01-Jun-2025 06:20:55 africa/cairo] Dashboard - No open day or last closed day, showing zero sales
[01-Jun-2025 06:20:55 africa/cairo] Dashboard - No open day or last closed day, showing zero weekly sales
[01-Jun-2025 06:20:55 africa/cairo] Dashboard - No open day or last closed day, showing zero monthly sales
[01-Jun-2025 06:20:55 africa/cairo] Dashboard - No open day or last closed day, showing zero expenses
[01-Jun-2025 06:20:55 africa/cairo] Dashboard - Preparing chart data, hasOpenDay: false, isCashier: true, isAdmin: false
[01-Jun-2025 06:20:55 africa/cairo] Dashboard - Chart data for no open day: 0
[01-Jun-2025 06:20:55 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:20:55 africa/cairo] Maintenance check: Current script = notifications.php, User role = cashier
[01-Jun-2025 06:20:55 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = false
[01-Jun-2025 06:20:55 africa/cairo] Maintenance mode is NOT active
[01-Jun-2025 06:20:55 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:20:55 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:20:55 africa/cairo] Maintenance check: Current script = notifications.php, User role = cashier
[01-Jun-2025 06:20:55 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = false
[01-Jun-2025 06:20:55 africa/cairo] Maintenance mode is NOT active
[01-Jun-2025 06:20:55 africa/cairo] Maintenance check: Current script = settings.php, User role = cashier
[01-Jun-2025 06:20:55 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = false
[01-Jun-2025 06:20:55 africa/cairo] Maintenance mode is NOT active
[01-Jun-2025 06:20:55 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:20:55 africa/cairo] Background Tasks Settings: {"enable_background_tasks":"0","notification_appointment_reminder":"1","notification_enable_whatsapp":"1","whatsapp_enabled":"1"}
[01-Jun-2025 06:20:59 africa/cairo] Maintenance check: Current script = dashboard.php, User role = cashier
[01-Jun-2025 06:20:59 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = false
[01-Jun-2025 06:20:59 africa/cairo] Maintenance mode is NOT active
[01-Jun-2025 06:20:59 africa/cairo] Dashboard - User role: cashier, isCashier: true, isAdmin: false
[01-Jun-2025 06:20:59 africa/cairo] Dashboard - Getting open day for branch ID: 1
[01-Jun-2025 06:20:59 africa/cairo] Dashboard - No open day found, looking for last closed day
[01-Jun-2025 06:20:59 africa/cairo] Dashboard - Last closed day found: ID=20250476, Date=2025-05-31
[01-Jun-2025 06:20:59 africa/cairo] Dashboard - No open day or last closed day, showing zero sales
[01-Jun-2025 06:20:59 africa/cairo] Dashboard - No open day or last closed day, showing zero weekly sales
[01-Jun-2025 06:20:59 africa/cairo] Dashboard - No open day or last closed day, showing zero monthly sales
[01-Jun-2025 06:20:59 africa/cairo] Dashboard - No open day or last closed day, showing zero expenses
[01-Jun-2025 06:20:59 africa/cairo] Dashboard - Preparing chart data, hasOpenDay: false, isCashier: true, isAdmin: false
[01-Jun-2025 06:20:59 africa/cairo] Dashboard - Chart data for no open day: 0
[01-Jun-2025 06:20:59 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:20:59 africa/cairo] Maintenance check: Current script = notifications.php, User role = cashier
[01-Jun-2025 06:20:59 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = false
[01-Jun-2025 06:20:59 africa/cairo] Maintenance mode is NOT active
[01-Jun-2025 06:20:59 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:20:59 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:20:59 africa/cairo] Maintenance check: Current script = notifications.php, User role = cashier
[01-Jun-2025 06:20:59 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = false
[01-Jun-2025 06:20:59 africa/cairo] Maintenance mode is NOT active
[01-Jun-2025 06:20:59 africa/cairo] Maintenance check: Current script = settings.php, User role = cashier
[01-Jun-2025 06:20:59 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = false
[01-Jun-2025 06:20:59 africa/cairo] Maintenance mode is NOT active
[01-Jun-2025 06:20:59 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:20:59 africa/cairo] Background Tasks Settings: {"enable_background_tasks":"0","notification_appointment_reminder":"1","notification_enable_whatsapp":"1","whatsapp_enabled":"1"}
[01-Jun-2025 06:21:00 africa/cairo] Maintenance check: Current script = dashboard.php, User role = cashier
[01-Jun-2025 06:21:00 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = false
[01-Jun-2025 06:21:00 africa/cairo] Maintenance mode is NOT active
[01-Jun-2025 06:21:00 africa/cairo] Dashboard - User role: cashier, isCashier: true, isAdmin: false
[01-Jun-2025 06:21:00 africa/cairo] Dashboard - Getting open day for branch ID: 1
[01-Jun-2025 06:21:00 africa/cairo] Dashboard - No open day found, looking for last closed day
[01-Jun-2025 06:21:00 africa/cairo] Dashboard - Last closed day found: ID=20250476, Date=2025-05-31
[01-Jun-2025 06:21:00 africa/cairo] Dashboard - No open day or last closed day, showing zero sales
[01-Jun-2025 06:21:00 africa/cairo] Dashboard - No open day or last closed day, showing zero weekly sales
[01-Jun-2025 06:21:00 africa/cairo] Dashboard - No open day or last closed day, showing zero monthly sales
[01-Jun-2025 06:21:00 africa/cairo] Dashboard - No open day or last closed day, showing zero expenses
[01-Jun-2025 06:21:00 africa/cairo] Dashboard - Preparing chart data, hasOpenDay: false, isCashier: true, isAdmin: false
[01-Jun-2025 06:21:00 africa/cairo] Dashboard - Chart data for no open day: 0
[01-Jun-2025 06:21:00 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:21:00 africa/cairo] Maintenance check: Current script = notifications.php, User role = cashier
[01-Jun-2025 06:21:00 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = false
[01-Jun-2025 06:21:00 africa/cairo] Maintenance mode is NOT active
[01-Jun-2025 06:21:00 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:21:00 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:21:00 africa/cairo] Maintenance check: Current script = settings.php, User role = cashier
[01-Jun-2025 06:21:00 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = false
[01-Jun-2025 06:21:00 africa/cairo] Maintenance mode is NOT active
[01-Jun-2025 06:21:00 africa/cairo] Loaded 52 settings into cache
[01-Jun-2025 06:21:00 africa/cairo] Background Tasks Settings: {"enable_background_tasks":"0","notification_appointment_reminder":"1","notification_enable_whatsapp":"1","whatsapp_enabled":"1"}
[01-Jun-2025 06:21:00 africa/cairo] Maintenance check: Current script = notifications.php, User role = cashier
[01-Jun-2025 06:21:00 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = false
[01-Jun-2025 06:21:00 africa/cairo] Maintenance mode is NOT active
[01-Jun-2025 06:21:33 africa/cairo] Maintenance check: Current script = login.php, User role = cashier
[01-Jun-2025 06:21:33 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = true
[01-Jun-2025 06:21:33 africa/cairo] Maintenance mode is ACTIVE
[01-Jun-2025 06:21:33 africa/cairo] User is exempt from maintenance mode
[01-Jun-2025 06:21:33 africa/cairo] Maintenance check: Current script = dashboard.php, User role = cashier
[01-Jun-2025 06:21:33 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = true
[01-Jun-2025 06:21:33 africa/cairo] Maintenance mode is ACTIVE
[01-Jun-2025 06:21:33 africa/cairo] User is NOT exempt, redirecting to maintenance page
[01-Jun-2025 06:22:01 africa/cairo] Maintenance check: Current script = notifications.php, User role = cashier
[01-Jun-2025 06:22:01 africa/cairo] Maintenance check: File path = C:\xampp\htdocs\backup\new1\includes/../config/maintenance_mode.txt, Exists = true
[01-Jun-2025 06:22:01 africa/cairo] Maintenance mode is ACTIVE
[01-Jun-2025 06:22:01 africa/cairo] User is exempt from maintenance mode
