[2025-06-01 06:41:09] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT es.*, e.name as employee_name, e.position
                              FROM employee_salaries es
                              LEFT JOIN employees e ON es.employee_id = e.id
                              WHERE es.payment_status = 'paid'
                              AND (
                                  (es.year = :start_year AND es.year = :end_year AND es.month >= :start_month AND es.month <= :end_month) OR
                                  (es.year = :start_year AND es.year < :end_year AND es.month >= :start_month) OR
                                  (es.year = :end_year AND es.year > :start_year AND es.month <= :end_month) OR
                                  (es.year > :start_year AND es.year < :end_year)
                              ) AND e.branch_id = :branch_id ORDER BY es.year DESC, es.month DESC, es.employee_id
[2025-06-01 06:41:09] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:41:09] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(99): Database->execute()
#2 C:\xampp\htdocs\backup\new1\test_salary_fix.php(95): Database->fetchAll()
#3 {main}
[2025-06-01 06:41:09] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT es.*, e.name as employee_name, e.position
                              FROM employee_salaries es
                              LEFT JOIN employees e ON es.employee_id = e.id
                              WHERE es.payment_status = 'paid'
                              AND (
                                  (es.year = :start_year AND es.year = :end_year AND es.month >= :start_month AND es.month <= :end_month) OR
                                  (es.year = :start_year AND es.year < :end_year AND es.month >= :start_month) OR
                                  (es.year = :end_year AND es.year > :start_year AND es.month <= :end_month) OR
                                  (es.year > :start_year AND es.year < :end_year)
                              ) AND e.branch_id = :branch_id ORDER BY es.year DESC, es.month DESC, es.employee_id
[2025-06-01 06:41:09] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:41:09] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(99): Database->execute()
#2 C:\xampp\htdocs\backup\new1\test_salary_fix.php(95): Database->fetchAll()
#3 {main}
