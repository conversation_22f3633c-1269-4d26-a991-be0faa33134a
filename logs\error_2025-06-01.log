[2025-06-01 05:49:35] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:49:35] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:49:35] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1239): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\api\reports.php(704): Invoice->getTotalSales(Array)
#4 C:\xampp\htdocs\backup\new1\api\reports.php(116): handleComprehensiveReport()
#5 {main}
[2025-06-01 05:49:35] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:49:35] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:49:35] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1239): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\api\reports.php(704): Invoice->getTotalSales(Array)
#4 C:\xampp\htdocs\backup\new1\api\reports.php(116): handleComprehensiveReport()
#5 {main}
[2025-06-01 05:49:36] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:49:36] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:49:36] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1239): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\api\reports.php(704): Invoice->getTotalSales(Array)
#4 C:\xampp\htdocs\backup\new1\api\reports.php(116): handleComprehensiveReport()
#5 {main}
[2025-06-01 05:49:36] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:49:36] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:49:36] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1239): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\api\reports.php(704): Invoice->getTotalSales(Array)
#4 C:\xampp\htdocs\backup\new1\api\reports.php(116): handleComprehensiveReport()
#5 {main}
[2025-06-01 05:49:43] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:49:43] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:49:43] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1239): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\api\reports.php(704): Invoice->getTotalSales(Array)
#4 C:\xampp\htdocs\backup\new1\api\reports.php(116): handleComprehensiveReport()
#5 {main}
[2025-06-01 05:49:43] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:49:43] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:49:43] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1239): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\api\reports.php(704): Invoice->getTotalSales(Array)
#4 C:\xampp\htdocs\backup\new1\api\reports.php(116): handleComprehensiveReport()
#5 {main}
[2025-06-01 05:49:46] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:49:46] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:49:46] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1239): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\api\reports.php(704): Invoice->getTotalSales(Array)
#4 C:\xampp\htdocs\backup\new1\api\reports.php(116): handleComprehensiveReport()
#5 {main}
[2025-06-01 05:49:46] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:49:46] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:49:46] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1239): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\api\reports.php(704): Invoice->getTotalSales(Array)
#4 C:\xampp\htdocs\backup\new1\api\reports.php(116): handleComprehensiveReport()
#5 {main}
[2025-06-01 05:50:03] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:50:03] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:50:03] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1239): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\api\reports.php(704): Invoice->getTotalSales(Array)
#4 C:\xampp\htdocs\backup\new1\api\reports.php(116): handleComprehensiveReport()
#5 {main}
[2025-06-01 05:50:03] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:50:03] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:50:03] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1239): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\api\reports.php(704): Invoice->getTotalSales(Array)
#4 C:\xampp\htdocs\backup\new1\api\reports.php(116): handleComprehensiveReport()
#5 {main}
[2025-06-01 05:50:09] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:50:09] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:50:09] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1239): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\api\reports.php(704): Invoice->getTotalSales(Array)
#4 C:\xampp\htdocs\backup\new1\api\reports.php(116): handleComprehensiveReport()
#5 {main}
[2025-06-01 05:50:09] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:50:09] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:50:09] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1239): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\api\reports.php(704): Invoice->getTotalSales(Array)
#4 C:\xampp\htdocs\backup\new1\api\reports.php(116): handleComprehensiveReport()
#5 {main}
[2025-06-01 05:50:48] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT
                    SUM(i.final_amount) as total_sales,
                    SUM(CASE WHEN i.payment_method = 'cash' THEN i.final_amount ELSE 0 END) as cash_amount,
                    SUM(CASE WHEN i.payment_method = 'card' THEN i.final_amount ELSE 0 END) as card_amount,
                    SUM(CASE WHEN i.payment_method NOT IN ('cash', 'card') THEN i.final_amount ELSE 0 END) as other_amount
                FROM invoices i
                LEFT JOIN end_days ed ON i.end_day_id = ed.id
                WHERE (
                    (i.end_day_id IS NOT NULL AND ed.date = :date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) = :date)
                )
                AND i.payment_status = 'paid' AND i.branch_id = :branch_id
[2025-06-01 05:50:48] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:50:48] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(108): Database->execute()
#2 C:\xampp\htdocs\backup\new1\api\reports.php(668): Database->fetch()
#3 C:\xampp\htdocs\backup\new1\api\reports.php(116): handleComprehensiveReport()
#4 {main}
[2025-06-01 05:50:48] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT
                    SUM(i.final_amount) as total_sales,
                    SUM(CASE WHEN i.payment_method = 'cash' THEN i.final_amount ELSE 0 END) as cash_amount,
                    SUM(CASE WHEN i.payment_method = 'card' THEN i.final_amount ELSE 0 END) as card_amount,
                    SUM(CASE WHEN i.payment_method NOT IN ('cash', 'card') THEN i.final_amount ELSE 0 END) as other_amount
                FROM invoices i
                LEFT JOIN end_days ed ON i.end_day_id = ed.id
                WHERE (
                    (i.end_day_id IS NOT NULL AND ed.date = :date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) = :date)
                )
                AND i.payment_status = 'paid' AND i.branch_id = :branch_id
[2025-06-01 05:50:48] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:50:48] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(108): Database->execute()
#2 C:\xampp\htdocs\backup\new1\api\reports.php(668): Database->fetch()
#3 C:\xampp\htdocs\backup\new1\api\reports.php(116): handleComprehensiveReport()
#4 {main}
[2025-06-01 05:50:52] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT
                    SUM(i.final_amount) as total_sales,
                    SUM(CASE WHEN i.payment_method = 'cash' THEN i.final_amount ELSE 0 END) as cash_amount,
                    SUM(CASE WHEN i.payment_method = 'card' THEN i.final_amount ELSE 0 END) as card_amount,
                    SUM(CASE WHEN i.payment_method NOT IN ('cash', 'card') THEN i.final_amount ELSE 0 END) as other_amount
                FROM invoices i
                LEFT JOIN end_days ed ON i.end_day_id = ed.id
                WHERE (
                    (i.end_day_id IS NOT NULL AND ed.date = :date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) = :date)
                )
                AND i.payment_status = 'paid' AND i.branch_id = :branch_id
[2025-06-01 05:50:52] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:50:52] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(108): Database->execute()
#2 C:\xampp\htdocs\backup\new1\api\reports.php(668): Database->fetch()
#3 C:\xampp\htdocs\backup\new1\api\reports.php(116): handleComprehensiveReport()
#4 {main}
[2025-06-01 05:50:52] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT
                    SUM(i.final_amount) as total_sales,
                    SUM(CASE WHEN i.payment_method = 'cash' THEN i.final_amount ELSE 0 END) as cash_amount,
                    SUM(CASE WHEN i.payment_method = 'card' THEN i.final_amount ELSE 0 END) as card_amount,
                    SUM(CASE WHEN i.payment_method NOT IN ('cash', 'card') THEN i.final_amount ELSE 0 END) as other_amount
                FROM invoices i
                LEFT JOIN end_days ed ON i.end_day_id = ed.id
                WHERE (
                    (i.end_day_id IS NOT NULL AND ed.date = :date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) = :date)
                )
                AND i.payment_status = 'paid' AND i.branch_id = :branch_id
[2025-06-01 05:50:52] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:50:52] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(108): Database->execute()
#2 C:\xampp\htdocs\backup\new1\api\reports.php(668): Database->fetch()
#3 C:\xampp\htdocs\backup\new1\api\reports.php(116): handleComprehensiveReport()
#4 {main}
[2025-06-01 05:50:53] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT
                    SUM(i.final_amount) as total_sales,
                    SUM(CASE WHEN i.payment_method = 'cash' THEN i.final_amount ELSE 0 END) as cash_amount,
                    SUM(CASE WHEN i.payment_method = 'card' THEN i.final_amount ELSE 0 END) as card_amount,
                    SUM(CASE WHEN i.payment_method NOT IN ('cash', 'card') THEN i.final_amount ELSE 0 END) as other_amount
                FROM invoices i
                LEFT JOIN end_days ed ON i.end_day_id = ed.id
                WHERE (
                    (i.end_day_id IS NOT NULL AND ed.date = :date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) = :date)
                )
                AND i.payment_status = 'paid' AND i.branch_id = :branch_id
[2025-06-01 05:50:53] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:50:53] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(108): Database->execute()
#2 C:\xampp\htdocs\backup\new1\api\reports.php(668): Database->fetch()
#3 C:\xampp\htdocs\backup\new1\api\reports.php(116): handleComprehensiveReport()
#4 {main}
[2025-06-01 05:50:53] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT
                    SUM(i.final_amount) as total_sales,
                    SUM(CASE WHEN i.payment_method = 'cash' THEN i.final_amount ELSE 0 END) as cash_amount,
                    SUM(CASE WHEN i.payment_method = 'card' THEN i.final_amount ELSE 0 END) as card_amount,
                    SUM(CASE WHEN i.payment_method NOT IN ('cash', 'card') THEN i.final_amount ELSE 0 END) as other_amount
                FROM invoices i
                LEFT JOIN end_days ed ON i.end_day_id = ed.id
                WHERE (
                    (i.end_day_id IS NOT NULL AND ed.date = :date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) = :date)
                )
                AND i.payment_status = 'paid' AND i.branch_id = :branch_id
[2025-06-01 05:50:53] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:50:53] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(108): Database->execute()
#2 C:\xampp\htdocs\backup\new1\api\reports.php(668): Database->fetch()
#3 C:\xampp\htdocs\backup\new1\api\reports.php(116): handleComprehensiveReport()
#4 {main}
[2025-06-01 05:51:22] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT
                    SUM(i.final_amount) as total_sales,
                    SUM(CASE WHEN i.payment_method = 'cash' THEN i.final_amount ELSE 0 END) as cash_amount,
                    SUM(CASE WHEN i.payment_method = 'card' THEN i.final_amount ELSE 0 END) as card_amount,
                    SUM(CASE WHEN i.payment_method NOT IN ('cash', 'card') THEN i.final_amount ELSE 0 END) as other_amount
                FROM invoices i
                LEFT JOIN end_days ed ON i.end_day_id = ed.id
                WHERE (
                    (i.end_day_id IS NOT NULL AND ed.date = :date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) = :date)
                )
                AND i.payment_status = 'paid' AND i.branch_id = :branch_id
[2025-06-01 05:51:22] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:51:22] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(108): Database->execute()
#2 C:\xampp\htdocs\backup\new1\api\reports.php(668): Database->fetch()
#3 C:\xampp\htdocs\backup\new1\api\reports.php(116): handleComprehensiveReport()
#4 {main}
[2025-06-01 05:51:22] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT
                    SUM(i.final_amount) as total_sales,
                    SUM(CASE WHEN i.payment_method = 'cash' THEN i.final_amount ELSE 0 END) as cash_amount,
                    SUM(CASE WHEN i.payment_method = 'card' THEN i.final_amount ELSE 0 END) as card_amount,
                    SUM(CASE WHEN i.payment_method NOT IN ('cash', 'card') THEN i.final_amount ELSE 0 END) as other_amount
                FROM invoices i
                LEFT JOIN end_days ed ON i.end_day_id = ed.id
                WHERE (
                    (i.end_day_id IS NOT NULL AND ed.date = :date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) = :date)
                )
                AND i.payment_status = 'paid' AND i.branch_id = :branch_id
[2025-06-01 05:51:22] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:51:22] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(108): Database->execute()
#2 C:\xampp\htdocs\backup\new1\api\reports.php(668): Database->fetch()
#3 C:\xampp\htdocs\backup\new1\api\reports.php(116): handleComprehensiveReport()
#4 {main}
[2025-06-01 05:54:44] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:54:44] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:54:44] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(114): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:54:44] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:54:44] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:54:44] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(138): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:54:44] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                )
[2025-06-01 05:54:44] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:54:44] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(251): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:55:50] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:55:50] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:55:50] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(114): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:55:50] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:55:50] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:55:50] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(138): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:55:50] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:55:50] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:55:50] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(251): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:55:52] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:55:52] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:55:52] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(114): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:55:52] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:55:52] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:55:52] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(138): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:55:52] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:55:52] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:55:52] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(251): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:55:53] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:55:53] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:55:53] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(114): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:55:53] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:55:53] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:55:53] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(138): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:55:53] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:55:53] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:55:53] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(251): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:55:59] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:55:59] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:55:59] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(114): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:55:59] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:55:59] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:55:59] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(138): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:55:59] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:55:59] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:55:59] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(251): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:56:31] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:56:31] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:56:31] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(114): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:56:31] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:56:31] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:56:31] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(138): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:56:31] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:56:31] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:56:31] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(251): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:57:37] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:57:37] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:37] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(114): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:57:37] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:57:37] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:37] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(138): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:57:37] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:57:37] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:37] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(251): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:57:38] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:57:38] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:38] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(114): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:57:38] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:57:38] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:38] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(138): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:57:38] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :date_start AND :date_end) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :date_start AND :date_end)
                )
[2025-06-01 05:57:38] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:38] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(251): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:57:51] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:51] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:51] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(114): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:57:51] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:51] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:51] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(138): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:57:51] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:51] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:51] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(251): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:57:53] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:53] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:53] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(114): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:57:53] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:53] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:53] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(138): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:57:53] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:53] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:53] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(251): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:57:55] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:55] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:55] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(114): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:57:55] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:55] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:55] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(138): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:57:55] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:55] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:55] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(251): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:57:56] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:56] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:56] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(114): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:57:56] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:56] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:56] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(138): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:57:56] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:56] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:56] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(251): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:57:59] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:59] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:59] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(114): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:57:59] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:59] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:59] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(138): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:57:59] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:57:59] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:57:59] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(251): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:58:02] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:02] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:02] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(114): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:58:02] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:02] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:02] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(138): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:58:02] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:02] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:02] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(251): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:58:03] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:03] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:03] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(114): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:58:03] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:03] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:03] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(138): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:58:03] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:03] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:03] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(251): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:58:04] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:04] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:04] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(114): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:58:04] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:04] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:04] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(138): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:58:04] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:04] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:04] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(251): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:58:05] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:05] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:05] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(114): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:58:05] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:05] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:05] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(138): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:58:05] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:05] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:05] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(251): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:58:06] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:06] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:06] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(114): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:58:06] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:06] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:06] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(138): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:58:06] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:06] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:06] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(251): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:58:39] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:39] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:39] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(118): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:58:39] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:39] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:39] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(143): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 05:58:39] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 05:58:39] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 05:58:39] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(256): Invoice->getTotalSales(Array)
#4 {main}
