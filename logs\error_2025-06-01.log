[2025-06-01 06:03:23] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i
                                  LEFT JOIN end_days ed ON i.end_day_id = ed.id
                                  WHERE i.payment_status = 'paid'
                                  AND i.branch_id = :branch_id
                                  AND (
                                      (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date AND :end_date) OR
                                      (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date AND :end_date)
                                  )
[2025-06-01 06:03:23] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:03:23] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(150): Database->fetchColumn()
#3 {main}
[2025-06-01 06:03:23] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:03:23] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:03:23] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(181): Invoice->getTotalSales(Array)
#4 {main}
[2025-06-01 06:03:23] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id WHERE i.payment_status = 'paid' AND i.branch_id = :sales_branch_id AND (
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )
[2025-06-01 06:03:23] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:03:23] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(117): Database->execute()
#2 C:\xampp\htdocs\backup\new1\includes\classes\Invoice.php(1245): Database->fetchColumn()
#3 C:\xampp\htdocs\backup\new1\pages\reports\test_fix.php(294): Invoice->getTotalSales(Array)
#4 {main}
