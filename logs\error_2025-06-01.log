[2025-06-01 06:05:20] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT
                    SUM(i.final_amount) as total_sales,
                    SUM(CASE WHEN i.payment_method = 'cash' THEN i.final_amount ELSE 0 END) as cash_amount,
                    SUM(CASE WHEN i.payment_method = 'card' THEN i.final_amount ELSE 0 END) as card_amount,
                    SUM(CASE WHEN i.payment_method NOT IN ('cash', 'card') THEN i.final_amount ELSE 0 END) as other_amount
                FROM invoices i
                LEFT JOIN end_days ed ON i.end_day_id = ed.id
                WHERE (
                    (i.end_day_id IS NOT NULL AND ed.date = :date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) = :date)
                )
                AND i.payment_status = 'paid' AND i.branch_id = :branch_id
[2025-06-01 06:05:20] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:05:20] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(108): Database->execute()
#2 C:\xampp\htdocs\backup\new1\api\reports.php(668): Database->fetch()
#3 C:\xampp\htdocs\backup\new1\api\reports.php(116): handleComprehensiveReport()
#4 {main}
[2025-06-01 06:05:20] [error] خطأ SQL: SQLSTATE[HY093]: Invalid parameter number - الاستعلام: SELECT
                    SUM(i.final_amount) as total_sales,
                    SUM(CASE WHEN i.payment_method = 'cash' THEN i.final_amount ELSE 0 END) as cash_amount,
                    SUM(CASE WHEN i.payment_method = 'card' THEN i.final_amount ELSE 0 END) as card_amount,
                    SUM(CASE WHEN i.payment_method NOT IN ('cash', 'card') THEN i.final_amount ELSE 0 END) as other_amount
                FROM invoices i
                LEFT JOIN end_days ed ON i.end_day_id = ed.id
                WHERE (
                    (i.end_day_id IS NOT NULL AND ed.date = :date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) = :date)
                )
                AND i.payment_status = 'paid' AND i.branch_id = :branch_id
[2025-06-01 06:05:20] [error] Exception: SQLSTATE[HY093]: Invalid parameter number
[2025-06-01 06:05:20] [error] Stack Trace: #0 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(87): PDOStatement->execute()
#1 C:\xampp\htdocs\backup\new1\includes\classes\Database.php(108): Database->execute()
#2 C:\xampp\htdocs\backup\new1\api\reports.php(668): Database->fetch()
#3 C:\xampp\htdocs\backup\new1\api\reports.php(116): handleComprehensiveReport()
#4 {main}
