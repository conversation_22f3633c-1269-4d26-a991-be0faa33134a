<?php
/**
 * لوحة التحكم في وضع الصيانة
 * يمكن للمدير تفعيل/إلغاء وضع الصيانة وتعديل الإعدادات
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../config/init.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn()) {
    header('Location: ../pages/auth/login.php');
    exit;
}

// التحقق من صلاحيات المدير
if (!hasPermission('admin_access')) {
    header('Location: ../pages/dashboard.php?error=no_permission');
    exit;
}

$maintenanceFile = '../config/maintenance_mode.txt';
$configFile = '../config/maintenance_config.json';

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'enable':
                $result = file_put_contents($maintenanceFile, 'enabled');
                error_log('Maintenance enable: File = ' . $maintenanceFile . ', Result = ' . ($result !== false ? 'success' : 'failed'));

                if ($result !== false) {
                    $message = 'تم تفعيل وضع الصيانة بنجاح';
                    $messageType = 'success';
                    error_log('Maintenance mode enabled successfully');
                } else {
                    $message = 'فشل في تفعيل وضع الصيانة - تحقق من صلاحيات المجلد';
                    $messageType = 'danger';
                    error_log('Failed to enable maintenance mode');
                }
                break;
                
            case 'disable':
                if (file_exists($maintenanceFile)) {
                    $result = unlink($maintenanceFile);
                    error_log('Maintenance disable: File deleted = ' . ($result ? 'success' : 'failed'));

                    if ($result) {
                        $message = 'تم إلغاء وضع الصيانة بنجاح';
                        $messageType = 'success';
                    } else {
                        $message = 'فشل في إلغاء وضع الصيانة - تحقق من صلاحيات الملف';
                        $messageType = 'danger';
                    }
                } else {
                    $message = 'وضع الصيانة غير مفعل بالفعل';
                    $messageType = 'info';
                    error_log('Maintenance file does not exist');
                }
                break;
                
            case 'update_config':
                $config = [
                    'title' => $_POST['title'] ?? 'النظام تحت الصيانة',
                    'message' => $_POST['message'] ?? '',
                    'description' => $_POST['description'] ?? '',
                    'estimated_time' => $_POST['estimated_time'] ?? '',
                    'contact_info' => $_POST['contact_info'] ?? '',
                    'show_progress' => isset($_POST['show_progress']),
                    'progress_percentage' => intval($_POST['progress_percentage'] ?? 0),
                    'last_updated' => date('Y-m-d H:i:s')
                ];
                
                file_put_contents($configFile, json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                $message = 'تم تحديث إعدادات الصيانة بنجاح';
                $messageType = 'success';
                break;
        }
    }
}

// قراءة حالة الصيانة الحالية
$maintenanceEnabled = file_exists($maintenanceFile);

// قراءة إعدادات الصيانة
$config = [];
if (file_exists($configFile)) {
    $config = json_decode(file_get_contents($configFile), true) ?: [];
}

// الإعدادات الافتراضية
$defaultConfig = [
    'title' => 'النظام تحت الصيانة',
    'message' => 'نعتذر، النظام غير متاح حالياً بسبب أعمال الصيانة والتطوير.',
    'description' => 'نحن نعمل على تحسين النظام وإضافة ميزات جديدة لتوفير تجربة أفضل لك.',
    'estimated_time' => 'الوقت المتوقع للانتهاء: 30 دقيقة',
    'contact_info' => 'للاستفسارات الطارئة، يرجى التواصل معنا على: <EMAIL>',
    'show_progress' => true,
    'progress_percentage' => 75
];

$config = array_merge($defaultConfig, $config);

$pageTitle = 'إدارة وضع الصيانة';
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SYSTEM_NAME; ?> - <?php echo $pageTitle; ?></title>
    
    <!-- Bootstrap RTL via CDN -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- Google Font Tajawal -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap">
    
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f8f9fa;
        }
        .maintenance-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status-badge {
            font-size: 1.1rem;
            padding: 8px 16px;
        }
        .preview-btn {
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="fas fa-tools"></i> <?php echo $pageTitle; ?></h1>
                    <a href="../pages/dashboard.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
                    </a>
                </div>

                <!-- رسائل النظام -->
                <?php if (isset($message)): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- حالة الصيانة الحالية -->
                <div class="maintenance-card p-4 mb-4">
                    <h3><i class="fas fa-info-circle"></i> حالة النظام الحالية</h3>
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <span class="badge status-badge <?php echo $maintenanceEnabled ? 'bg-warning' : 'bg-success'; ?>">
                                <i class="fas <?php echo $maintenanceEnabled ? 'fa-tools' : 'fa-check-circle'; ?>"></i>
                                <?php echo $maintenanceEnabled ? 'النظام تحت الصيانة' : 'النظام يعمل بشكل طبيعي'; ?>
                            </span>
                        </div>
                        <div>
                            <?php if ($maintenanceEnabled): ?>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="disable">
                                    <button type="submit" class="btn btn-success" onclick="return confirm('هل أنت متأكد من إلغاء وضع الصيانة؟')">
                                        <i class="fas fa-play"></i> إلغاء الصيانة
                                    </button>
                                </form>
                            <?php else: ?>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="enable">
                                    <button type="submit" class="btn btn-warning" onclick="return confirm('هل أنت متأكد من تفعيل وضع الصيانة؟')">
                                        <i class="fas fa-pause"></i> تفعيل الصيانة
                                    </button>
                                </form>
                            <?php endif; ?>
                            
                            <a href="../maintenance.php" target="_blank" class="btn btn-info preview-btn">
                                <i class="fas fa-eye"></i> معاينة صفحة الصيانة
                            </a>
                        </div>
                    </div>
                </div>

                <!-- إعدادات صفحة الصيانة -->
                <div class="maintenance-card p-4">
                    <h3><i class="fas fa-cog"></i> إعدادات صفحة الصيانة</h3>
                    
                    <form method="POST">
                        <input type="hidden" name="action" value="update_config">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="title" class="form-label">عنوان الصفحة</label>
                                    <input type="text" class="form-control" id="title" name="title" value="<?php echo htmlspecialchars($config['title']); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="estimated_time" class="form-label">الوقت المتوقع</label>
                                    <input type="text" class="form-control" id="estimated_time" name="estimated_time" value="<?php echo htmlspecialchars($config['estimated_time']); ?>">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="message" class="form-label">الرسالة الرئيسية</label>
                            <textarea class="form-control" id="message" name="message" rows="2"><?php echo htmlspecialchars($config['message']); ?></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">الوصف التفصيلي</label>
                            <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars($config['description']); ?></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="contact_info" class="form-label">معلومات التواصل</label>
                            <input type="text" class="form-control" id="contact_info" name="contact_info" value="<?php echo htmlspecialchars($config['contact_info']); ?>">
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="show_progress" name="show_progress" <?php echo $config['show_progress'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="show_progress">
                                        عرض شريط التقدم
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="progress_percentage" class="form-label">نسبة التقدم (%)</label>
                                    <input type="number" class="form-control" id="progress_percentage" name="progress_percentage" min="0" max="100" value="<?php echo $config['progress_percentage']; ?>">
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ الإعدادات
                            </button>
                            
                            <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                <i class="fas fa-undo"></i> إعادة تعيين
                            </button>
                        </div>
                    </form>
                </div>

                <!-- معلومات إضافية -->
                <div class="maintenance-card p-4">
                    <h3><i class="fas fa-lightbulb"></i> معلومات مهمة</h3>
                    <div class="alert alert-info">
                        <ul class="mb-0">
                            <li><strong>تفعيل الصيانة:</strong> عند تفعيل وضع الصيانة، سيتم توجيه جميع المستخدمين إلى صفحة الصيانة</li>
                            <li><strong>استثناء المدراء:</strong> المدراء يمكنهم الوصول للنظام حتى أثناء الصيانة</li>
                            <li><strong>التحديث التلقائي:</strong> صفحة الصيانة تتحدث تلقائياً كل 30 ثانية</li>
                            <li><strong>الإعدادات:</strong> يمكن تعديل محتوى صفحة الصيانة في أي وقت</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery via CDN -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Bootstrap Bundle JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function resetForm() {
            if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟')) {
                document.querySelector('form').reset();
            }
        }

        // تحديث شريط التقدم عند تغيير القيمة
        document.getElementById('progress_percentage').addEventListener('input', function() {
            const value = this.value;
            if (value < 0) this.value = 0;
            if (value > 100) this.value = 100;
        });

        // إخفاء/إظهار حقل نسبة التقدم حسب الخيار
        document.getElementById('show_progress').addEventListener('change', function() {
            const progressField = document.getElementById('progress_percentage').closest('.mb-3');
            if (this.checked) {
                progressField.style.display = 'block';
            } else {
                progressField.style.display = 'none';
            }
        });
    </script>
</body>
</html>
