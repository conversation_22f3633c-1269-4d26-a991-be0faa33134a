<?php
/**
 * صفحة اختبار للمدراء لتجربة نظام الصيانة
 */

// بدء الجلسة
session_start();

// محاكاة تسجيل دخول مدير للاختبار
if (isset($_GET['simulate_admin'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['user_role'] = 'admin';
    $_SESSION['user_name'] = 'مدير النظام';
    echo "<div style='background: green; color: white; padding: 10px; margin: 10px; border-radius: 5px;'>تم محاكاة تسجيل دخول المدير بنجاح!</div>";
}

// محاكاة تسجيل دخول كاشير للاختبار
if (isset($_GET['simulate_cashier'])) {
    $_SESSION['user_id'] = 10;
    $_SESSION['user_role'] = 'cashier';
    $_SESSION['user_name'] = 'كاشير';
    echo "<div style='background: blue; color: white; padding: 10px; margin: 10px; border-radius: 5px;'>تم محاكاة تسجيل دخول الكاشير بنجاح!</div>";
}

// تسجيل خروج
if (isset($_GET['logout'])) {
    session_destroy();
    session_start();
    echo "<div style='background: red; color: white; padding: 10px; margin: 10px; border-radius: 5px;'>تم تسجيل الخروج بنجاح!</div>";
}

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head><meta charset='UTF-8'><title>اختبار نظام الصيانة للمدراء</title></head>";
echo "<body style='font-family: Arial, sans-serif; margin: 20px;'>";

echo "<h1>🔧 اختبار نظام الصيانة للمدراء</h1>";

// أزرار محاكاة تسجيل الدخول
echo "<div style='background: #f0f0f0; padding: 15px; border-radius: 5px; margin-bottom: 20px;'>";
echo "<h3>محاكاة تسجيل الدخول:</h3>";
echo "<a href='?simulate_admin' style='background: green; color: white; padding: 10px; text-decoration: none; margin: 5px; border-radius: 3px;'>👨‍💼 تسجيل دخول كمدير</a> ";
echo "<a href='?simulate_cashier' style='background: blue; color: white; padding: 10px; text-decoration: none; margin: 5px; border-radius: 3px;'>👨‍💻 تسجيل دخول ككاشير</a> ";
echo "<a href='?logout' style='background: red; color: white; padding: 10px; text-decoration: none; margin: 5px; border-radius: 3px;'>🚪 تسجيل خروج</a>";
echo "</div>";

// عرض معلومات الجلسة الحالية
echo "<h2>📋 معلومات الجلسة الحالية:</h2>";
echo "<ul>";
echo "<li><strong>دور المستخدم:</strong> " . ($_SESSION['user_role'] ?? 'غير مسجل') . "</li>";
echo "<li><strong>معرف المستخدم:</strong> " . ($_SESSION['user_id'] ?? 'غير مسجل') . "</li>";
echo "<li><strong>اسم المستخدم:</strong> " . ($_SESSION['user_name'] ?? 'غير مسجل') . "</li>";
echo "</ul>";

// التحقق من حالة الصيانة
$maintenanceFile = 'config/maintenance_mode.txt';
echo "<h2>🔍 حالة الصيانة الحالية:</h2>";
echo "<p><strong>ملف الصيانة موجود:</strong> " . (file_exists($maintenanceFile) ? 'نعم ✅' : 'لا ❌') . "</p>";

if (file_exists($maintenanceFile)) {
    echo "<p><strong>محتوى الملف:</strong> " . file_get_contents($maintenanceFile) . "</p>";
    echo "<p><strong>وقت الإنشاء:</strong> " . date('Y-m-d H:i:s', filemtime($maintenanceFile)) . "</p>";
}

// أزرار التحكم في الصيانة
echo "<h2>⚙️ التحكم في الصيانة:</h2>";

if (isset($_GET['enable_maintenance'])) {
    $result = file_put_contents($maintenanceFile, 'enabled_from_test_' . date('Y-m-d H:i:s'));
    if ($result !== false) {
        echo "<div style='background: orange; color: white; padding: 10px; margin: 10px; border-radius: 5px;'>✅ تم تفعيل الصيانة بنجاح!</div>";
    } else {
        echo "<div style='background: red; color: white; padding: 10px; margin: 10px; border-radius: 5px;'>❌ فشل في تفعيل الصيانة!</div>";
    }
}

if (isset($_GET['disable_maintenance'])) {
    if (file_exists($maintenanceFile)) {
        $result = unlink($maintenanceFile);
        if ($result) {
            echo "<div style='background: green; color: white; padding: 10px; margin: 10px; border-radius: 5px;'>✅ تم إلغاء الصيانة بنجاح!</div>";
        } else {
            echo "<div style='background: red; color: white; padding: 10px; margin: 10px; border-radius: 5px;'>❌ فشل في إلغاء الصيانة!</div>";
        }
    } else {
        echo "<div style='background: blue; color: white; padding: 10px; margin: 10px; border-radius: 5px;'>ℹ️ الصيانة غير مفعلة بالفعل!</div>";
    }
}

echo "<div style='margin: 15px 0;'>";
if (file_exists($maintenanceFile)) {
    echo "<a href='?disable_maintenance' style='background: green; color: white; padding: 10px; text-decoration: none; margin: 5px; border-radius: 3px;'>❌ إلغاء الصيانة</a>";
} else {
    echo "<a href='?enable_maintenance' style='background: orange; color: white; padding: 10px; text-decoration: none; margin: 5px; border-radius: 3px;'>✅ تفعيل الصيانة</a>";
}
echo "</div>";

// اختبار الوصول للصفحات
echo "<h2>🔗 اختبار الوصول للصفحات:</h2>";
echo "<ul>";
echo "<li><a href='maintenance.php' target='_blank'>صفحة الصيانة</a></li>";
echo "<li><a href='admin/maintenance_control.php' target='_blank'>لوحة التحكم في الصيانة</a></li>";
echo "<li><a href='pages/dashboard.php' target='_blank'>لوحة التحكم الرئيسية</a></li>";
echo "<li><a href='simple_test.php' target='_blank'>صفحة الاختبار البسيطة</a></li>";
echo "<li><a href='debug_maintenance.php' target='_blank'>صفحة التشخيص</a></li>";
echo "</ul>";

// تعليمات الاختبار
echo "<h2>📝 تعليمات الاختبار:</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; border-left: 4px solid #2196F3;'>";
echo "<ol>";
echo "<li><strong>اختبار المدير:</strong> سجل دخول كمدير، ثم فعل الصيانة، ثم جرب الوصول للصفحات المختلفة</li>";
echo "<li><strong>اختبار الكاشير:</strong> سجل دخول ككاشير، ثم جرب الوصول للصفحات أثناء تفعيل الصيانة</li>";
echo "<li><strong>اختبار الزائر:</strong> سجل خروج، ثم جرب الوصول للصفحات أثناء تفعيل الصيانة</li>";
echo "<li><strong>اختبار لوحة التحكم:</strong> جرب الوصول للوحة التحكم في الصيانة بأدوار مختلفة</li>";
echo "</ol>";
echo "</div>";

// معلومات إضافية
echo "<h2>ℹ️ معلومات مهمة:</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;'>";
echo "<ul>";
echo "<li><strong>المدراء:</strong> يمكنهم الوصول للنظام حتى أثناء الصيانة</li>";
echo "<li><strong>الكاشيرين:</strong> سيتم توجيههم لصفحة الصيانة</li>";
echo "<li><strong>الزوار:</strong> سيتم توجيههم لصفحة الصيانة</li>";
echo "<li><strong>الصفحات المستثناة:</strong> maintenance.php, login.php, logout.php</li>";
echo "</ul>";
echo "</div>";

// عرض آخر الأخطاء
echo "<h2>📋 سجل الأخطاء:</h2>";
echo "<div style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; border-radius: 5px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;'>";

// قراءة آخر سجلات الأخطاء
$errorLogFile = ini_get('error_log');
if ($errorLogFile && file_exists($errorLogFile)) {
    $lines = file($errorLogFile);
    $maintenanceLines = array_filter($lines, function($line) {
        return strpos($line, 'Maintenance') !== false;
    });
    
    $lastLines = array_slice($maintenanceLines, -10);
    
    if (empty($lastLines)) {
        echo "لا توجد سجلات صيانة حديثة";
    } else {
        foreach ($lastLines as $line) {
            echo htmlspecialchars($line) . "<br>";
        }
    }
} else {
    echo "لا يمكن العثور على ملف سجل الأخطاء";
}

echo "</div>";

echo "</body></html>";
?>
