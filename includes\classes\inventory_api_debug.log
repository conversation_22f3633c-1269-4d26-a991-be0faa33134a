=== 2025-04-14 22:15:58 ===
API Filters: {"search":null,"category_id":null,"branch_id":"1","low_stock":null,"out_of_stock":null,"is_for_sale":null,"min_quantity":null,"max_quantity":null,"limit":50,"offset":0}
Session branch_id: not set
=== 2025-04-14 22:16:01 ===
API Filters: {"search":null,"category_id":null,"branch_id":0,"low_stock":null,"out_of_stock":null,"is_for_sale":null,"min_quantity":null,"max_quantity":null,"limit":50,"offset":0}
Session branch_id: not set
=== 2025-04-14 22:24:10 ===
API Filters: {"search":null,"category_id":null,"branch_id":0,"low_stock":null,"out_of_stock":null,"is_for_sale":null,"min_quantity":null,"max_quantity":null,"limit":50,"offset":0}
Session branch_id: not set
=== 2025-04-14 22:24:23 ===
API Filters: {"search":null,"category_id":null,"branch_id":0,"low_stock":null,"out_of_stock":null,"is_for_sale":null,"min_quantity":null,"max_quantity":null,"limit":50,"offset":0}
Session branch_id: not set
=== 2025-04-14 22:24:27 ===
API Filters: {"search":null,"category_id":null,"branch_id":0,"low_stock":null,"out_of_stock":null,"is_for_sale":null,"min_quantity":null,"max_quantity":null,"limit":50,"offset":0}
Session branch_id: not set
=== 2025-04-14 22:24:34 ===
API Filters: {"search":null,"category_id":null,"branch_id":0,"low_stock":null,"out_of_stock":null,"is_for_sale":null,"min_quantity":null,"max_quantity":null,"limit":50,"offset":0}
Session branch_id: not set
=== 2025-04-14 22:24:35 ===
API Filters: {"search":null,"category_id":null,"branch_id":0,"low_stock":null,"out_of_stock":null,"is_for_sale":null,"min_quantity":null,"max_quantity":null,"limit":50,"offset":0}
Session branch_id: not set
=== 2025-04-14 22:24:35 ===
API Filters: {"search":null,"category_id":null,"branch_id":0,"low_stock":null,"out_of_stock":null,"is_for_sale":null,"min_quantity":null,"max_quantity":null,"limit":50,"offset":0}
Session branch_id: not set
=== 2025-04-14 22:24:38 ===
API Filters: {"search":null,"category_id":null,"branch_id":0,"low_stock":null,"out_of_stock":null,"is_for_sale":null,"min_quantity":null,"max_quantity":null,"limit":50,"offset":0}
Session branch_id: not set
=== 2025-04-14 22:43:27 ===
API Filters: {"search":null,"category_id":null,"branch_id":0,"low_stock":null,"out_of_stock":null,"is_for_sale":null,"min_quantity":null,"max_quantity":null,"limit":50,"offset":0}
Session branch_id: not set
=== 2025-04-14 22:43:31 ===
API Filters: {"search":null,"category_id":null,"branch_id":"1","low_stock":null,"out_of_stock":null,"is_for_sale":null,"min_quantity":null,"max_quantity":null,"limit":50,"offset":0}
Session branch_id: not set
=== 2025-04-14 22:43:33 ===
API Filters: {"search":null,"category_id":null,"branch_id":0,"low_stock":null,"out_of_stock":null,"is_for_sale":null,"min_quantity":null,"max_quantity":null,"limit":50,"offset":0}
Session branch_id: not set
=== 2025-04-16 04:05:29 ===
API Filters: {"search":null,"category_id":null,"branch_id":0,"low_stock":null,"out_of_stock":null,"is_for_sale":null,"min_quantity":null,"max_quantity":null,"limit":50,"offset":0}
Session branch_id: not set
=== 2025-04-16 04:07:01 ===
API Filters: {"search":null,"category_id":null,"branch_id":0,"low_stock":null,"out_of_stock":null,"is_for_sale":null,"min_quantity":null,"max_quantity":null,"limit":50,"offset":0}
Session branch_id: not set
=== 2025-04-16 07:24:58 ===
API Filters: {"search":null,"category_id":null,"branch_id":"1","low_stock":null,"out_of_stock":null,"is_for_sale":null,"min_quantity":null,"max_quantity":null,"limit":50,"offset":0}
Session branch_id: not set
=== 2025-04-16 07:25:38 ===
API Filters: {"search":null,"category_id":null,"branch_id":0,"low_stock":null,"out_of_stock":null,"is_for_sale":null,"min_quantity":null,"max_quantity":null,"limit":50,"offset":0}
Session branch_id: not set
=== 2025-04-16 07:41:32 ===
API Filters: {"search":null,"category_id":null,"branch_id":0,"low_stock":null,"out_of_stock":null,"is_for_sale":null,"min_quantity":null,"max_quantity":null,"limit":50,"offset":0}
Session branch_id: not set
=== 2025-04-16 09:15:25 ===
API Filters: {"search":null,"category_id":null,"branch_id":0,"low_stock":null,"out_of_stock":null,"is_for_sale":null,"min_quantity":null,"max_quantity":null,"limit":50,"offset":0}
Session branch_id: not set
=== 2025-04-17 13:03:46 ===
API Filters: {"search":null,"category_id":null,"branch_id":0,"low_stock":null,"out_of_stock":null,"is_for_sale":null,"min_quantity":null,"max_quantity":null,"limit":50,"offset":0}
Session branch_id: not set
=== 2025-05-05 01:46:56 ===
API Filters: {"search":null,"category_id":null,"branch_id":0,"low_stock":null,"out_of_stock":null,"is_for_sale":null,"min_quantity":null,"max_quantity":null,"limit":50,"offset":0}
Session branch_id: not set
=== 2025-05-08 17:05:06 ===
API Filters: {"search":null,"category_id":null,"branch_id":0,"low_stock":null,"out_of_stock":null,"is_for_sale":null,"min_quantity":null,"max_quantity":null,"limit":50,"offset":0}
Session branch_id: not set
=== 2025-05-11 13:36:57 ===
API Filters: {"search":null,"category_id":null,"branch_id":0,"low_stock":null,"out_of_stock":null,"is_for_sale":null,"min_quantity":null,"max_quantity":null,"limit":50,"offset":0}
Session branch_id: not set
=== 2025-05-17 01:06:08 ===
API Filters: {"search":null,"category_id":null,"branch_id":0,"low_stock":null,"out_of_stock":null,"is_for_sale":null,"min_quantity":null,"max_quantity":null,"limit":50,"offset":0}
Session branch_id: not set
