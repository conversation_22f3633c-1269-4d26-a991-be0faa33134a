<?php

// منع الوصول المباشر للملف

if (!defined('BASEPATH')) {

    exit('لا يمكن الوصول المباشر لهذا الملف');

}



// إنشاء إشعارات للمستخدمين عن مواعيد اليوم

// استخدام الجلسة لتجنب التكرار

if (isLoggedIn() && (!isset($_SESSION['last_notification_check']) || (time() - $_SESSION['last_notification_check']) > 300)) { // التحقق كل 5 دقائق فقط

    // تحديث وقت آخر تحقق

    $_SESSION['last_notification_check'] = time();



    // تنظيف الإشعارات المكررة بشكل عشوائي (10% من الوقت)

    if (rand(1, 10) === 1) {

        $cleanupUrl = BASE_URL . 'api/cleanup_notifications.php';

        $ch2 = curl_init();

        curl_setopt($ch2, CURLOPT_URL, $cleanupUrl);

        curl_setopt($ch2, CURLOPT_RETURNTRANSFER, true);

        curl_setopt($ch2, CURLOPT_TIMEOUT, 1);

        curl_setopt($ch2, CURLOPT_COOKIE, session_name() . '=' . session_id());

        curl_setopt($ch2, CURLOPT_SSL_VERIFYPEER, false);

        curl_setopt($ch2, CURLOPT_SSL_VERIFYHOST, false);

        curl_exec($ch2);

        curl_close($ch2);

    }

    // استدعاء API إنشاء الإشعارات بشكل غير متزامن

    $apiUrl = BASE_URL . 'api/create_user_notifications.php';

    $ch = curl_init();

    curl_setopt($ch, CURLOPT_URL, $apiUrl);

    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    curl_setopt($ch, CURLOPT_TIMEOUT, 1); // توقيت قصير جدًا لتجنب تأخير تحميل الصفحة

    curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id()); // إرسال كوكي الجلسة

    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    curl_exec($ch);

    curl_close($ch);

}



// الحصول على إعدادات العملة من قاعدة البيانات

$settingsModel = new Settings($db);

$currencySymbol = $settingsModel->get('system_currency_symbol', 'ر.س');

$currency = $settingsModel->get('system_currency', 'ريال سعودي');

?>

<!DOCTYPE html>

<html dir="rtl" lang="ar">

<head>

    <meta charset="UTF-8">

    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title><?php echo $pageTitle ?? 'نظام إدارة الصالون'; ?></title>



    <!-- Bootstrap RTL CSS -->

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Font Tajawal -->

    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap">

    <!-- jQuery UI CSS -->

    <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">

    <!-- DataTables CSS -->

    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">

    <!-- Select2 CSS -->

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">

    <!-- Daterangepicker CSS -->

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css">

    <!-- إضافة مكتبة toastr CSS -->

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">

    <!-- أنماط مشتركة للنظام -->

    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/common.css">

    <!-- أنماط القائمة الجانبية المتجاوبة -->

    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/sidebar-responsive.css">

    <!-- أنماط كروت لوحة التحكم -->

    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/dashboard-cards.css">



    <!-- مكتبة WhatsApp Client للإرسال من جانب العميل -->



    <!-- إعدادات المتغيرات العامة للجافاسكريبت -->

    <script>

        // تعريف متغيرات النظام لاستخدامها في ملفات جافاسكريبت

        var BASE_URL = '<?php echo BASE_URL; ?>';

        var API_URL = BASE_URL + 'api/';

        var currencySymbol = '<?php echo $currencySymbol; ?>';

        var currency = '<?php echo $currency; ?>';

    </script>

    <!-- WhatsApp notification scripts se cargan en las páginas específicas que los necesitan -->



    <style>

        body {

            font-family: 'Tajawal', sans-serif;

            background-color: #f8f9fa;

        }



        /* Sidebar Styles - تم نقلها إلى ملف sidebar-responsive.css */



        /* Content Styles - تم نقلها إلى ملف sidebar-responsive.css */



        /* Navbar Styles */

        .main-navbar {

            padding: 10px 20px;

            background: white;

            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);

            margin-bottom: 20px;

            border-radius: 5px;

            display: flex;

            justify-content: space-between;

            align-items: center;

        }



        .user-menu .dropdown-menu {

            min-width: 200px;

            right: auto;

            left: 0;

        }



        /* Card styling */

        .card {

            border-radius: 8px;

            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

            margin-bottom: 20px;

        }



        .card-header {

            border-radius: 8px 8px 0 0;

            background-color: #f8f9fa;

            padding: 15px 20px;

            font-weight: 600;

        }



        /* Button styling */

        .btn-primary {

            background-color: #3498db;

            border-color: #3498db;

        }



        .btn-primary:hover {

            background-color: #2980b9;

            border-color: #2980b9;

        }



        /* Status badges */

        .badge.bg-success {

            background-color: #2ecc71 !important;

        }



        .badge.bg-warning {

            background-color: #f39c12 !important;

        }



        .badge.bg-danger {

            background-color: #e74c3c !important;

        }



        /* Select2 RTL fix */

        .select2-container--default .select2-selection--single {

            height: 38px;

            padding: 6px 12px;

            border: 1px solid #ced4da;

        }



        .select2-container--default .select2-selection--single .select2-selection__arrow {

            height: 36px;

        }



        /* Notifications Styles */

        .notifications-dropdown {

            padding: 0;

        }



        .notifications-dropdown .dropdown-header {

            padding: 10px 15px;

            background-color: #f8f9fa;

            border-bottom: 1px solid #e9ecef;

        }



        .notifications-list {

            max-height: 300px;

            overflow-y: auto;

        }



        .notification-item {

            padding: 10px 15px;

            border-bottom: 1px solid #f1f1f1;

            transition: background-color 0.2s;

        }



        .notification-item:hover {

            background-color: #f8f9fa;

        }



        .notification-item.unread {

            background-color: #e8f4fd;

        }



        .notification-item .notification-title {

            font-weight: 600;

            margin-bottom: 5px;

            font-size: 0.9rem;

        }



        .notification-item .notification-text {

            color: #6c757d;

            font-size: 0.85rem;

            margin-bottom: 5px;

        }



        .notification-item .notification-time {

            color: #adb5bd;

            font-size: 0.75rem;

        }



        .notification-item .notification-icon {

            width: 40px;

            height: 40px;

            border-radius: 50%;

            background-color: #e9ecef;

            display: flex;

            align-items: center;

            justify-content: center;

            margin-left: 10px;

        }



        .notification-item .notification-icon.appointment {

            background-color: #d1e7ff;

            color: #0d6efd;

        }



        .notification-item .notification-icon.system {

            background-color: #d1e6dd;

            color: #198754;

        }



        .notification-item .notification-icon.alert {

            background-color: #f8d7da;

            color: #dc3545;

        }



        /* Mobile responsiveness - تم نقلها إلى ملف sidebar-responsive.css */

    </style>

</head>

<body>



<!-- Sidebar -->

<?php include __DIR__ . '/sidebar.php'; ?>



<!-- Page Content -->

<div class="content">

    <!-- Top navbar -->

    <div class="main-navbar">

        <div>



            <span class="fw-bold ms-2"><?php echo $pageTitle ?? 'نظام إدارة الصالون'; ?></span>

        </div>



        <div class="d-flex align-items-center">

            <!-- زر الإشعارات -->

            <div class="notifications-menu dropdown me-3">

                <button class="btn position-relative" type="button" id="notificationsMenu" data-bs-toggle="dropdown" aria-expanded="false">

                    <i class="fas fa-bell"></i>

                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger notifications-count" style="display: none;">0</span>

                </button>

                <div class="dropdown-menu dropdown-menu-end notifications-dropdown" aria-labelledby="notificationsMenu" style="width: 300px; max-height: 400px; overflow-y: auto;">

                    <div class="dropdown-header d-flex justify-content-between align-items-center">

                        <span>الإشعارات</span>

                        <a href="#" class="text-decoration-none small mark-all-read">تعيين الكل كمقروء</a>

                    </div>

                    <div class="notifications-list">

                        <div class="text-center p-3 empty-notifications">

                            <i class="fas fa-bell-slash text-muted mb-2" style="font-size: 2rem;"></i>

                            <p class="text-muted mb-0">لا توجد إشعارات جديدة</p>

                        </div>

                    </div>

                    <div class="dropdown-divider"></div>

                    <a class="dropdown-item text-center" href="../../pages/notifications/index.php">عرض جميع الإشعارات</a>

                </div>

            </div>



            <!-- قائمة المستخدم -->

            <div class="user-menu dropdown">

                <button class="btn dropdown-toggle" type="button" id="userMenu" data-bs-toggle="dropdown" aria-expanded="false">

                    <i class="fas fa-user-circle me-1"></i>

                    <?php echo $_SESSION['user_name'] ?? 'المستخدم'; ?>

                </button>

                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userMenu">

                    <li class="dropdown-item-text text-center text-muted small">

                        <?php echo getUserRoleText(); ?>

                    </li>

                    <li><hr class="dropdown-divider"></li>

                    <li><a class="dropdown-item" href="../../pages/settings/users.php?action=profile"><i class="fas fa-user-edit me-2"></i> الملف الشخصي</a></li>

                    <li><a class="dropdown-item" href="../../pages/settings/users.php?action=password"><i class="fas fa-key me-2"></i> تغيير كلمة المرور</a></li>

                    <li><hr class="dropdown-divider"></li>

                    <li><a class="dropdown-item" href="../../pages/auth/logout.php"><i class="fas fa-sign-out-alt me-2"></i> تسجيل الخروج</a></li>

                </ul>

            </div>

        </div>

    </div>



    <!-- Alert messages -->

    <div id="alerts-container">

        <?php include __DIR__ . '/alerts.php'; ?>

    </div>

