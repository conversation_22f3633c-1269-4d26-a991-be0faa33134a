# دليل رفع نظام الصيانة للخادم المباشر

## 📋 **الملفات المطلوب رفعها:**

### **1. الملفات الأساسية:**
```
📁 الجذر الرئيسي للموقع/
├── maintenance.php                     # انسخ من maintenance_production.php
├── 📁 config/
│   ├── maintenance_mode.txt            # سيتم إنشاؤه تلقائياً
│   └── maintenance_config.json         # انسخ من config/maintenance_config.json
├── 📁 includes/
│   └── maintenance_check.php           # انسخ من includes/maintenance_check.php
├── 📁 admin/
│   └── maintenance_control.php         # انسخ من admin/maintenance_control.php
└── 📁 pages/
    └── dashboard.php                   # محدث بقسم أدوات الإدارة
```

### **2. التحديثات المطلوبة:**
```
📁 config/
└── init.php                           # محدث بتضمين maintenance_check.php
```

## 🚀 **خطوات الرفع:**

### **الخطوة 1: رفع صفحة الصيانة**
1. انسخ محتوى `maintenance_production.php`
2. أنشئ ملف جديد باسم `maintenance.php` في الجذر الرئيسي للموقع
3. الصق المحتوى واحفظ الملف

### **الخطوة 2: رفع ملف التحقق**
1. ارفع `includes/maintenance_check.php` إلى مجلد `includes/`
2. تأكد من أن المسار صحيح

### **الخطوة 3: تحديث ملف التهيئة**
1. افتح `config/init.php` على الخادم
2. أضف هذا السطر بعد السطر 65:
```php
// التحقق من وضع الصيانة
require_once __DIR__ . '/../includes/maintenance_check.php';
```

### **الخطوة 4: رفع لوحة التحكم**
1. ارفع `admin/maintenance_control.php` إلى مجلد `admin/`
2. تأكد من صلاحيات الكتابة لمجلد `config/`

### **الخطوة 5: رفع ملف الإعدادات**
1. ارفع `config/maintenance_config.json` إلى مجلد `config/`

### **الخطوة 6: تحديث لوحة التحكم**
1. افتح `pages/dashboard.php` على الخادم
2. أضف قسم أدوات الإدارة (الأسطر 580-634 من الملف المحدث)

## 🔧 **إعدادات الخادم:**

### **صلاحيات المجلدات:**
```bash
chmod 755 config/
chmod 644 config/maintenance_config.json
chmod 666 config/maintenance_mode.txt  # سيتم إنشاؤه تلقائياً
```

### **التحقق من PHP:**
- تأكد من أن PHP يدعم `file_put_contents()` و `unlink()`
- تأكد من صلاحيات الكتابة في مجلد `config/`

## 🧪 **اختبار النظام:**

### **1. اختبار الوصول:**
```
https://eszy-salon.lovestoblog.com/maintenance.php
```
يجب أن تظهر صفحة الصيانة بشكل صحيح

### **2. اختبار لوحة التحكم:**
```
https://eszy-salon.lovestoblog.com/admin/maintenance_control.php
```
يجب أن تعمل للمدراء فقط

### **3. اختبار التفعيل:**
1. سجل دخول كمدير
2. اذهب للوحة التحكم في الصيانة
3. فعل الصيانة
4. جرب الوصول للصفحات الأخرى

## ⚠️ **مشاكل شائعة وحلولها:**

### **المشكلة: 404 Not Found**
**الحل:**
- تأكد من رفع `maintenance.php` في الجذر الرئيسي
- تحقق من أن اسم الملف صحيح (بدون مسافات)

### **المشكلة: Permission Denied**
**الحل:**
```bash
chmod 755 config/
chmod 644 maintenance.php
chmod 644 includes/maintenance_check.php
```

### **المشكلة: لا يتم إنشاء ملف الصيانة**
**الحل:**
```bash
chmod 777 config/  # مؤقتاً للاختبار
```

### **المشكلة: لا يتم التوجيه لصفحة الصيانة**
**الحل:**
- تحقق من أن `maintenance_check.php` مضمن في `init.php`
- تحقق من سجل الأخطاء

## 📞 **الدعم:**

إذا واجهت مشاكل:
1. تحقق من سجل أخطاء PHP
2. تأكد من صلاحيات الملفات
3. جرب الوصول المباشر لصفحة الصيانة
4. تحقق من أن جميع الملفات مرفوعة بشكل صحيح

## 🎯 **النتيجة المتوقعة:**

بعد الرفع الصحيح:
- ✅ صفحة الصيانة تعمل: `https://eszy-salon.lovestoblog.com/maintenance.php`
- ✅ لوحة التحكم تعمل للمدراء
- ✅ التوجيه التلقائي يعمل عند تفعيل الصيانة
- ✅ المدراء يمكنهم الوصول حتى أثناء الصيانة

---

**ملاحظة مهمة:** احتفظ بنسخة احتياطية من الملفات الأصلية قبل التحديث!
