-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version *******
-- https://www.phpmyadmin.net/
--
-- Host: sql112.infinityfree.com
-- Generation Time: May 31, 2025 at 06:23 PM
-- Server version: 10.6.19-MariaDB
-- PHP Version: 7.2.22

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `if0_38692463_hok`
--

-- --------------------------------------------------------

--
-- Table structure for table `appointments`
--

CREATE TABLE `appointments` (
  `id` int(11) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `employee_id` int(11) DEFAULT NULL,
  `service_id` int(11) DEFAULT NULL,
  `date` date NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `status` enum('booked','waiting','completed','cancelled') NOT NULL DEFAULT 'booked',
  `notes` text DEFAULT NULL,
  `branch_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `appointments`
--

INSERT INTO `appointments` (`id`, `customer_id`, `employee_id`, `service_id`, `date`, `start_time`, `end_time`, `status`, `notes`, `branch_id`, `created_at`, `updated_at`) VALUES
(1, NULL, NULL, NULL, '2025-03-01', '10:00:00', '10:30:00', 'completed', NULL, 1, '2025-02-28 13:00:00', NULL),
(2, NULL, NULL, NULL, '2025-03-01', '11:00:00', '11:45:00', 'completed', NULL, 1, '2025-02-28 14:30:00', NULL),
(3, NULL, NULL, NULL, '2025-03-01', '12:00:00', '12:30:00', 'completed', NULL, 1, '2025-02-28 15:15:00', NULL),
(4, NULL, NULL, NULL, '2025-03-01', '10:00:00', '10:30:00', 'completed', NULL, NULL, '2025-02-28 10:00:00', NULL),
(5, NULL, NULL, NULL, '2025-03-01', '11:00:00', '11:45:00', 'completed', NULL, NULL, '2025-02-28 12:00:00', NULL),
(6, NULL, NULL, NULL, '2025-03-01', '12:00:00', '12:30:00', 'completed', NULL, NULL, '2025-02-28 13:30:00', NULL),
(7, NULL, NULL, NULL, '2025-03-01', '10:00:00', '10:30:00', 'completed', NULL, NULL, '2025-02-28 09:00:00', NULL),
(8, NULL, NULL, NULL, '2025-03-01', '11:00:00', '11:45:00', 'completed', NULL, NULL, '2025-02-28 11:00:00', NULL),
(9, NULL, NULL, NULL, '2025-03-01', '12:00:00', '12:30:00', 'completed', NULL, NULL, '2025-02-28 12:30:00', NULL),
(10, NULL, NULL, NULL, '2025-03-01', '14:00:00', '14:45:00', 'completed', NULL, 1, '2025-02-28 16:00:00', NULL),
(11, NULL, NULL, NULL, '2025-04-05', '09:00:00', '10:00:00', 'booked', '', NULL, '2025-04-04 23:20:44', NULL),
(12, NULL, NULL, NULL, '2025-04-05', '09:00:00', '10:00:00', 'booked', '', NULL, '2025-04-04 23:39:36', NULL),
(13, NULL, NULL, NULL, '2025-04-05', '09:00:00', '10:00:00', 'booked', '', 1, '2025-04-04 23:44:15', NULL),
(14, 1004, 1, 72, '2025-04-06', '10:30:00', '11:00:00', 'booked', '', 1, '2025-04-06 07:14:12', NULL),
(15, 3015, 4, 74, '2025-04-08', '09:00:00', '09:30:00', 'cancelled', '', 1, '2025-04-07 23:17:14', '2025-04-08 19:31:30'),
(16, 3028, 1, 73, '2025-04-09', '18:00:00', '18:30:00', 'booked', 'وصل 500 مع خصم 20%', 1, '2025-04-08 20:07:09', NULL),
(17, 1004, 1, 56, '2025-04-19', '19:30:00', '20:00:00', 'booked', '', 1, '2025-04-19 17:23:32', NULL),
(18, 3081, 1, 58, '2025-04-20', '11:00:00', '11:30:00', 'cancelled', 'ككمنمنمم', 1, '2025-04-19 22:17:56', '2025-04-19 22:22:03'),
(19, 3081, 1, 58, '2025-04-20', '11:00:00', '11:30:00', 'cancelled', 'ككمنمنمم', 1, '2025-04-19 22:17:58', '2025-04-19 22:22:27');

-- --------------------------------------------------------

--
-- Table structure for table `branches`
--

CREATE TABLE `branches` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `address` varchar(255) DEFAULT NULL,
  `phone` varchar(200) DEFAULT NULL,
  `manager_id` int(11) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `branches`
--

INSERT INTO `branches` (`id`, `name`, `address`, `phone`, `manager_id`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'البدرواي صالون', 'المنصوره /طناح / طريق المنصوره / ميت فارس', '01009036186/01022429488', 1, 1, '2025-04-04 06:06:39', '2025-04-09 14:47:00');

-- --------------------------------------------------------

--
-- Table structure for table `customers`
--

CREATE TABLE `customers` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL COMMENT 'عنوان العميل',
  `birthday` date DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `loyalty_points` int(11) DEFAULT 0,
  `branch_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `customers`
--

INSERT INTO `customers` (`id`, `name`, `phone`, `email`, `address`, `birthday`, `notes`, `loyalty_points`, `branch_id`, `created_at`, `updated_at`) VALUES
(1, 'احمد القصبي', '01020305672', NULL, NULL, NULL, NULL, 0, 1, '2025-03-21 20:34:08', '2025-03-21 20:34:08'),
(2, 'ايهاب زكى', '01270225010', NULL, NULL, NULL, NULL, 0, 1, '2025-03-21 20:43:58', '2025-03-21 20:43:58'),
(3, 'حمدى الجميلي', '01096365507', NULL, NULL, NULL, NULL, 0, 1, '2025-03-21 21:25:18', '2025-03-21 21:25:18'),
(4, 'خالد', '00', NULL, NULL, NULL, NULL, 0, 1, '2025-03-22 14:28:07', '2025-03-22 14:28:07'),
(5, 'جو ميدكال', '01200633063', '', 'الكفر', '0000-00-00', '', 0, 1, '2025-03-22 15:07:55', '2025-05-25 12:07:24'),
(6, 'محمد المتولي', '01281341327', NULL, NULL, NULL, NULL, 0, 1, '2025-03-22 18:13:15', '2025-03-22 18:13:15'),
(1004, 'ابراهيم حسن علي', '01020264857', '', 'النسميه', '0000-00-00', '', 0, 1, '2025-03-22 20:05:34', '2025-05-31 19:59:55'),
(2004, 'حسن شاكر حسن', '01212294635', NULL, NULL, NULL, NULL, 0, 1, '2025-03-22 21:12:56', '2025-03-22 21:12:56'),
(2005, 'عبد الرحمن نبيل', '01279572216', NULL, NULL, NULL, NULL, 0, 1, '2025-03-22 21:13:26', '2025-03-22 21:13:26'),
(2006, 'المتولى ابو المجد', '01009783004', NULL, NULL, NULL, NULL, 0, 1, '2025-03-22 21:52:37', '2025-03-22 21:52:37'),
(3004, 'زياد جمال', '01044012245', NULL, NULL, NULL, NULL, 0, 1, '2025-03-23 19:33:39', '2025-03-23 19:33:39'),
(3005, 'عمر مجدى', '01026497464', NULL, NULL, NULL, NULL, 0, 1, '2025-03-23 20:00:55', '2025-03-23 20:00:55'),
(3006, 'محمدفواد', '01202024891', '', NULL, '0000-00-00', '', 0, 1, '2025-03-23 22:02:45', '2025-05-10 22:57:46'),
(3007, 'شيكا', '01005819723', NULL, NULL, NULL, NULL, 0, 1, '2025-03-23 22:50:40', '2025-03-23 22:50:40'),
(3008, 'محمدمدين', '01286767090', NULL, NULL, NULL, NULL, 0, 1, '2025-03-25 15:52:32', '2025-03-25 15:52:32'),
(3009, 'كريم نبيل', '01211204271', NULL, NULL, NULL, NULL, 0, 1, '2025-03-25 19:22:36', '2025-03-25 19:22:36'),
(3010, 'احمد رجب', '01025340122', NULL, NULL, NULL, NULL, 0, 1, '2025-03-25 19:37:37', '2025-03-25 19:37:37'),
(3011, 'محمودابراهيم', '01013384554', NULL, NULL, NULL, NULL, 0, 1, '2025-03-25 23:19:21', '2025-03-25 23:19:21'),
(3012, 'اسلام ابراهيم', '01000279476', NULL, NULL, NULL, NULL, 0, 1, '2025-03-25 23:20:06', '2025-03-25 23:20:06'),
(3013, 'ماهر نبيل المرجبي', '01110002172', '', '', '0000-00-00', '', 0, 1, '2025-03-26 12:47:16', '2025-05-20 09:07:40'),
(3014, 'على مسعد', '01014622474', '', NULL, '0000-00-00', '', 0, 1, '2025-03-26 18:55:32', '2025-05-09 15:54:39'),
(3015, 'احمد البراكبي', '01212867459', NULL, NULL, NULL, NULL, 0, 1, '2025-03-27 21:14:34', '2025-03-27 21:14:34'),
(3016, 'احمدالسيد', '01279306729', NULL, NULL, NULL, NULL, 0, 1, '2025-03-27 22:14:55', '2025-03-27 22:14:55'),
(3017, 'احمد', '01204891920', NULL, NULL, NULL, NULL, 0, 1, '2025-03-28 17:30:55', '2025-03-28 17:30:55'),
(3018, 'احمد الصعيدى', '01067465037', NULL, NULL, NULL, NULL, 0, 1, '2025-03-28 18:49:43', '2025-03-28 18:49:43'),
(3019, 'يوسف كمال', '01559459005', NULL, NULL, NULL, NULL, 0, 1, '2025-03-28 19:08:26', '2025-03-28 19:08:26'),
(3020, 'احمد محمد فتحي', '01069581187', '', '', '0000-00-00', '', 0, 1, '2025-03-31 07:05:56', '2025-05-30 20:18:39'),
(3021, 'عبد الرحمان', '01276503589', NULL, NULL, NULL, NULL, 0, 1, '2025-03-31 07:11:28', '2025-03-31 07:11:28'),
(3022, 'حازم', '01060820703', NULL, NULL, NULL, NULL, 0, 1, '2025-04-01 12:49:06', '2025-04-01 12:49:06'),
(3023, 'محمد احمد', '01211706624', NULL, NULL, NULL, NULL, 0, 1, '2025-04-02 13:16:23', '2025-04-02 13:16:23'),
(3024, 'فارس', '01212288474', NULL, NULL, NULL, NULL, 0, 1, '2025-04-04 12:17:03', '2025-04-04 12:17:03'),
(3025, 'عبد الحميد', '01023293807', NULL, NULL, NULL, NULL, 0, 1, '2025-04-05 15:00:54', '2025-04-05 15:00:54'),
(3028, 'عبد الحى عبد الحكيم', '01007614336', '<EMAIL>', NULL, '0000-00-00', '', 0, 1, '2025-04-08 20:05:11', NULL),
(3029, 'محمد يحيي', '01027660785', '0', NULL, NULL, '', 0, 1, '2025-04-10 15:42:35', NULL),
(3030, 'محمد احمد البسطؤيسي', '01060539481', '', '', '0000-00-00', '', 0, 1, '2025-04-10 15:51:37', '2025-05-29 11:30:19'),
(3031, 'اشرف', '01550867706', '0', NULL, NULL, '', 0, 1, '2025-04-10 16:32:22', NULL),
(3032, 'عمر موسي', '01091386947', '', NULL, NULL, '', 0, 1, '2025-04-10 17:51:52', NULL),
(3033, 'احمد السباعي', '01550759339', '0', NULL, NULL, '', 0, 1, '2025-04-11 13:40:50', NULL),
(3034, 'اسلام سامي', '010978360414', '0', NULL, NULL, '', 0, 1, '2025-04-11 14:09:28', NULL),
(3035, 'خالد', '01050384483', '0', NULL, NULL, '', 0, 1, '2025-04-11 14:48:55', NULL),
(3036, 'محمد القصبي', '01050384482', '0', NULL, NULL, '', 0, 1, '2025-04-11 14:51:53', NULL),
(3037, 'محمد الفشفعي', '010650974735', '', NULL, NULL, '', 0, 1, '2025-04-12 11:32:26', NULL),
(3038, 'علي السعيد الشا فعي', '011136526241', '0', NULL, NULL, '', 0, 1, '2025-04-12 11:41:06', NULL),
(3039, 'علي السعيد الشا فعي', '01113652621', '0', NULL, NULL, '', 0, 1, '2025-04-12 11:42:58', NULL),
(3040, 'احمد محمد', '01278437417', '0', NULL, NULL, '', 0, 1, '2025-04-12 11:51:39', NULL),
(3041, 'اسامه', '01211027271', '0', NULL, NULL, '', 0, 1, '2025-04-12 13:51:34', NULL),
(3042, 'ابوفادي', '01122443330', '', NULL, NULL, '', 0, 1, '2025-04-12 15:46:46', NULL),
(3043, 'ايهاب درويش', '01023256766', '', NULL, NULL, '', 0, 1, '2025-04-12 16:05:07', NULL),
(3044, 'عماد جمال', '01555350331', '', NULL, NULL, '', 0, 1, '2025-04-12 16:22:46', NULL),
(3045, 'محمد زياده', '01009016655', '', NULL, NULL, '', 0, 1, '2025-04-12 22:27:24', NULL),
(3046, 'محمد العزاب', '01014870851', '', NULL, NULL, '', 0, 1, '2025-04-12 22:37:41', NULL),
(3047, 'محمودخالد', '01028834393', '', NULL, NULL, '', 0, 1, '2025-04-13 00:00:52', NULL),
(3048, 'احمدسمير', '01552588599', '0', NULL, NULL, '', 0, 1, '2025-04-13 13:49:45', NULL),
(3049, 'محمدشابنه', '01205530860', '', NULL, NULL, '', 0, 1, '2025-04-13 15:09:19', NULL),
(3050, 'عبدلله البيومي', '01281040022', '', NULL, NULL, '', 0, 1, '2025-04-13 17:05:09', NULL),
(3051, 'جمال عبد الناصر', '01063881626', '', NULL, NULL, '', 0, 1, '2025-04-13 17:42:57', NULL),
(3052, 'محمود الطنطاوي', '01005275739', '', NULL, '0000-00-00', '', 0, 1, '2025-04-13 18:15:34', '2025-04-13 18:20:15'),
(3053, 'ابو مازن', '01029664876', '', NULL, NULL, '', 0, 1, '2025-04-13 20:22:33', NULL),
(3054, 'خالد المرسي', '01277101488', '0', NULL, NULL, '', 0, 1, '2025-04-13 20:31:52', NULL),
(3055, 'عبدالرحمن', '01550069011', '0', NULL, NULL, '', 0, 1, '2025-04-13 20:59:21', NULL),
(3056, 'محمودعبدالعظيم', '01222512077', '0', NULL, NULL, '', 0, 1, '2025-04-14 11:11:35', NULL),
(3057, 'علي طه', '01277052319', '', NULL, NULL, '', 0, 1, '2025-04-14 14:44:22', NULL),
(3058, 'محمد احمد', '0122608740060', '', NULL, NULL, '', 0, 1, '2025-04-14 14:48:42', NULL),
(3059, 'احمدحمدي', '01220421895', '0', NULL, NULL, '', 0, 1, '2025-04-14 16:56:49', NULL),
(3060, 'ادهم رضا', '01283051419', '0', NULL, NULL, '', 0, 1, '2025-04-14 18:29:21', NULL),
(3061, 'شريف', '01064967559', '0', NULL, NULL, '', 0, 1, '2025-04-15 12:31:29', NULL),
(3062, 'اسلام العشري', '01026424543', '0', NULL, NULL, '', 0, 1, '2025-04-15 14:11:36', NULL),
(3063, 'مصطفي', '01061650346', '', NULL, NULL, '', 0, 1, '2025-04-15 17:51:45', NULL),
(3064, 'محمد المتولي', '01205499310', '', NULL, NULL, '', 0, 1, '2025-04-15 17:59:56', NULL),
(3065, 'سعد السيد سعد', '01030474154', '', '', '0000-00-00', '', 0, 1, '2025-04-15 19:46:12', '2025-05-24 19:54:46'),
(3066, 'محمد الحسيني', '01024958044', '', NULL, NULL, '', 0, 1, '2025-04-15 20:05:10', NULL),
(3067, 'هشام مرزق', '01011899465', '', NULL, NULL, '', 0, 1, '2025-04-15 23:23:57', NULL),
(3068, 'احمد فتحي', '01111000136', '', NULL, NULL, '', 0, 1, '2025-04-16 18:16:57', NULL),
(3069, 'خالد علاء', '01030823436', '', NULL, NULL, '', 0, 1, '2025-04-16 19:25:57', NULL),
(3070, 'مستر محمد مجدي', '01153569634', '', NULL, NULL, '', 0, 1, '2025-04-16 21:15:43', NULL),
(3071, 'جلال محمد', '0109493923', '', NULL, NULL, '', 0, 1, '2025-04-16 22:27:43', NULL),
(3072, 'عبدالرحمن محمد', '0107032180', '', NULL, NULL, '', 0, 1, '2025-04-17 17:54:43', NULL),
(3073, 'خالد عمرو', '01551806720', '', NULL, NULL, '', 0, 1, '2025-04-18 12:50:29', NULL),
(3074, 'خالد علاء', '01097561801', '0', NULL, NULL, '', 0, 1, '2025-04-18 14:42:40', NULL),
(3075, 'محمد', '01066624260', '0', NULL, NULL, '', 0, 1, '2025-04-18 15:27:55', NULL),
(3076, 'عمرو', '01015115726', '0', NULL, NULL, '', 0, 1, '2025-04-18 15:41:45', NULL),
(3077, 'محمودمحمد', '01420390007', '0', NULL, NULL, '', 0, 1, '2025-04-18 16:04:55', NULL),
(3078, 'محمد اشرف', '01093168824', '0', NULL, NULL, '', 0, 1, '2025-04-19 12:19:13', NULL),
(3079, 'test', '01556262660', '<EMAIL>', NULL, '0000-00-00', '', 0, NULL, '2025-04-19 16:37:45', '2025-04-20 05:56:28'),
(3080, 'محمد', '01145220845', '0', NULL, NULL, '', 0, 1, '2025-04-19 20:35:39', NULL),
(3081, 'محمد البدراوي', '01009036186', '', NULL, '0000-00-00', '', 0, 1, '2025-04-19 22:15:38', NULL),
(3082, 'احمد مسعد', '01551261500', '', 'الخليج', '0000-00-00', '', 0, 1, '2025-04-20 10:33:50', '2025-05-21 11:57:50'),
(3083, 'حمدي احمد', '01062431076', '', NULL, NULL, '', 0, 1, '2025-04-20 14:22:31', NULL),
(3084, 'محمد السيدمحيله', '01092952592', '', 'الخليج', '0000-00-00', '', 0, 1, '2025-04-20 18:14:25', '2025-05-27 18:33:08'),
(3085, 'محمد سامح', '01557697175', '0', NULL, NULL, '', 0, 1, '2025-04-20 20:25:23', NULL),
(3086, 'اسلام', '01024733254', '0', NULL, NULL, '', 0, 1, '2025-04-21 11:56:26', NULL),
(3087, 'عبدالعليم', '01288150405', '0', NULL, NULL, '', 0, 1, '2025-04-21 13:29:26', NULL),
(3088, 'محمدالسيد الطهري', '01030124289', '', '', '0000-00-00', '', 0, 1, '2025-04-21 14:03:48', '2025-05-29 11:51:53'),
(3089, 'محمد', '01040169273', '0', NULL, NULL, '', 0, 1, '2025-04-21 14:59:15', NULL),
(3090, 'حمدي', '01066153164', '0', NULL, NULL, '', 0, 1, '2025-04-21 16:25:53', NULL),
(3091, 'محمد ثروت', '01010123444', '', NULL, NULL, '', 0, 1, '2025-04-21 19:19:22', NULL),
(3092, 'محم الفرحاتي', '01066376140', '', NULL, NULL, '', 0, 1, '2025-04-21 19:56:00', NULL),
(3093, 'عبدلله مروان', '01212707092', '', NULL, NULL, '', 0, 1, '2025-04-22 17:56:56', NULL),
(3094, 'مصطفي الغندور', '01068206351', '0', NULL, NULL, '', 0, 1, '2025-04-22 19:01:32', NULL),
(3095, 'محمد طارق', '01556855298', '', NULL, NULL, '', 0, 1, '2025-04-22 21:04:06', NULL),
(3096, 'اسلام عمر بدر', '01095608736', '', NULL, '0000-00-00', '', 0, 1, '2025-04-23 15:04:54', '2025-05-18 19:36:27'),
(3097, 'حمدي', '01104166812', '', NULL, NULL, '', 0, 1, '2025-04-24 11:55:16', NULL),
(3098, 'تامر الاسيد', '01018040543', '', NULL, NULL, '', 0, 1, '2025-04-24 13:21:11', NULL),
(3099, 'احمد البنز', '01021334567', '', NULL, NULL, '', 0, 1, '2025-04-24 15:44:41', NULL),
(3100, 'محمد اكرام محمود', '01021146230', '', 'طناح', '0000-00-00', '', 0, 1, '2025-04-24 17:00:41', '2025-05-31 17:28:02'),
(3101, 'زياد محمود', '01026326059', '', NULL, '0000-00-00', '', 0, 1, '2025-04-24 17:59:38', NULL),
(3102, 'محمد علاق', '01000208201', '', NULL, '0000-00-00', '', 0, 1, '2025-04-24 18:17:39', NULL),
(3103, 'عبدلله البدوي', '01020258694', '', NULL, NULL, '', 0, 1, '2025-04-24 19:16:32', NULL),
(3104, 'ابراهيم محمد ابراهيم', '01091502124', '', NULL, '0000-00-00', '', 0, 1, '2025-04-24 19:34:54', '2025-05-19 18:50:22'),
(3105, 'محمد ثروت', '01019536668', '', NULL, NULL, '', 0, 1, '2025-04-24 21:10:38', NULL),
(3106, 'محمد العدل الحربي', '01203860990', '', NULL, '0000-00-00', '', 0, 1, '2025-04-24 21:15:38', '2025-05-19 20:35:14'),
(3107, 'بيسم مخيمر', '01062131769', '', NULL, NULL, '', 0, 1, '2025-04-24 23:46:26', NULL),
(3108, 'اسلام', '01097836041', '0', NULL, NULL, '', 0, 1, '2025-04-25 10:40:41', NULL),
(3109, 'عمر احمد', '01289245743', '', NULL, NULL, '', 0, 1, '2025-04-25 13:06:43', NULL),
(3110, 'كريم المتولي', '01096588914', '', NULL, '0000-00-00', '', 0, 1, '2025-04-25 14:38:14', '2025-05-18 15:03:46'),
(3111, 'محمود محمد', '01004490553', '', NULL, NULL, '', 0, 1, '2025-04-25 16:14:41', NULL),
(3112, 'شرايف كمال', '01005640567', '', NULL, NULL, '', 0, 1, '2025-04-25 21:20:16', NULL),
(3113, 'غبد الناصر عبد الوهاب', '01016771120', '', NULL, NULL, '', 0, 1, '2025-04-25 21:46:29', NULL),
(3114, 'محمد احمد رمضان', '01150288224', '', 'كوم بني مراس', '0000-00-00', '', 0, 1, '2025-04-26 12:44:20', '2025-05-24 11:56:03'),
(3115, 'حسام اسه', '01062831861', '', NULL, NULL, '', 0, 1, '2025-04-26 16:57:24', NULL),
(3116, 'احمد السيد علي', '01028144748', '', 'النسميه', '0000-00-00', '', 0, 1, '2025-04-27 10:16:00', '2025-05-20 15:06:03'),
(3117, 'عبد الله السيد البسيوني', '01271202665', '', 'الكفر', '0000-00-00', '', 0, 1, '2025-04-27 11:18:37', '2025-05-24 17:38:46'),
(3118, 'كريم محمد العدل', '01278304606', '0', NULL, NULL, '', 0, 1, '2025-04-27 12:59:38', NULL),
(3119, 'عمر سعيد', '01153420436', '', NULL, NULL, '', 0, 1, '2025-04-27 16:49:54', NULL),
(3120, 'محمد عبدلله', '01064436682', '', NULL, NULL, '', 0, 1, '2025-04-27 16:51:56', NULL),
(3121, 'احمد ابو علاء', '01212744060', '', NULL, NULL, '', 0, 1, '2025-04-27 19:13:08', NULL),
(3122, 'احمد عماد', '01091232691', '', NULL, NULL, '', 0, 1, '2025-04-27 19:20:46', NULL),
(3123, 'ثروت زينهم', '0128875132', '', NULL, NULL, '', 0, 1, '2025-04-28 10:32:01', NULL),
(3124, 'زيزو', '01040939748', '0', NULL, NULL, '', 0, 1, '2025-04-28 14:17:32', NULL),
(3125, 'خالد زياده', '01001741661', '', NULL, NULL, '', 0, 1, '2025-04-28 15:41:33', NULL),
(3126, 'ابراهيم عز الدين', '01004843887', '', NULL, NULL, '', 0, 1, '2025-04-28 15:43:16', NULL),
(3127, 'عمرو علي', '01019358514', '', NULL, NULL, '', 0, 1, '2025-04-28 15:44:34', NULL),
(3128, 'عبد', '01003678896', '0', NULL, NULL, '', 0, 1, '2025-04-28 17:37:50', NULL),
(3129, 'يوسف', '01068567051', '0', NULL, NULL, '', 0, 1, '2025-04-29 12:57:05', NULL),
(3130, 'محمدالسيد الجعيدي', '01285587111', '', '', '0000-00-00', '', 0, 1, '2025-04-29 13:50:57', '2025-05-31 16:03:31'),
(3131, 'محمد اكرم محمود', '01032131292', '', 'طناح', '0000-00-00', '', 0, 1, '2025-04-29 14:31:26', '2025-05-20 18:39:31'),
(3132, 'محمود', '01212110302', '', NULL, NULL, '', 0, 1, '2025-04-30 12:05:30', NULL),
(3133, 'المانؤ', '01272389424', '', NULL, NULL, '', 0, 1, '2025-04-30 14:37:42', NULL),
(3134, 'محمد عبد العزيز', '01099254666', '', '', '0000-00-00', '', 0, 1, '2025-04-30 15:05:14', '2025-05-22 23:16:44'),
(3135, 'السعيد', '01024095179', '', NULL, NULL, '', 0, 1, '2025-04-30 18:43:54', NULL),
(3136, 'محمد', '01002156062', '', NULL, NULL, '', 0, 1, '2025-04-30 19:04:05', NULL),
(3137, 'فادي محمد العشري', '01225108833', '', NULL, NULL, '', 0, 1, '2025-04-30 19:20:09', NULL),
(3138, 'محمد  ساسي', '01018261401', '', NULL, NULL, '', 0, 1, '2025-04-30 19:44:16', NULL),
(3139, 'احمد التونسي', '01206523967', '', NULL, NULL, '', 0, 1, '2025-05-01 16:48:20', NULL),
(3140, 'صالح المسكر', '01091492216', '', NULL, NULL, '', 0, 1, '2025-05-01 16:49:23', NULL),
(3141, 'محمد سالم', '01021110055', '', NULL, NULL, '', 0, 1, '2025-05-01 18:20:02', NULL),
(3142, 'ياسر محمد', '01069158074', '', NULL, NULL, '', 0, 1, '2025-05-01 18:27:16', NULL),
(3143, 'السيد السعيد السيد', '01032646608', '', 'كفر طناح', '0000-00-00', '', 0, 1, '2025-05-01 18:58:52', '2025-05-28 18:37:28'),
(3144, 'محمد سعد الحربي', '01228122213', '', NULL, NULL, '', 0, 1, '2025-05-01 22:51:35', NULL),
(3145, 'نادر السيد', '01063814402', '', NULL, NULL, '', 0, 1, '2025-05-01 23:23:52', NULL),
(3146, 'عبد السلام', '01008935529', '', NULL, NULL, '', 0, 1, '2025-05-01 23:25:23', NULL),
(3147, 'محمد الدهشات', '01203824082', '', NULL, NULL, '', 0, 1, '2025-05-02 11:12:22', NULL),
(3148, 'ابو عمر احمد شعبان', '01274909739', '', '', '0000-00-00', '', 0, 1, '2025-05-02 15:30:58', '2025-05-26 16:42:55'),
(3149, 'ابو ريتاج', '01097522251', '', NULL, NULL, '', 0, 1, '2025-05-02 16:18:08', NULL),
(3150, 'حسين محمد', '01061395043', '', NULL, NULL, '', 0, 1, '2025-05-02 18:33:44', NULL),
(3151, 'فتحي احمد', '0106496850', '', NULL, NULL, '', 0, 1, '2025-05-03 16:22:21', NULL),
(3152, 'خالد محمد', '01550070116', '', NULL, NULL, '', 0, 1, '2025-05-03 18:58:08', NULL),
(3153, 'محمد رضا', '01201250608', '', NULL, NULL, '', 0, 1, '2025-05-03 21:13:35', NULL),
(3154, 'السعيد جمال', '01007414485', '', NULL, NULL, '', 0, 1, '2025-05-03 22:00:50', NULL),
(3155, 'احمدعيد', '01007424971', '', NULL, NULL, '', 0, 1, '2025-05-04 13:21:46', NULL),
(3156, 'محمد الشافعي', '01065974735', '', NULL, NULL, '', 0, 1, '2025-05-04 14:56:25', NULL),
(3157, 'عبدالراحمن حسن', '01555889401', '', NULL, NULL, '', 0, 1, '2025-05-04 17:27:08', NULL),
(3158, 'ابو رضا', '01012094494', '', NULL, NULL, '', 0, 1, '2025-05-04 22:11:09', NULL),
(3159, 'قفهمي', '01093799324', '', NULL, NULL, '', 0, 1, '2025-05-05 15:05:02', NULL),
(3160, 'اشرف', '01201489771', '', NULL, NULL, '', 0, 1, '2025-05-05 16:44:08', NULL),
(3161, 'شهدي', '01155530500', '', NULL, '0000-00-00', '', 0, 1, '2025-05-05 18:33:46', NULL),
(3162, 'محمود احمد العدل', '01220974632', '', NULL, NULL, '', 0, 1, '2025-05-05 18:47:34', NULL),
(3163, 'احمد رضا محمد', '01272919395', '', NULL, NULL, '', 0, 1, '2025-05-05 21:36:55', NULL),
(3164, 'علي', '01229517824', '', NULL, NULL, '', 0, 1, '2025-05-06 09:37:06', NULL),
(3165, 'شريف', '01018551564', '', NULL, NULL, '', 0, 1, '2025-05-06 10:05:34', NULL),
(3166, 'احمد سعد', '01212248568', '', NULL, NULL, '', 0, 1, '2025-05-06 14:50:12', NULL),
(3167, 'عبدالله زكي', '01285522555', '', NULL, NULL, '', 0, 1, '2025-05-06 17:30:35', NULL),
(3168, 'سيف باشير', '01554554518', '', NULL, NULL, '', 0, 1, '2025-05-06 17:33:07', NULL),
(3169, 'خالد حامد السعيد', '01284907979', '', 'طناح', '0000-00-00', '', 0, 1, '2025-05-07 17:34:16', '2025-05-28 14:26:45'),
(3170, 'محمود', '01097120914', '', NULL, '0000-00-00', '', 0, 1, '2025-05-07 20:44:13', NULL),
(3171, 'هشام السبيتي', '01061593029', '', NULL, NULL, '', 0, 1, '2025-05-07 20:52:46', NULL),
(3172, 'احمد الكومي', '01558540554', '', NULL, NULL, '', 0, 1, '2025-05-07 21:13:10', NULL),
(3173, 'محمود', '01027088225', '', NULL, NULL, '', 0, 1, '2025-05-08 09:34:02', NULL),
(3174, 'محمد محمود البنداري', '01224189148', '', 'الكفر', '0000-00-00', '', 0, 1, '2025-05-08 13:01:43', '2025-05-22 12:11:05'),
(3175, 'رضا علاء', '01557822112', '', NULL, NULL, '', 0, 1, '2025-05-08 14:22:35', NULL),
(3176, 'محمد عبد الحميد', '01207557566', '', NULL, NULL, '', 0, 1, '2025-05-08 15:16:01', NULL),
(3177, 'محمود حسين', '01205906565', '', NULL, NULL, '', 0, 1, '2025-05-08 15:25:27', NULL),
(3178, 'محمد عبد العزيز', '01270228848', '', NULL, NULL, '', 0, 1, '2025-05-08 15:31:03', NULL),
(3179, 'عباس محمد', '01151795391', '', NULL, '0000-00-00', '', 0, 1, '2025-05-08 15:49:17', NULL),
(3180, 'احمد عبد الحميد', '01024691738', '', NULL, NULL, '', 0, 1, '2025-05-08 16:38:10', NULL),
(3181, 'محمد رجب', '0101247787', '', NULL, NULL, '', 0, 1, '2025-05-08 16:41:18', NULL),
(3182, 'ابراهيم محرم', '01004389154', '', NULL, NULL, '', 0, 1, '2025-05-08 17:45:17', NULL),
(3183, 'عبد الراحمن السعيد الخولي', '01551902722', '', '', '0000-00-00', '', 0, 1, '2025-05-08 18:33:51', '2025-05-29 18:50:33'),
(3184, 'كريم عبد النبي عوض', '01558261144', '', 'طناح', '0000-00-00', '', 0, 1, '2025-05-08 18:34:40', '2025-05-21 17:08:06'),
(3185, 'lihnpkluhnhpkuj', '01077440656546', '0', NULL, NULL, 'kol;kl;kl;', 0, 1, '2025-05-08 20:20:01', NULL),
(3186, 'احمد البدراوي', '01022429488', '', NULL, NULL, '', 0, 1, '2025-05-08 20:25:46', NULL),
(3187, 'زياد شبانه', '01276733340', '', NULL, NULL, '', 0, 1, '2025-05-08 21:32:48', NULL),
(3188, 'مصطفي', '01272198429', '', NULL, NULL, '', 0, 1, '2025-05-09 11:14:36', NULL),
(3189, 'سمير طارق', '01225349458', '', NULL, NULL, '', 0, 1, '2025-05-09 11:47:50', NULL),
(3190, 'طارق حسين  عرفات', '0128/0577293', '', 'طناح', '0000-00-00', '', 0, 1, '2025-05-09 15:39:24', '2025-05-23 14:59:36'),
(3191, 'احمد زكي', '01229287743', '', NULL, '0000-00-00', '', 0, 1, '2025-05-09 17:23:31', NULL),
(3192, 'محمد السيد', '01080001262', '', NULL, NULL, '', 0, 1, '2025-05-10 10:44:15', NULL),
(3193, 'توفيق محمد', '01287061506', '', NULL, NULL, '', 0, 1, '2025-05-10 11:14:53', NULL),
(3194, 'عبد الرحن جمال', '01025726826', '', NULL, NULL, '', 0, 1, '2025-05-10 13:20:06', NULL),
(3195, 'عمرعبد العزيز', '01069057179', '', NULL, NULL, '', 0, 1, '2025-05-10 15:41:51', NULL),
(3196, 'ابرهيم', '01009302399', '', NULL, NULL, '', 0, 1, '2025-05-10 15:43:21', NULL),
(3197, 'محمد حسن عطيه', '01023725315', '', NULL, NULL, '', 0, 1, '2025-05-10 16:54:51', NULL),
(3198, 'يوسف طارق', '01145957486', '', NULL, NULL, '', 0, 1, '2025-05-10 18:34:57', NULL),
(3199, 'محمد', '01554940891', '0', NULL, NULL, '', 0, 1, '2025-05-10 18:53:31', NULL),
(3200, 'امير عز', '01229804734', '', NULL, NULL, '', 0, 1, '2025-05-11 12:29:58', NULL),
(3201, 'طارق احمد', '01023124211', '', NULL, NULL, '', 0, 1, '2025-05-11 14:06:32', NULL),
(3202, 'محمد العدل', '01223272300', '', NULL, NULL, '', 0, 1, '2025-05-11 14:45:36', NULL),
(3203, 'طاهر', '01055459217', '', NULL, NULL, '', 0, 1, '2025-05-11 16:41:05', NULL),
(3204, 'محمد المتولي ابراهيم', '01201071640', '', 'كفر طناح', '0000-00-00', '', 0, 1, '2025-05-11 18:12:08', '2025-05-26 19:21:47'),
(3205, 'عبد الرحمن منتصر حلمي', '01032708324', '', 'ديبو عوام', '0000-00-00', '', 0, 1, '2025-05-11 21:39:03', '2025-05-23 21:25:57'),
(3206, 'ابو جني', '01003532773', '', NULL, NULL, '', 0, 1, '2025-05-12 13:28:43', NULL),
(3207, 'ياسر ابو العز', '01069505472', '', NULL, NULL, '', 0, 1, '2025-05-12 16:03:25', NULL),
(3208, 'محمد رضا رعبد القادر', '01206838448', '', NULL, NULL, '', 0, 1, '2025-05-12 16:08:50', NULL),
(3209, 'علي محمدعلي', '01146322260', '', 'طناح', '0000-00-00', '', 0, 1, '2025-05-12 20:12:44', '2025-05-26 18:51:37'),
(3210, 'ابو عمر', '01274909736', '', NULL, NULL, '', 0, 1, '2025-05-13 11:49:11', NULL),
(3211, 'علي شبانه', '01094706696', '', NULL, NULL, '', 0, 1, '2025-05-13 12:52:12', NULL),
(3212, 'محمود', '01203900091', '', NULL, NULL, '', 0, 1, '2025-05-13 15:57:30', NULL),
(3213, 'عبده', '01063764308', '', 'الشناوي', '0000-00-00', '', 0, 1, '2025-05-13 16:02:58', '2025-05-23 16:04:33'),
(3214, 'محمد ابو مسعد', '01281781504', '', NULL, NULL, '', 0, 1, '2025-05-13 17:44:51', NULL),
(3215, 'ندر', '01080363855', '', NULL, NULL, '', 0, 1, '2025-05-13 18:45:57', NULL),
(3216, 'شريف اسم', '01001389055', '', NULL, NULL, '', 0, 1, '2025-05-14 10:09:03', NULL),
(3217, 'حسام', '01225074174', '', NULL, NULL, '', 0, 1, '2025-05-14 13:00:34', NULL),
(3218, 'احمد جمال', '01009825521', '', NULL, NULL, '', 0, 1, '2025-05-14 16:04:53', NULL),
(3219, 'ثروت', '01278576727', '', NULL, '0000-00-00', '', 0, 1, '2025-05-14 17:19:28', NULL),
(3220, 'محمدعبد الفتاح', '01144231842', '', NULL, NULL, '', 0, 1, '2025-05-15 12:03:49', NULL),
(3221, 'كهرباء', '01226618452', '', NULL, NULL, '', 0, 1, '2025-05-15 15:01:51', NULL),
(3222, 'احمد امبابي', '01271789583', '', NULL, NULL, '', 0, 1, '2025-05-15 15:30:24', NULL),
(3223, 'عوض العشري', '01201890587', '', NULL, NULL, '', 10, 1, '2025-05-15 15:40:00', '2025-05-23 14:24:10'),
(3224, 'احمد ابو حسن', '01017157033', '', NULL, NULL, '', 0, 1, '2025-05-15 15:59:08', NULL),
(3225, 'عبد الرحمن صابر', '01124183746', '', NULL, NULL, '', 0, 1, '2025-05-15 16:20:33', NULL),
(3226, 'يوسف شبانه', '01044913078', '', NULL, NULL, '', 0, 1, '2025-05-15 17:23:58', NULL),
(3227, 'شادي حسن', '01210009652', '', NULL, '0000-00-00', '', 0, 1, '2025-05-15 19:17:20', '2025-05-15 19:18:54'),
(3228, 'البشمهندس محمود', '01032648474', '<EMAIL>', NULL, '0000-00-00', '', 0, 1, '2025-05-15 20:43:50', NULL),
(3229, 'علي', '01066270410', '', NULL, NULL, '', 0, 1, '2025-05-15 22:18:33', NULL),
(3230, 'حسين هاني', '01004926164', '', NULL, NULL, '', 0, 1, '2025-05-16 16:04:20', NULL),
(3231, 'احمد سعد', '01020522012', '', NULL, NULL, '', 0, 1, '2025-05-16 17:13:52', NULL),
(3232, 'السيد رمضان', '01011998052', '', NULL, '0000-00-00', '', 0, 1, '2025-05-16 19:01:15', NULL),
(3233, 'حسن محمد', '01097473117', '', NULL, NULL, '', 0, 1, '2025-05-16 19:53:59', NULL),
(3234, 'احمد الغندور', '01092519136', '', NULL, '0000-00-00', '', 0, 1, '2025-05-16 20:48:25', NULL),
(3235, 'محمود', '01064602502', '', NULL, NULL, '', 0, 1, '2025-05-17 16:07:55', NULL),
(3236, 'السيد الطنطاؤي', '01005169887', '', NULL, NULL, '', 0, 1, '2025-05-17 20:11:55', NULL),
(3237, 'تامر فايد', '01024522704', '', 'النسميه', '0000-00-00', '', 0, 1, '2025-05-18 15:49:49', '2025-05-23 16:18:28'),
(3238, 'احمد علي عزت', '01030403570', '', NULL, '0000-00-00', '', 0, 1, '2025-05-18 17:58:38', NULL),
(3239, 'محمد محمود الحلو', '01066051749', '', NULL, '0000-00-00', '', 0, 1, '2025-05-18 18:58:14', NULL),
(3240, 'احمد مصطفي جمعه', '01095849218', '', NULL, '0000-00-00', '', 0, 1, '2025-05-18 21:55:08', NULL),
(3241, 'ابراهيم السيد عبد العال', '01014108553', '', NULL, '0000-00-00', '', 0, 1, '2025-05-18 22:27:11', NULL),
(3242, 'محمدعبد الحكيم', '01207907001', '', NULL, NULL, '', 0, 1, '2025-05-19 10:43:12', NULL),
(3243, 'احمد عصام سلامه', '01113232000', '', NULL, '0000-00-00', '', 0, 1, '2025-05-19 15:45:20', NULL),
(3244, 'محمود القصبي', '01005700958', '', NULL, '0000-00-00', '', 0, 1, '2025-05-19 15:47:07', NULL),
(3245, 'عبد الرحمن عبد العزيم', '01224980994', '', NULL, NULL, '', 0, 1, '2025-05-19 16:46:51', NULL),
(3246, 'زياد علي المتولي', '01024343592', '', NULL, NULL, '', 0, 1, '2025-05-19 19:19:00', NULL),
(3247, 'احمد السعيد المتولي', '01060746837', '', '', '0000-00-00', '', 0, 1, '2025-05-20 14:34:48', NULL),
(3248, 'احمد مراداحمد', '01556051711', '', 'النسيمه', '2025-01-21', '', 0, 1, '2025-05-20 14:42:31', NULL),
(3249, 'زياد رضا احمد', '01014277402', '', 'النسميه', NULL, '', 0, 1, '2025-05-20 15:08:16', NULL),
(3250, 'كمال فيصل', '01025266162', '', 'الخليج', '0000-00-00', '', 0, 1, '2025-05-20 15:49:00', '2025-05-20 16:15:24'),
(3251, 'احمد ماهر عبد الحكم', '01095613419', '', 'ميت علي', '0000-00-00', '', 0, 1, '2025-05-20 17:47:55', NULL),
(3252, 'عزت محمد', '01552569256', '', 'كوم بني مراس', NULL, '', 0, 1, '2025-05-20 22:07:02', NULL),
(3253, 'احمد الشحات عوض', '01099552192', '', 'ميت علي', '0000-00-00', '', 0, 1, '2025-05-21 10:14:56', NULL),
(3254, 'احمد العيسوي', '01007221189', '', 'الخليج', '0000-00-00', '', 0, 1, '2025-05-21 17:50:32', NULL),
(3255, 'السعيد احمد سعيد', '01222130495', '', 'طناح', '0000-00-00', '', 0, 1, '2025-05-22 14:03:40', NULL),
(3256, 'محمد خالف', '01272855516', '', 'كفر طناح', '0000-00-00', '', 0, 1, '2025-05-22 14:10:34', NULL),
(3257, 'حسن محمود  حامد', '01283034394', '', 'طناح', '0000-00-00', '', 0, 1, '2025-05-22 14:13:54', NULL),
(3258, 'محمد  علي يونس', '01225160777', '', 'طناح', '0000-00-00', '', 0, 1, '2025-05-22 16:49:05', NULL),
(3259, 'زكي مسعد الصياد', '01206546967', '', 'كفر طناح', '0000-00-00', '', 0, 1, '2025-05-23 13:13:17', NULL),
(3260, 'ابراهيم عبد الفتاح', '01280884435', '', 'ميت جراح', '0000-00-00', '', 0, 1, '2025-05-23 14:02:24', NULL),
(3261, 'محمد ابو حسن', '01145220545', '', '', NULL, '', 0, 1, '2025-05-23 15:36:47', NULL),
(3262, 'كريم احمد مكاوي', '01552425777', '', 'المنصوره', '0000-00-00', '', 0, 1, '2025-05-23 15:56:09', NULL),
(3263, 'محمود شتيوي', '01060010242', '', 'كوم بني مراس', '0000-00-00', '', 0, 1, '2025-05-23 15:59:15', NULL),
(3264, 'احمد محمد عوض الله', '01147810700', '', 'ميت غمر', '0000-00-00', '', 0, 1, '2025-05-23 17:40:01', NULL),
(3265, 'محمد عمر و خيرلله', '01555863166', '', 'الخليج', '0000-00-00', '', 0, 1, '2025-05-23 17:42:04', NULL),
(3266, 'محمد احمد حماده', '01014443541', '', 'عزبت نسيم', '0000-00-00', '', 0, 1, '2025-05-23 17:48:31', NULL),
(3267, 'محمد رضا حلمي', '01275130223', '', 'الخليج', '0000-00-00', '', 0, 1, '2025-05-23 18:55:25', NULL),
(3268, 'محمد عبد الوهاب', '01274634410', '', 'طناح', '0000-00-00', '', 0, 1, '2025-05-24 16:28:19', NULL),
(3269, 'محمد نجيب الرفعي', '01092720421', '', 'كفر طناح', '0000-00-00', '', 0, 1, '2025-05-24 17:10:24', NULL),
(3270, 'احمد عصام الدين', '01017359735', '', 'عزبت الشيخ يوسف', '0000-00-00', '', 0, 1, '2025-05-24 17:36:49', NULL),
(3271, 'عمار مطاوع', '01031441617', '', 'النسميه', '0000-00-00', '', 0, 1, '2025-05-24 19:56:15', NULL),
(3272, 'احمد مطاوع', '01031451911', '', 'النسميه', '0000-00-00', '', 0, 1, '2025-05-24 21:53:16', NULL),
(3273, 'حازم عمرو', '01099729108', '', '', '0000-00-00', '', 0, 1, '2025-05-24 22:36:03', NULL),
(3274, 'محمد حسين صالح', '01007732128', '', 'النسميه', '0000-00-00', '', 0, 1, '2025-05-25 14:17:09', NULL),
(3275, 'احمد السيد رزق', '01007443758', '', 'كفر طناح', '0000-00-00', '', 0, 1, '2025-05-25 16:35:21', NULL),
(3276, 'عبد الرحمن الارش', '01113625667', '', 'ميت محمود', '0000-00-00', '', 0, 1, '2025-05-25 16:55:06', NULL),
(3277, 'محمد تامر', '01094896879', '', '', '0000-00-00', '', 0, 1, '2025-05-25 18:14:58', NULL),
(3278, 'محمد  عبد الفتاح فؤد', '01228974305', '', 'طناح', '0000-00-00', '', 0, 1, '2025-05-25 22:04:13', NULL),
(3279, 'عمر السعيد كربال', '01221743156', '', 'طناح', '0000-00-00', '', 0, 1, '2025-05-26 19:17:35', NULL),
(3280, 'عبد الله مروان', '01019289268', '', 'طناح', '0000-00-00', '', 0, 1, '2025-05-26 19:21:12', NULL),
(3281, 'احمد عبد العال السيد', '01205511687', '', 'الكفر', '0000-00-00', '', 0, 1, '2025-05-27 10:59:03', NULL),
(3282, 'احمد عادل سمير', '014014620198', '', 'طناح', '0000-00-00', '', 0, 1, '2025-05-27 14:35:04', NULL),
(3283, 'مجدي طارق عبد المجيد', '01288593408', '', 'طناح', '0000-00-00', '', 0, 1, '2025-05-27 14:43:27', NULL),
(3284, 'احمد محمد حامد', '01206225917', '', 'طناح', '0000-00-00', '', 0, 1, '2025-05-27 15:46:45', NULL),
(3285, 'احمد يسري ابو الحسن', '01044252814', '', 'كوم بني مراس', '0000-00-00', '', 0, 1, '2025-05-27 18:37:20', NULL),
(3286, 'محمد ياسر يسري', '01029646495', '', 'عزبت الشنوي', '0000-00-00', '', 0, 1, '2025-05-27 19:40:00', NULL),
(3287, 'حسن مجدي', '01040964139', '', 'الشناوي', '0000-00-00', '', 0, 1, '2025-05-27 19:41:35', NULL),
(3288, 'محمد سيد احمد', '01096657010', '', 'كفر طناح', NULL, '', 0, 1, '2025-05-27 21:13:15', NULL),
(3289, 'احمد عارف', '01201634309', '', 'طناح', '0000-00-00', '', 0, 1, '2025-05-27 22:24:44', NULL),
(3290, 'زيدان المهدي بركه', '01289999576', '', 'كفر طناح', '0000-00-00', '', 0, 1, '2025-05-27 23:00:27', NULL),
(3291, 'محمد السيد محمد', '01205891012', '', 'الكفر طناح', '0000-00-00', '', 0, 1, '2025-05-28 14:46:43', NULL),
(3292, 'عبد الراجمن رضا العزب', '01020696395', '', 'الكفر طناح', '0000-00-00', '', 0, 1, '2025-05-28 16:59:32', '2025-05-28 17:02:12'),
(3293, 'محمد جمل ابو الوفا', '01200126378', '', 'طناح', '0000-00-00', '', 0, 1, '2025-05-28 18:21:58', NULL),
(3294, 'فوزي شعبان محمد', '01008438759', '', 'كفر طناح', '0000-00-00', '', 0, 1, '2025-05-28 18:29:28', NULL),
(3295, 'حمدي عزت رافعت', '01016616166333', '', 'كوم بني مراس', '0000-00-00', '', 0, 1, '2025-05-28 20:24:24', NULL),
(3296, 'ناجي محمود', '01069393571', '', 'طناح', '0000-00-00', '', 0, 1, '2025-05-28 22:08:31', NULL),
(3297, 'زياد طارق حجازي', '01210084474', '', 'كوم بني مراس', '0000-00-00', '', 0, 1, '2025-05-28 22:49:09', NULL),
(3298, 'احمد عصام احمد', '01558664424', '', 'طناح', '0000-00-00', '', 0, 1, '2025-05-29 11:40:54', NULL),
(3299, 'عادل عصام', '01096551487', '', 'كوم بني مراس', '0000-00-00', '', 0, 1, '2025-05-29 16:36:26', NULL),
(3300, 'محمود حسن القضب', '01200258054', '', 'طناح', '0000-00-00', '', 0, 1, '2025-05-29 22:28:45', NULL),
(3301, 'يوسف محمد علاء', '01141074349', '', 'كفر طناح', '0000-00-00', '', 0, 1, '2025-05-30 11:37:05', NULL),
(3302, 'حسن سمير حسن', '01210906552', '', 'كفر طناح', '0000-00-00', '', 0, 1, '2025-05-30 12:10:13', NULL),
(3303, 'كمال زكريا', '01288007226', '', 'كفر طناح', '0000-00-00', '', 0, 1, '2025-05-30 12:16:36', NULL),
(3304, 'احمد شحاته', '01018406637', '', 'ميت علي', '0000-00-00', '', 0, 1, '2025-05-30 14:18:50', NULL),
(3305, 'محمد محمود', '01281207185', '', 'الخليج', '0000-00-00', '', 0, 1, '2025-05-30 16:16:03', NULL),
(3306, 'محمد وسيم محمد', '01288701104', '', 'الخليج', '0000-00-00', '', 0, 1, '2025-05-30 16:19:20', NULL),
(3307, 'جمال احمد عوده', '01273464069', '', 'ميت محمود', '0000-00-00', '', 0, 1, '2025-05-30 19:00:45', NULL),
(3308, 'محمود محمد فواد', '01016896983', '', 'كوم بني مراس', '0000-00-00', '', 0, 1, '2025-05-30 19:03:22', NULL),
(3309, 'احمد عبد الحميد الموفي', '01097630572', '', 'كوم بني مراس', '0000-00-00', '', 0, 1, '2025-05-30 19:49:46', NULL),
(3310, 'عبد الرحمن محمد محمد', '01277529751', '', 'طناح', '0000-00-00', '', 0, 1, '2025-05-31 17:17:04', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `customer_visits`
--

CREATE TABLE `customer_visits` (
  `id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `visit_date` datetime NOT NULL DEFAULT current_timestamp(),
  `notes` text DEFAULT NULL,
  `branch_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `employees`
--

CREATE TABLE `employees` (
  `id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `name` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `position` varchar(50) DEFAULT NULL,
  `salary_type` enum('fixed','percentage','both') NOT NULL DEFAULT 'fixed',
  `fixed_salary` decimal(10,2) DEFAULT 0.00,
  `commission_percentage` decimal(5,2) DEFAULT 0.00,
  `branch_id` int(11) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `employees`
--

INSERT INTO `employees` (`id`, `user_id`, `name`, `phone`, `email`, `position`, `salary_type`, `fixed_salary`, `commission_percentage`, `branch_id`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 8, 'احمد البدراوي', '0102 2429488‬', '', 'مدير', 'percentage', '0.00', '50.00', 1, 1, '2025-03-15 23:05:19', '2025-05-10 14:21:15'),
(2, NULL, 'محمد إسلام', '‭+20 110 3119010‬', '', 'حلاق', 'percentage', '0.00', '40.00', 1, 1, '2025-03-15 23:05:19', '2025-05-10 14:22:50'),
(3, NULL, 'ذياد عمر', '‭+20 112 9132229‬', '', 'حلاق', 'percentage', '0.00', '40.00', 1, 1, '2025-03-15 23:05:19', '2025-05-10 14:23:56'),
(4, NULL, 'عبد الله عيد', '‭+20 110 3119220‬', '', 'حلاق', 'percentage', '0.00', '40.00', 1, 1, '2025-03-15 23:05:19', '2025-05-10 14:25:06'),
(5, NULL, 'محمد تامر', '‭+20 109 4896879‬', '', 'كاشير', 'fixed', '3000.00', '0.00', 1, 1, '2025-03-15 23:05:19', '2025-05-10 14:26:28'),
(6, NULL, 'محمد البدراوي', '01009036186', '<EMAIL>', 'مدير', 'percentage', '0.00', '50.00', 1, 1, '2025-03-15 23:05:19', '2025-05-07 21:24:31'),
(14, NULL, 'احمد شيكا', '01228620645', '', 'حلاق', 'percentage', '0.00', '30.00', 1, 1, '2025-05-02 20:09:35', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `employee_attendance`
--

CREATE TABLE `employee_attendance` (
  `id` int(11) NOT NULL,
  `employee_id` int(11) NOT NULL,
  `date` date NOT NULL,
  `check_in` time DEFAULT NULL,
  `check_out` time DEFAULT NULL,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `employee_attendance`
--

INSERT INTO `employee_attendance` (`id`, `employee_id`, `date`, `check_in`, `check_out`, `notes`) VALUES
(13, 1, '2025-04-08', '00:00:00', '17:11:00', ''),
(14, 2, '2025-04-08', '00:00:00', '23:00:00', '');

-- --------------------------------------------------------

--
-- Table structure for table `employee_salaries`
--

CREATE TABLE `employee_salaries` (
  `id` int(11) NOT NULL,
  `employee_id` int(11) NOT NULL,
  `month` int(11) NOT NULL,
  `year` int(11) NOT NULL,
  `fixed_amount` decimal(10,2) DEFAULT 0.00,
  `commission_amount` decimal(10,2) DEFAULT 0.00,
  `bonuses` decimal(10,2) DEFAULT 0.00,
  `deductions` decimal(10,2) DEFAULT 0.00,
  `total_amount` decimal(10,2) NOT NULL,
  `payment_date` date DEFAULT NULL,
  `payment_status` enum('paid','unpaid') NOT NULL DEFAULT 'unpaid',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `employee_salaries`
--

INSERT INTO `employee_salaries` (`id`, `employee_id`, `month`, `year`, `fixed_amount`, `commission_amount`, `bonuses`, `deductions`, `total_amount`, `payment_date`, `payment_status`, `notes`, `created_at`, `updated_at`) VALUES
(149, 5, 4, 2025, '3000.00', '0.00', '0.00', '0.00', '3000.00', NULL, 'unpaid', '{&quot;calculated_days&quot;:30}', '2025-04-30 18:22:03', '2025-05-08 14:15:25'),
(151, 1, 4, 2025, '0.00', '5058.23', '0.00', '0.00', '5058.23', '2025-04-30', 'paid', NULL, '2025-04-30 20:25:09', '2025-04-30 20:25:25'),
(153, 2, 4, 2025, '0.00', '2108.39', '0.00', '0.00', '2108.39', '2025-04-30', 'paid', NULL, '2025-04-30 20:26:51', '2025-04-30 20:26:57'),
(154, 3, 4, 2025, '0.00', '3443.78', '0.00', '0.00', '3443.78', '2025-04-30', 'paid', NULL, '2025-04-30 20:27:24', '2025-04-30 20:27:34'),
(157, 4, 4, 2025, '0.00', '1521.10', '0.00', '0.00', '1521.10', '2025-04-30', 'paid', NULL, '2025-04-30 20:27:51', '2025-04-30 20:27:54'),
(158, 6, 4, 2025, '0.00', '445.00', '0.00', '0.00', '445.00', '2025-04-30', 'paid', NULL, '2025-04-30 20:28:03', '2025-04-30 20:28:48'),
(160, 14, 4, 2025, '0.00', '0.00', '0.00', '0.00', '0.00', NULL, 'unpaid', NULL, '2025-05-08 14:15:23', NULL),
(185, 3, 5, 2025, '0.00', '2782.00', '0.00', '0.00', '2782.00', NULL, 'unpaid', '', '2025-05-26 20:02:27', '2025-05-31 21:49:41'),
(186, 4, 5, 2025, '0.00', '2686.00', '0.00', '0.00', '2686.00', NULL, 'unpaid', '', '2025-05-26 20:02:41', '2025-05-31 21:49:42'),
(187, 6, 5, 2025, '0.00', '100.00', '0.00', '0.00', '100.00', NULL, 'unpaid', '', '2025-05-26 20:02:55', '2025-05-31 21:49:42'),
(188, 14, 5, 2025, '0.00', '1609.50', '0.00', '0.00', '1609.50', NULL, 'unpaid', '', '2025-05-26 20:03:08', '2025-05-31 21:49:41'),
(191, 2, 5, 2025, '0.00', '3358.00', '0.00', '0.00', '3358.00', NULL, 'unpaid', '', '2025-05-28 20:03:16', '2025-05-31 21:49:42'),
(195, 1, 5, 2025, '0.00', '5647.50', '0.00', '0.00', '5647.50', NULL, 'unpaid', '', '2025-05-31 15:44:46', '2025-05-31 21:49:41'),
(196, 5, 5, 2025, '3000.00', '0.00', '0.00', '0.00', '3000.00', NULL, 'unpaid', NULL, '2025-05-31 21:49:42', '2025-05-31 21:49:43'),
(197, 1, 6, 2025, '0.00', '0.00', '0.00', '0.00', '0.00', NULL, 'unpaid', NULL, '2025-05-31 22:01:06', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `end_days`
--

CREATE TABLE `end_days` (
  `id` int(11) NOT NULL,
  `date` date NOT NULL,
  `cash_amount` decimal(10,2) DEFAULT 0.00,
  `card_amount` decimal(10,2) DEFAULT 0.00,
  `other_amount` decimal(10,2) DEFAULT 0.00,
  `total_sales` decimal(10,2) DEFAULT 0.00,
  `total_expenses` decimal(10,2) DEFAULT 0.00,
  `total_discounts` decimal(10,2) DEFAULT 0.00,
  `notes` text DEFAULT NULL,
  `closed_by` int(11) DEFAULT NULL,
  `branch_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `closed_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `end_days`
--

INSERT INTO `end_days` (`id`, `date`, `cash_amount`, `card_amount`, `other_amount`, `total_sales`, `total_expenses`, `total_discounts`, `notes`, `closed_by`, `branch_id`, `created_at`, `closed_at`) VALUES
(20250421, '2025-04-08', '240.00', '0.00', '0.00', '240.00', '0.00', '20.00', '', 11, 1, '2025-04-08 18:43:59', '2025-04-09 09:54:04'),
(20250423, '2025-04-09', '1360.00', '0.00', '0.00', '1360.00', '50.00', '0.00', '', 8, 1, '2025-04-08 23:54:09', '2025-04-10 11:21:16'),
(20250424, '2025-04-10', '1535.00', '0.00', '0.00', '1535.00', '50.00', '0.00', '', 8, 1, '2025-04-10 14:19:41', '2025-04-11 10:51:11'),
(20250425, '2025-04-11', '976.76', '0.00', '0.00', '976.76', '565.00', '1001.99', '', 8, 1, '2025-04-11 13:29:36', '2025-04-12 07:30:02'),
(20250426, '2025-04-12', '3196.00', '0.00', '0.00', '3196.00', '115.00', '450.00', '', 8, 1, '2025-04-12 11:31:21', '2025-04-13 10:20:46'),
(20250427, '2025-04-13', '2045.00', '0.00', '0.00', '2045.00', '1970.00', '70.00', '', 10, 1, '2025-04-13 11:19:47', '2025-04-14 08:42:16'),
(20250428, '2025-04-14', '1545.00', '0.00', '0.00', '1545.00', '6635.00', '15.00', '', 10, 1, '2025-04-14 09:15:26', '2025-04-15 09:06:32'),
(20250429, '2025-04-15', '1164.50', '0.00', '0.00', '1164.50', '310.00', '45.00', '', 8, 1, '2025-04-15 12:26:58', '2025-04-16 08:25:32'),
(20250431, '2025-04-16', '1515.00', '0.00', '0.00', '1515.00', '170.00', '0.00', '', 10, 1, '2025-04-16 10:18:35', '2025-04-17 09:04:47'),
(20250432, '2025-04-17', '1350.00', '0.00', '0.00', '1350.00', '0.00', '0.00', '', 10, 1, '2025-04-17 13:42:01', '2025-04-18 07:34:47'),
(20250433, '2025-04-18', '2005.00', '0.00', '0.00', '2005.00', '235.00', '25.00', '', 10, 1, '2025-04-18 10:19:19', '2025-04-19 09:06:27'),
(20250435, '2025-04-20', '1200.00', '0.00', '0.00', '1200.00', '4520.00', '0.00', '', 10, 1, '2025-04-20 01:44:20', '2025-04-21 08:45:28'),
(20250436, '2025-04-21', '1275.00', '0.00', '0.00', '1275.00', '135.00', '0.00', '', 10, 1, '2025-04-21 10:08:50', '2025-04-22 22:46:54'),
(20250437, '2025-04-22', '1105.00', '0.00', '0.00', '1105.00', '445.00', '0.00', '', 10, 1, '2025-04-22 13:47:00', '2025-04-23 08:14:51'),
(20250438, '2025-04-23', '425.00', '0.00', '0.00', '425.00', '100.00', '0.00', '', 10, 1, '2025-04-23 14:15:28', '2025-04-24 20:53:33'),
(20250439, '2025-04-24', '1680.00', '0.00', '0.00', '1680.00', '55.00', '0.00', '', 10, 1, '2025-04-24 11:53:44', '2025-04-25 09:49:22'),
(20250440, '2025-04-25', '1453.50', '0.00', '0.00', '1453.50', '200.00', '20.00', '', 10, 1, '2025-04-25 09:55:54', '2025-04-26 07:56:21'),
(20250441, '2025-04-26', '850.00', '0.00', '0.00', '850.00', '185.00', '0.00', '', 10, 1, '2025-04-26 08:47:58', '2025-04-27 07:18:02'),
(20250442, '2025-04-27', '835.00', '0.00', '0.00', '835.00', '50.00', '0.00', '', 10, 1, '2025-04-27 07:46:30', '2025-04-28 08:17:37'),
(20250443, '2025-04-28', '820.00', '0.00', '0.00', '820.00', '300.00', '0.00', '', 10, 1, '2025-04-28 09:14:09', '2025-04-29 07:26:34'),
(20250444, '2025-04-29', '665.00', '0.00', '0.00', '665.00', '35.00', '0.00', '', 10, 1, '2025-04-29 07:52:56', '2025-04-30 07:56:53'),
(20250445, '2025-04-30', '945.00', '0.00', '0.00', '945.00', '60.00', '0.00', '', 10, 1, '2025-04-30 07:58:53', '2025-05-01 09:11:04'),
(20250446, '2025-05-01', '1820.00', '0.00', '0.00', '1820.00', '60.00', '0.00', '', 10, 1, '2025-05-01 07:04:04', '2025-05-02 09:31:36'),
(20250447, '2025-05-02', '1605.00', '0.00', '0.00', '1605.00', '410.00', '0.00', '', 10, 1, '2025-05-02 09:27:08', '2025-05-03 09:03:14'),
(20250448, '2025-05-03', '1190.00', '0.00', '0.00', '1190.00', '100.00', '0.00', '', 10, 1, '2025-05-03 09:11:52', '2025-05-04 08:01:53'),
(20250449, '2025-05-04', '1095.00', '0.00', '0.00', '1095.00', '1900.00', '0.00', '', 10, 1, '2025-05-04 07:58:16', '2025-05-05 09:21:11'),
(20250450, '2025-05-05', '1190.00', '0.00', '0.00', '1190.00', '1035.00', '0.00', '', 10, 1, '2025-05-05 07:59:45', '2025-05-06 08:07:30'),
(20250451, '2025-05-06', '1625.00', '0.00', '0.00', '1625.00', '490.00', '0.00', '', 10, 1, '2025-05-06 07:40:41', '2025-05-07 10:10:57'),
(20250452, '2025-05-07', '1085.00', '0.00', '0.00', '1085.00', '50.00', '0.00', '', 10, 1, '2025-05-07 08:38:07', '2025-05-08 13:09:05'),
(20250453, '2025-05-08', '2565.00', '0.00', '0.00', '2565.00', '100.00', '0.00', '', 10, 1, '2025-05-08 18:16:36', '2025-05-09 08:13:26'),
(20250454, '2025-05-09', '2025.00', '0.00', '0.00', '2025.00', '4450.00', '0.00', '', 10, 1, '2025-05-09 18:18:46', '2025-05-10 08:29:50'),
(20250455, '2025-05-10', '3065.00', '0.00', '0.00', '3065.00', '380.00', '0.00', '', 10, 1, '2025-05-10 17:11:27', '2025-05-11 09:15:19'),
(20250456, '2025-05-11', '1080.00', '0.00', '0.00', '1080.00', '360.00', '0.00', '', 10, 1, '2025-05-11 20:35:27', '2025-05-12 09:17:42'),
(20250457, '2025-05-12', '1005.00', '0.00', '0.00', '1005.00', '180.00', '0.00', '', 10, 1, '2025-05-12 18:29:05', '2025-05-13 07:54:02'),
(20250458, '2025-05-13', '1195.00', '0.00', '0.00', '1195.00', '270.00', '0.00', '', 10, 1, '2025-05-13 17:14:09', '2025-05-14 07:43:26'),
(20250459, '2025-05-14', '685.00', '0.00', '0.00', '685.00', '140.00', '0.00', '', 10, 1, '2025-05-14 16:59:38', '2025-05-15 07:51:20'),
(20250460, '2025-05-15', '1975.00', '0.00', '0.00', '1975.00', '125.00', '0.00', '', 10, 1, '2025-05-15 17:02:18', '2025-05-16 08:29:43'),
(20250461, '2025-05-16', '1370.00', '0.00', '0.00', '1370.00', '380.00', '0.00', '', 10, 1, '2025-05-16 17:02:42', '2025-05-17 08:01:11'),
(20250462, '2025-05-17', '755.00', '0.00', '0.00', '755.00', '140.00', '55.00', '', 10, 1, '2025-05-17 16:46:28', '2025-05-18 07:37:50'),
(20250463, '2025-05-18', '1275.00', '0.00', '0.00', '1275.00', '1070.00', '0.00', '', 10, 1, '2025-05-18 17:26:12', '2025-05-19 08:28:54'),
(20250464, '2025-05-19', '665.00', '0.00', '0.00', '665.00', '200.00', '0.00', '', 10, 1, '2025-05-19 19:16:36', '2025-05-20 08:19:03'),
(20250465, '2025-05-20', '1255.00', '0.00', '0.00', '1255.00', '165.00', '0.00', '', 10, 1, '2025-05-20 17:02:11', '2025-05-21 08:10:03'),
(20250466, '2025-05-21', '1340.00', '0.00', '0.00', '1340.00', '620.00', '0.00', '', 10, 1, '2025-05-21 16:55:34', '2025-05-22 07:56:55'),
(20250467, '2025-05-22', '770.00', '0.00', '0.00', '770.00', '380.00', '0.00', '', 10, 1, '2025-05-22 17:04:35', '2025-05-23 22:00:21'),
(20250468, '2025-05-23', '2180.00', '0.00', '0.00', '2180.00', '445.00', '0.00', '', 10, 1, '2025-05-23 22:00:30', '2025-05-24 07:59:26'),
(20250469, '2025-05-24', '2175.00', '0.00', '0.00', '2175.00', '250.00', '0.00', '', 10, 1, '2025-05-24 17:04:00', '2025-05-25 08:37:29'),
(20250470, '2025-05-25', '1075.00', '0.00', '0.00', '1075.00', '0.00', '0.00', '', 10, 1, '2025-05-25 17:08:53', '2025-05-26 08:12:54'),
(20250471, '2025-05-26', '580.00', '0.00', '0.00', '580.00', '330.00', '0.00', '', 10, 1, '2025-05-26 18:26:01', '2025-05-27 16:59:16'),
(20250472, '2025-05-27', '1425.00', '0.00', '0.00', '1425.00', '200.00', '0.00', '', 1, 1, '2025-05-27 17:02:06', '2025-05-28 13:29:36'),
(20250473, '2025-05-28', '2280.00', '0.00', '0.00', '2280.00', '150.00', '150.00', '', 10, 1, '2025-05-28 17:00:43', '2025-05-29 08:50:23'),
(20250474, '2025-05-29', '1185.00', '0.00', '0.00', '1185.00', '420.00', '0.00', '', 10, 1, '2025-05-29 17:02:40', '2025-05-30 10:18:03'),
(20250475, '2025-05-30', '1465.00', '0.00', '0.00', '1465.00', '150.00', '0.00', '', 10, 1, '2025-05-30 17:02:38', '2025-05-31 07:49:57'),
(20250476, '2025-05-31', '715.00', '0.00', '0.00', '715.00', '145.00', '0.00', NULL, 10, 1, '2025-05-31 17:20:49', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `expenses`
--

CREATE TABLE `expenses` (
  `id` int(11) NOT NULL,
  `category_id` int(11) DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `date` date NOT NULL,
  `payment_method` enum('cash','card','other') NOT NULL DEFAULT 'cash',
  `user_id` int(11) DEFAULT NULL,
  `branch_id` int(11) DEFAULT NULL,
  `end_day_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `expenses`
--

INSERT INTO `expenses` (`id`, `category_id`, `amount`, `description`, `date`, `payment_method`, `user_id`, `branch_id`, `end_day_id`, `created_at`, `updated_at`) VALUES
(3078, 5, '50.00', 'السعيد', '2025-04-09', 'cash', 10, 1, 20250423, '2025-04-09 17:25:31', NULL),
(3079, 4, '50.00', 'طفيط', '2025-04-11', 'cash', 8, 1, 20250424, '2025-04-11 00:45:21', NULL),
(3080, 7, '400.00', 'كهرباء', '2025-04-11', 'cash', 8, 1, 20250425, '2025-04-11 14:42:38', NULL),
(3081, 7, '100.00', 'منديل', '2025-04-11', 'cash', 8, 1, 20250425, '2025-04-11 14:43:18', NULL),
(3082, 3, '65.00', 'رضا', '2025-04-11', 'cash', 8, 1, 20250425, '2025-04-11 15:42:59', NULL),
(3083, 7, '115.00', 'منديل= معطر', '2025-04-12', 'cash', 8, 1, 20250426, '2025-04-12 16:10:35', NULL),
(3084, 5, '100.00', 'كهرباء', '2025-04-13', 'cash', 10, 1, 20250427, '2025-04-13 15:05:23', NULL),
(3085, 5, '100.00', 'محمد البدراوي يوم الكشف', '2025-04-13', 'cash', 10, 1, 20250427, '2025-04-13 17:45:36', NULL),
(3086, 5, '900.00', 'محمد البدراوي يوم الكشف', '2025-04-13', 'cash', 10, 1, 20250427, '2025-04-13 18:10:48', NULL),
(3087, 5, '70.00', 'ورق كومبيوتر', '2025-04-13', 'cash', 10, 1, 20250427, '2025-04-13 19:50:37', NULL),
(3088, 1, '800.00', 'محمد رضا', '2025-04-13', 'cash', 10, 1, 20250427, '2025-04-13 19:51:23', NULL),
(3089, 4, '2000.00', 'مصناعيه البركيه', '2025-04-14', 'cash', 10, 1, 20250428, '2025-04-14 13:28:09', NULL),
(3090, 3, '4600.00', 'مستلزمات لي المحل', '2025-04-14', 'cash', 10, 1, 20250428, '2025-04-14 13:55:53', NULL),
(3091, 5, '35.00', 'سكر', '2025-04-14', 'cash', 10, 1, 20250428, '2025-04-14 19:59:24', NULL),
(3092, 4, '20.00', 'سلك +لزك', '2025-04-15', 'cash', 10, 1, 20250429, '2025-04-15 15:55:59', NULL),
(3093, 3, '290.00', 'سلت 3', '2025-04-15', 'cash', 10, 1, 20250429, '2025-04-15 16:33:41', NULL),
(3094, 5, '100.00', 'ابراهيم بتاع اليفطه حلاق', '2025-04-16', 'cash', 10, 1, 20250431, '2025-04-16 19:25:16', NULL),
(3095, 5, '70.00', 'ليسون +شاي+كركري', '2025-04-16', 'cash', 10, 1, 20250431, '2025-04-16 21:26:57', NULL),
(3096, 5, '200.00', 'كهرباء', '2025-04-18', 'cash', 10, 1, 20250433, '2025-04-18 15:06:14', NULL),
(3097, 5, '35.00', 'سكر', '2025-04-18', 'cash', 10, 1, 20250433, '2025-04-18 15:07:12', NULL),
(3098, 5, '20.00', 'بن', '2025-04-19', 'cash', 10, 1, 20250435, '2025-04-19 14:39:29', '2025-04-20 12:49:01'),
(3099, 5, '65.00', 'بن', '2025-04-19', 'cash', 10, 1, 20250435, '2025-04-19 18:30:59', '2025-04-20 12:48:58'),
(3100, 5, '500.00', 'عبد الرحمن جمال', '2025-04-20', 'cash', 10, 1, 20250435, '2025-04-19 22:14:42', '2025-04-20 01:44:20'),
(3101, 5, '20.00', 'جرنين', '2025-04-20', 'cash', 10, 1, 20250435, '2025-04-20 12:33:41', NULL),
(3102, 5, '1150.00', 'الاكل', '2025-04-20', 'cash', 10, 1, 20250435, '2025-04-20 17:58:27', NULL),
(3103, 7, '350.00', 'بتعت القهوه', '2025-04-20', 'cash', 10, 1, 20250435, '2025-04-20 18:03:37', NULL),
(3104, 1, '190.00', 'اشتراك النت', '2025-04-20', 'cash', 10, 1, 20250435, '2025-04-20 18:04:18', NULL),
(3105, 3, '1190.00', 'فوط خاصه', '2025-04-20', 'cash', 10, 1, 20250435, '2025-04-20 18:04:48', NULL),
(3106, 5, '35.00', 'سكر', '2025-04-20', 'cash', 10, 1, 20250435, '2025-04-20 18:58:40', NULL),
(3107, 3, '1000.00', 'فوط', '2025-04-20', 'cash', 10, 1, 20250435, '2025-04-20 19:08:09', NULL),
(3108, 5, '100.00', 'كهرباء', '2025-04-21', 'cash', 10, 1, 20250436, '2025-04-21 16:58:02', NULL),
(3109, 5, '35.00', 'بن', '2025-04-21', 'cash', 10, 1, 20250436, '2025-04-21 20:52:14', NULL),
(3110, 3, '125.00', 'منديل', '2025-04-22', 'cash', 10, 1, 20250437, '2025-04-22 14:31:31', NULL),
(3111, 5, '200.00', 'كهرباء', '2025-04-22', 'cash', 10, 1, 20250437, '2025-04-22 15:22:38', NULL),
(3112, 4, '120.00', 'بتاع الكلوردير', '2025-04-22', 'cash', 10, 1, 20250437, '2025-04-22 15:26:25', NULL),
(3113, 5, '100.00', 'بن+شاي', '2025-04-24', 'cash', 10, 1, 20250438, '2025-04-24 11:48:40', NULL),
(3114, 5, '35.00', 'سكر', '2025-04-24', 'cash', 10, 1, 20250439, '2025-04-24 17:07:22', NULL),
(3115, 5, '20.00', 'معطر', '2025-04-24', 'cash', 10, 1, 20250439, '2025-04-24 18:26:40', NULL),
(3116, 5, '200.00', 'كهرباء', '2025-04-25', 'cash', 10, 1, 20250440, '2025-04-25 14:44:58', NULL),
(3117, 5, '135.00', 'بن+منديل', '2025-04-26', 'cash', 10, 1, 20250441, '2025-04-26 16:55:28', NULL),
(3118, 5, '50.00', 'جرنين', '2025-04-26', 'cash', 10, 1, 20250441, '2025-04-26 20:06:23', NULL),
(3119, 5, '50.00', 'بن', '2025-04-27', 'cash', 10, 1, 20250442, '2025-04-27 17:49:19', NULL),
(3120, 5, '300.00', 'كهرباء +منديل', '2025-04-28', 'cash', 10, 1, 20250443, '2025-04-28 16:36:11', NULL),
(3121, 5, '35.00', 'بن', '2025-04-29', 'cash', 10, 1, 20250444, '2025-04-29 15:35:16', NULL),
(3122, 5, '60.00', 'بن', '2025-04-30', 'cash', 10, 1, 20250445, '2025-04-30 19:21:43', NULL),
(3123, 5, '60.00', 'بن', '2025-05-01', 'cash', 10, 1, 20250446, '2025-05-01 16:46:23', NULL),
(3124, 5, '250.00', 'منديل+كارت كهرباء', '2025-05-02', 'cash', 10, 1, 20250447, '2025-05-02 14:33:22', NULL),
(3125, 1, '160.00', 'روات من شهر 4', '2025-05-02', 'cash', 8, 1, 20250447, '2025-05-02 18:39:50', NULL),
(3126, 5, '100.00', 'بن +سكر', '2025-05-03', 'cash', 10, 1, 20250448, '2025-05-03 14:32:44', NULL),
(3127, 1, '600.00', 'محمد رضا', '2025-05-04', 'cash', 10, 1, 20250449, '2025-05-04 18:58:43', NULL),
(3128, 5, '1300.00', 'شغل السستم', '2025-05-05', 'cash', 10, 1, 20250449, '2025-05-04 23:13:31', NULL),
(3129, 4, '380.00', 'ورق كشير+دويه +مشترك+السير الابيض+ارشه+حاجات سباكه+سكيت كهرباء', '2025-05-05', 'cash', 10, 1, 20250450, '2025-05-05 18:24:08', NULL),
(3130, 4, '300.00', 'بشمهندس محمود', '2025-05-05', 'cash', 10, 1, 20250450, '2025-05-05 18:24:37', NULL),
(3131, 4, '255.00', 'حاجات من محمد صلاح', '2025-05-05', 'cash', 10, 1, 20250450, '2025-05-05 19:18:51', NULL),
(3132, 5, '100.00', 'دبسه', '2025-05-06', 'cash', 10, 1, 20250450, '2025-05-05 22:07:13', NULL),
(3133, 5, '350.00', 'كهرباء +منديل', '2025-05-06', 'cash', 10, 1, 20250451, '2025-05-06 16:14:40', NULL),
(3134, 5, '100.00', 'بن', '2025-05-06', 'cash', 10, 1, 20250451, '2025-05-06 16:25:59', NULL),
(3135, 5, '40.00', 'سكر', '2025-05-06', 'cash', 10, 1, 20250451, '2025-05-06 17:29:04', NULL),
(3136, 5, '50.00', 'شاي', '2025-05-08', 'cash', 10, 1, 20250452, '2025-05-07 22:43:51', NULL),
(3137, 5, '100.00', 'بن+كلونيا', '2025-05-08', 'cash', 10, 1, 20250453, '2025-05-08 16:24:07', NULL),
(3138, 5, '200.00', 'كهرباء', '2025-05-09', 'cash', 10, 1, 20250454, '2025-05-09 12:13:38', NULL),
(3139, 4, '850.00', 'صيانه كهرباء', '2025-05-09', 'cash', 10, 1, 20250454, '2025-05-09 17:26:33', NULL),
(3141, 2, '3400.00', 'ايجار', '2025-05-09', 'cash', 8, 1, 20250454, '2025-05-09 20:04:18', NULL),
(3142, 3, '200.00', 'منديل', '2025-05-10', 'cash', 10, 1, 20250455, '2025-05-10 12:49:56', NULL),
(3143, 5, '50.00', 'سكر', '2025-05-10', 'cash', 10, 1, 20250455, '2025-05-10 15:35:44', NULL),
(3144, 3, '130.00', 'شمع', '2025-05-10', 'cash', 10, 1, 20250455, '2025-05-10 21:00:08', NULL),
(3145, 5, '160.00', 'بن+شامبو', '2025-05-11', 'cash', 10, 1, 20250456, '2025-05-11 16:56:03', NULL),
(3146, 5, '200.00', 'كهرباء', '2025-05-11', 'cash', 10, 1, 20250456, '2025-05-11 17:07:36', NULL),
(3147, 7, '180.00', 'تورطه', '2025-05-12', 'cash', 10, 1, 20250457, '2025-05-12 20:18:12', NULL),
(3148, 5, '100.00', 'كهرباء', '2025-05-13', 'cash', 10, 1, 20250458, '2025-05-13 15:27:13', NULL),
(3149, 3, '95.00', 'تشقير', '2025-05-13', 'cash', 10, 1, 20250458, '2025-05-13 15:27:36', NULL),
(3150, 5, '35.00', 'سكر', '2025-05-13', 'cash', 10, 1, 20250458, '2025-05-13 15:27:51', NULL),
(3151, 5, '40.00', 'بن', '2025-05-13', 'cash', 10, 1, 20250458, '2025-05-13 15:28:21', NULL),
(3152, 5, '40.00', 'بن', '2025-05-14', 'cash', 10, 1, 20250459, '2025-05-14 17:18:09', NULL),
(3153, 3, '100.00', 'منديل', '2025-05-14', 'cash', 10, 1, 20250459, '2025-05-14 19:39:23', NULL),
(3154, 5, '125.00', 'كهرباء+ منديل', '2025-05-15', 'cash', 10, 1, 20250460, '2025-05-15 13:43:50', NULL),
(3155, 5, '200.00', 'بن+كهرباء', '2025-05-16', 'cash', 10, 1, 20250461, '2025-05-16 14:54:52', NULL),
(3156, 6, '180.00', 'السيد رمضان حلا ق', '2025-05-16', 'cash', 10, 1, 20250461, '2025-05-16 19:00:37', NULL),
(3157, 5, '100.00', 'كهرباء', '2025-05-17', 'cash', 10, 1, 20250462, '2025-05-17 16:32:55', NULL),
(3158, 5, '40.00', 'سكر', '2025-05-17', 'cash', 10, 1, 20250462, '2025-05-17 16:33:15', NULL),
(3159, 4, '850.00', 'نجار', '2025-05-18', 'cash', 10, 1, 20250463, '2025-05-18 11:34:33', NULL),
(3160, 5, '200.00', 'بن+منديل', '2025-05-18', 'cash', 10, 1, 20250463, '2025-05-18 19:01:11', NULL),
(3161, 5, '20.00', 'شاي', '2025-05-18', 'cash', 10, 1, 20250463, '2025-05-18 19:02:36', NULL),
(3162, 5, '200.00', 'كهرباء', '2025-05-19', 'cash', 10, 1, 20250464, '2025-05-19 15:46:17', NULL),
(3163, 3, '165.00', 'حنه', '2025-05-20', 'cash', 10, 1, 20250465, '2025-05-20 14:12:54', NULL),
(3164, 5, '120.00', 'الا رضي', '2025-05-21', 'cash', 10, 1, 20250466, '2025-05-21 17:45:44', NULL),
(3165, 5, '500.00', 'الباشمهندس محمود فودافون كاش', '2025-05-22', 'cash', 10, 1, 20250466, '2025-05-21 21:53:00', NULL),
(3166, 4, '50.00', 'نت', '2025-05-22', 'cash', 10, 1, 20250467, '2025-05-22 16:51:00', NULL),
(3167, 5, '95.00', 'بن', '2025-05-22', 'cash', 10, 1, 20250467, '2025-05-22 16:51:57', NULL),
(3168, 5, '35.00', 'سكر', '2025-05-23', 'cash', 10, 1, 20250467, '2025-05-22 22:13:52', NULL),
(3169, 5, '100.00', 'كهربه', '2025-05-23', 'cash', 10, 1, 20250467, '2025-05-22 23:17:10', NULL),
(3170, 5, '100.00', 'كهربه', '2025-05-23', 'cash', 10, 1, 20250467, '2025-05-22 23:17:10', NULL),
(3171, 3, '250.00', 'منديل + كهرباء', '2025-05-23', 'cash', 10, 1, 20250468, '2025-05-23 13:11:58', NULL),
(3172, 7, '195.00', 'نت', '2025-05-23', 'cash', 10, 1, 20250468, '2025-05-23 16:06:49', NULL),
(3173, 5, '200.00', 'كهرباء', '2025-05-24', 'cash', 10, 1, 20250469, '2025-05-24 14:13:14', NULL),
(3174, 5, '50.00', 'شاي', '2025-05-25', 'cash', 10, 1, 20250469, '2025-05-24 22:00:02', NULL),
(3175, 5, '200.00', 'كهرباء', '2025-05-26', 'cash', 10, 1, 20250471, '2025-05-26 15:19:38', NULL),
(3176, 3, '130.00', 'شمع', '2025-05-26', 'cash', 10, 1, 20250471, '2025-05-26 15:20:46', NULL),
(3177, 5, '155.00', 'كهرباء', '2025-05-27', 'cash', 10, 1, 20250472, '2025-05-27 13:21:34', NULL),
(3178, 5, '45.00', 'سكر', '2025-05-27', 'cash', 10, 1, 20250472, '2025-05-27 13:21:47', NULL),
(3179, 5, '150.00', 'بن', '2025-05-28', 'cash', 10, 1, 20250473, '2025-05-28 14:11:52', NULL),
(3180, 3, '300.00', 'مسبت', '2025-05-29', 'cash', 10, 1, 20250474, '2025-05-29 16:51:21', NULL),
(3181, 5, '100.00', 'كهرباء', '2025-05-29', 'cash', 10, 1, 20250474, '2025-05-29 16:52:34', NULL),
(3182, 5, '20.00', 'جرنين', '2025-05-29', 'cash', 10, 1, 20250474, '2025-05-29 19:21:49', NULL),
(3183, 3, '150.00', 'منديل', '2025-05-30', 'cash', 10, 1, 20250475, '2025-05-30 19:54:04', NULL),
(3184, 5, '125.00', '125', '2025-05-31', 'cash', 10, 1, 20250476, '2025-05-31 15:41:17', NULL),
(3185, 5, '20.00', 'تصوير', '2025-05-31', 'cash', 10, 1, 20250476, '2025-05-31 20:40:21', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `expense_categories`
--

CREATE TABLE `expense_categories` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `expense_categories`
--

INSERT INTO `expense_categories` (`id`, `name`, `description`) VALUES
(1, 'رواتب', 'رواتب ومكافآت الموظفين'),
(2, 'إيجار', 'إيجار المحل والمعدات'),
(3, 'مستلزمات', 'مستلزمات ومواد استهلاكية'),
(4, 'صيانة', 'صيانة وإصلاحات'),
(5, 'مرافق', 'فواتير المرافق والخدمات'),
(6, 'تسويق', 'مصروفات التسويق والإعلان'),
(7, 'متنوعة', 'مصروفات متنوعة أخرى');

-- --------------------------------------------------------

--
-- Table structure for table `inventory`
--

CREATE TABLE `inventory` (
  `id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 0,
  `branch_id` int(11) DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `inventory`
--

INSERT INTO `inventory` (`id`, `product_id`, `quantity`, `branch_id`, `updated_at`) VALUES
(86, 3, 3, 1, '2025-04-22 16:30:05'),
(87, 4, 0, 1, '2025-03-20 14:11:45'),
(88, 5, 1, 1, '2025-04-14 19:33:00'),
(89, 6, 1, 1, '2025-03-20 14:13:53'),
(90, 7, 0, 1, '2025-05-11 10:44:24'),
(91, 8, 0, 1, '2025-03-20 14:15:35'),
(92, 9, 1, 1, '2025-03-20 14:16:37'),
(93, 10, 1, 1, '2025-03-20 14:17:14'),
(94, 11, 1, 1, '2025-03-20 14:18:20'),
(95, 12, 1, 1, '2025-05-11 10:54:11'),
(96, 13, 3, 1, '2025-05-11 10:51:15'),
(97, 14, 0, 1, '2025-03-20 14:21:47'),
(98, 15, 0, 1, '2025-04-26 17:02:44'),
(99, 16, 1, 1, '2025-03-20 14:23:57'),
(100, 17, 1, 1, '2025-03-20 14:24:46'),
(101, 18, 1, 1, '2025-03-20 14:25:28'),
(102, 19, 0, 1, '2025-03-20 14:26:08'),
(103, 20, 0, 1, '2025-03-20 14:27:11'),
(104, 21, 0, 1, '2025-03-20 14:28:25'),
(105, 22, 5, 1, '2025-05-27 18:24:16'),
(106, 23, 1, 1, '2025-05-10 21:35:12'),
(107, 24, 6, 1, '2025-05-11 10:42:57'),
(108, 25, 4, 1, '2025-03-20 14:34:52'),
(109, 26, 4, 1, '2025-03-20 14:36:07'),
(110, 27, 2, 1, '2025-03-20 14:37:25'),
(111, 28, 3, 1, '2025-05-27 18:19:45'),
(112, 29, 1, 1, '2025-03-20 14:40:15'),
(113, 30, 0, 1, '2025-03-20 14:47:18'),
(114, 31, 0, 1, '2025-03-20 14:47:50'),
(115, 32, 0, 1, '2025-03-20 14:48:16'),
(116, 33, 0, 1, '2025-05-27 18:18:49'),
(117, 35, 0, 1, '2025-03-20 14:50:45'),
(118, 36, 0, 1, '2025-03-20 14:51:39'),
(119, 37, 1, 1, '2025-03-20 14:52:41'),
(120, 38, 0, 1, '2025-05-11 10:46:20'),
(121, 39, 0, 1, '2025-03-20 14:54:17'),
(122, 40, 1, 1, '2025-04-12 16:08:43'),
(123, 41, 0, 1, '2025-03-20 14:57:00'),
(124, 42, 0, 1, '2025-03-20 14:57:53'),
(125, 43, 0, 1, '2025-03-20 14:58:27'),
(126, 44, 0, 1, '2025-03-20 15:00:25'),
(127, 45, 0, 1, '2025-03-20 15:01:06'),
(128, 46, 0, 1, '2025-03-20 15:02:00'),
(129, 47, 0, 1, '2025-03-20 15:02:53'),
(130, 48, 0, 1, '2025-03-20 15:03:59'),
(131, 49, 0, 1, '2025-03-20 15:04:14'),
(132, 50, 0, 1, '2025-05-10 21:39:34'),
(133, 51, 0, 1, '2025-03-20 15:05:27'),
(134, 52, 0, 1, '2025-03-20 15:07:18'),
(135, 53, 2, 1, '2025-03-20 15:10:00'),
(136, 54, 2, 1, '2025-03-20 15:10:42'),
(137, 55, 0, 1, '2025-03-20 15:12:26'),
(138, 56, 0, 1, '2025-03-20 15:19:37'),
(139, 57, 2, 1, NULL),
(140, 58, 1, 1, '2025-05-11 10:57:04'),
(141, 59, 1, 1, NULL),
(142, 61, 0, 1, '2025-04-26 17:03:26'),
(143, 62, 0, 1, '2025-05-11 10:48:16'),
(144, 63, 1, 1, NULL),
(145, 64, 1, 1, '2025-05-11 10:45:21'),
(146, 65, 0, 1, '2025-05-11 10:48:41'),
(147, 66, 1, 1, NULL),
(148, 68, 1, 1, NULL),
(149, 70, 0, 1, '2025-05-11 10:47:08'),
(150, 72, 1, 1, NULL),
(151, 73, 0, 1, '2025-04-26 17:01:43'),
(152, 74, 0, 1, '2025-04-14 19:38:55'),
(153, 75, 0, 1, '2025-04-14 19:36:56'),
(154, 76, 0, 1, '2025-04-14 19:34:30'),
(155, 77, 0, 1, '2025-04-14 19:39:32'),
(156, 78, 0, 1, '2025-04-14 13:57:23'),
(157, 79, 3, 1, NULL),
(158, 80, 0, 1, '2025-04-14 19:12:13'),
(159, 81, 0, 1, '2025-04-14 19:31:27'),
(160, 82, 0, 1, '2025-04-14 19:43:39'),
(161, 83, 0, 1, '2025-04-14 19:31:37'),
(162, 84, 0, 1, '2025-04-14 19:34:45'),
(163, 85, 0, 1, '2025-04-14 19:38:32'),
(164, 86, 2, 1, '2025-05-10 21:37:29'),
(165, 87, 0, 1, '2025-04-26 17:03:11'),
(166, 88, 0, 1, '2025-04-14 19:36:35'),
(167, 89, 0, 1, '2025-04-14 19:43:21'),
(168, 90, 0, 1, '2025-05-10 21:36:45'),
(169, 91, 1, 1, '2025-05-11 10:51:51'),
(170, 92, 0, 1, '2025-04-14 19:29:56'),
(171, 93, 0, 1, '2025-04-14 19:30:19'),
(172, 94, 0, 1, '2025-04-14 19:41:37'),
(174, 96, 0, 1, '2025-05-08 14:22:40');

-- --------------------------------------------------------

--
-- Table structure for table `inventory_transactions`
--

CREATE TABLE `inventory_transactions` (
  `id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `transaction_type` enum('in','out') NOT NULL,
  `quantity` int(11) NOT NULL,
  `previous_quantity` int(11) NOT NULL,
  `current_quantity` int(11) NOT NULL,
  `notes` text DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `branch_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `inventory_transactions`
--

INSERT INTO `inventory_transactions` (`id`, `product_id`, `transaction_type`, `quantity`, `previous_quantity`, `current_quantity`, `notes`, `user_id`, `branch_id`, `created_at`) VALUES
(43, 33, 'in', 1, 0, 1, '', 10, 1, '2025-04-07 10:16:23'),
(44, 40, 'out', 1, 1, 0, 'استهلاك داخلي', 8, 1, '2025-04-12 16:06:45'),
(45, 40, 'in', 1, 0, 1, '', 8, 1, '2025-04-12 16:08:43'),
(46, 57, 'in', 2, 0, 2, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-13 16:28:48'),
(47, 58, 'in', 3, 0, 3, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-13 16:31:48'),
(48, 59, 'in', 1, 0, 1, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-13 16:34:57'),
(49, 61, 'in', 1, 0, 1, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-13 16:37:51'),
(50, 62, 'in', 1, 0, 1, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-13 16:39:13'),
(51, 63, 'in', 1, 0, 1, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-13 16:43:16'),
(52, 64, 'in', 2, 0, 2, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-13 16:44:29'),
(53, 65, 'in', 2, 0, 2, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-13 16:46:29'),
(54, 66, 'in', 1, 0, 1, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-13 16:52:37'),
(55, 68, 'in', 1, 0, 1, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-13 16:55:46'),
(56, 70, 'in', 1, 0, 1, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-13 16:59:12'),
(57, 72, 'in', 1, 0, 1, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-13 17:02:45'),
(58, 73, 'in', 1, 0, 1, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-13 17:03:36'),
(59, 74, 'in', 1, 0, 1, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-13 17:04:28'),
(60, 75, 'in', 3, 0, 3, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-13 17:06:58'),
(61, 76, 'in', 5, 0, 5, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-13 17:07:39'),
(62, 77, 'in', 1, 0, 1, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-13 17:08:37'),
(63, 78, 'in', 1, 0, 1, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-13 17:10:07'),
(64, 79, 'in', 3, 0, 3, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-13 17:18:42'),
(65, 80, 'in', 1, 0, 1, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-13 17:25:52'),
(66, 81, 'in', 10, 0, 10, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-13 17:29:16'),
(67, 78, 'out', 1, 1, 0, 'استهلاك داخلي', 10, 1, '2025-04-14 13:57:23'),
(68, 33, 'in', 1, 1, 2, '', 10, 1, '2025-04-14 19:04:30'),
(69, 82, 'in', 1, 0, 1, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-14 19:10:08'),
(70, 80, 'out', 1, 1, 0, 'استهلاك داخلي', 10, 1, '2025-04-14 19:12:13'),
(71, 83, 'in', 1, 0, 1, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-14 19:16:25'),
(72, 84, 'in', 1, 0, 1, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-14 19:17:33'),
(73, 85, 'in', 2, 0, 2, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-14 19:18:21'),
(74, 86, 'in', 4, 0, 4, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-14 19:19:45'),
(75, 87, 'in', 2, 0, 2, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-14 19:20:38'),
(76, 88, 'in', 4, 0, 4, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-14 19:21:40'),
(77, 89, 'in', 3, 0, 3, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-14 19:22:42'),
(78, 90, 'in', 6, 0, 6, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-14 19:23:35'),
(79, 91, 'in', 3, 0, 3, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-14 19:24:26'),
(80, 92, 'in', 7, 0, 7, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-14 19:25:35'),
(81, 93, 'in', 1, 0, 1, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-14 19:26:24'),
(82, 94, 'in', 2, 0, 2, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-04-14 19:28:45'),
(83, 92, 'out', 1, 7, 6, 'استهلاك داخلي', 10, 1, '2025-04-14 19:29:38'),
(84, 92, 'out', 6, 6, 0, 'استهلاك داخلي', 10, 1, '2025-04-14 19:29:56'),
(85, 93, 'out', 1, 1, 0, 'استهلاك داخلي', 10, 1, '2025-04-14 19:30:19'),
(86, 81, 'out', 10, 10, 0, 'استهلاك داخلي', 10, 1, '2025-04-14 19:31:27'),
(87, 83, 'out', 1, 1, 0, 'استهلاك داخلي', 10, 1, '2025-04-14 19:31:37'),
(88, 86, 'out', 1, 4, 3, 'استهلاك داخلي', 10, 1, '2025-04-14 19:32:11'),
(89, 5, 'out', 1, 2, 1, 'استهلاك داخلي', 10, 1, '2025-04-14 19:33:00'),
(90, 24, 'out', 2, 4, 2, 'بيع منتج', 1, 1, '2025-04-14 19:33:05'),
(91, 76, 'out', 5, 5, 0, 'استهلاك داخلي', 10, 1, '2025-04-14 19:34:30'),
(92, 84, 'out', 1, 1, 0, 'استهلاك داخلي', 10, 1, '2025-04-14 19:34:45'),
(93, 24, 'out', 1, 3, 2, 'بيع منتج', 1, 1, '2025-04-14 19:35:24'),
(94, 22, 'out', 2, 9, 7, 'استهلاك داخلي', 10, 1, '2025-04-14 19:35:26'),
(95, 88, 'out', 4, 4, 0, 'استهلاك داخلي', 10, 1, '2025-04-14 19:36:35'),
(96, 75, 'out', 3, 3, 0, 'استهلاك داخلي', 10, 1, '2025-04-14 19:36:56'),
(97, 85, 'out', 2, 2, 0, 'استهلاك داخلي', 10, 1, '2025-04-14 19:38:32'),
(98, 74, 'out', 1, 1, 0, 'استهلاك داخلي', 10, 1, '2025-04-14 19:38:55'),
(99, 77, 'out', 1, 1, 0, 'استهلاك داخلي', 10, 1, '2025-04-14 19:39:32'),
(100, 94, 'out', 2, 2, 0, 'استهلاك داخلي', 10, 1, '2025-04-14 19:41:37'),
(101, 24, 'out', 1, 2, 1, 'بيع منتج', 1, 1, '2025-04-14 19:42:19'),
(102, 89, 'out', 3, 3, 0, 'استهلاك داخلي', 10, 1, '2025-04-14 19:43:21'),
(103, 82, 'out', 1, 1, 0, 'استهلاك داخلي', 10, 1, '2025-04-14 19:43:39'),
(104, 24, 'in', 1, 2, 3, 'إلغاء بيع منتج', 1, 1, '2025-04-14 19:43:47'),
(105, 24, 'in', 2, 3, 5, 'إلغاء بيع منتج', 1, 1, '2025-04-14 19:44:20'),
(106, 24, 'in', 1, 5, 6, 'إلغاء بيع منتج', 1, 1, '2025-04-14 19:44:53'),
(109, 22, 'out', 1, 7, 6, 'استهلاك داخلي', 10, 1, '2025-04-15 20:47:23'),
(111, 28, 'out', 1, 4, 3, 'بيع منتج', 8, 1, '2025-04-15 21:47:23'),
(112, 28, 'in', 1, 3, 4, 'إلغاء بيع منتج', 11, 1, '2025-04-15 21:51:19'),
(113, 3, 'out', 1, 4, 3, 'بيع منتج', 10, 1, '2025-04-22 16:30:05'),
(114, 23, 'out', 1, 1, 0, 'استهلاك داخلي', 10, 1, '2025-04-26 17:01:13'),
(115, 73, 'out', 1, 1, 0, 'استهلاك داخلي', 10, 1, '2025-04-26 17:01:43'),
(116, 15, 'out', 1, 1, 0, 'استهلاك داخلي', 10, 1, '2025-04-26 17:02:44'),
(117, 87, 'out', 1, 2, 1, 'استهلاك داخلي', 10, 1, '2025-04-26 17:03:05'),
(118, 87, 'out', 1, 1, 0, 'استهلاك داخلي', 10, 1, '2025-04-26 17:03:11'),
(119, 61, 'out', 1, 1, 0, 'استهلاك داخلي', 10, 1, '2025-04-26 17:03:26'),
(120, 96, 'in', 1, 0, 1, 'الكمية الأولية عند إضافة المنتج', 10, 1, '2025-05-08 14:19:52'),
(121, 96, 'out', 1, 1, 0, 'بيع منتج', 10, 1, '2025-05-08 14:22:40'),
(122, 28, 'out', 1, 4, 3, 'بيع منتج', 10, 1, '2025-05-09 19:51:20'),
(123, 33, 'out', 1, 2, 1, '', 10, 1, '2025-05-10 21:34:21'),
(124, 23, 'in', 1, 0, 1, 'تعديل المخزون من واجهة تعديل المنتج', 10, 1, '2025-05-10 21:35:12'),
(125, 28, 'out', 1, 3, 2, '', 10, 1, '2025-05-10 21:35:57'),
(126, 90, 'out', 6, 6, 0, '', 10, 1, '2025-05-10 21:36:45'),
(127, 86, 'out', 1, 3, 2, '', 10, 1, '2025-05-10 21:37:29'),
(128, 50, 'out', 1, 1, 0, '', 10, 1, '2025-05-10 21:39:34'),
(129, 24, 'out', 2, 6, 4, '', 10, 1, '2025-05-11 10:41:12'),
(130, 24, 'in', 2, 4, 6, 'تعديل المخزون من واجهة تعديل المنتج', 10, 1, '2025-05-11 10:42:57'),
(131, 7, 'out', 1, 1, 0, '', 10, 1, '2025-05-11 10:44:24'),
(132, 64, 'out', 1, 2, 1, '', 10, 1, '2025-05-11 10:45:21'),
(133, 38, 'out', 1, 2, 1, '', 10, 1, '2025-05-11 10:46:08'),
(134, 38, 'out', 1, 1, 0, '', 10, 1, '2025-05-11 10:46:20'),
(135, 70, 'out', 1, 1, 0, '', 10, 1, '2025-05-11 10:47:08'),
(136, 22, 'in', 1, 6, 7, 'تعديل المخزون من واجهة تعديل المنتج', 10, 1, '2025-05-11 10:47:37'),
(137, 62, 'out', 1, 1, 0, '', 10, 1, '2025-05-11 10:48:16'),
(138, 65, 'out', 2, 2, 0, '', 10, 1, '2025-05-11 10:48:41'),
(139, 13, 'in', 1, 2, 3, 'تعديل المخزون من واجهة تعديل المنتج', 10, 1, '2025-05-11 10:51:15'),
(140, 91, 'out', 2, 3, 1, '', 10, 1, '2025-05-11 10:51:51'),
(141, 12, 'out', 1, 2, 1, '', 10, 1, '2025-05-11 10:54:11'),
(142, 58, 'out', 2, 3, 1, '', 10, 1, '2025-05-11 10:57:04'),
(143, 33, 'out', 1, 1, 0, '', 10, 1, '2025-05-27 18:18:49'),
(144, 28, 'in', 3, 2, 5, '', 10, 1, '2025-05-27 18:19:32'),
(145, 28, 'out', 2, 5, 3, '', 10, 1, '2025-05-27 18:19:45'),
(146, 22, 'out', 2, 7, 5, '', 10, 1, '2025-05-27 18:24:16');

-- --------------------------------------------------------

--
-- Table structure for table `invoices`
--

CREATE TABLE `invoices` (
  `id` int(11) NOT NULL,
  `invoice_number` varchar(20) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `cashier_id` int(11) DEFAULT NULL,
  `employee_id` int(11) DEFAULT NULL COMMENT 'الحلاق',
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `discount_amount` decimal(10,2) DEFAULT 0.00,
  `discount_type` enum('percentage','amount') DEFAULT NULL,
  `tax_amount` decimal(10,2) DEFAULT 0.00,
  `final_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `payment_method` enum('cash','card','other') NOT NULL DEFAULT 'cash',
  `payment_status` enum('paid','partial','unpaid') NOT NULL DEFAULT 'paid',
  `paid_amount` decimal(10,2) DEFAULT 0.00,
  `notes` text DEFAULT NULL,
  `branch_id` int(11) DEFAULT NULL,
  `end_day_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `invoices`
--

INSERT INTO `invoices` (`id`, `invoice_number`, `customer_id`, `cashier_id`, `employee_id`, `total_amount`, `discount_amount`, `discount_type`, `tax_amount`, `final_amount`, `payment_method`, `payment_status`, `paid_amount`, `notes`, `branch_id`, `end_day_id`, `created_at`, `updated_at`) VALUES
(5384, 'INV-00001', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250421, '2025-04-09 07:01:27', '2025-04-19 15:37:54'),
(5385, 'INV-00002', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250421, '2025-04-09 08:24:50', '2025-04-19 15:37:54'),
(5386, 'INV-00003', NULL, 10, NULL, '50.00', '20.00', 'percentage', '0.00', '40.00', 'cash', 'paid', '40.00', '', 1, 20250421, '2025-04-09 08:29:52', '2025-04-19 15:37:54'),
(5388, 'INV-00004', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250423, '2025-04-09 23:48:29', '2025-04-19 15:37:54'),
(5389, 'INV-00005', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250423, '2025-04-10 00:17:39', '2025-04-19 15:37:54'),
(5390, 'INV-00006', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250423, '2025-04-10 03:02:43', '2025-04-19 15:37:54'),
(5391, 'INV-00007', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250423, '2025-04-10 03:20:06', '2025-04-19 15:37:54'),
(5392, 'INV-00008', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250423, '2025-04-10 03:40:40', '2025-04-19 15:37:54'),
(5394, 'INV-00010', NULL, 10, NULL, '150.00', '0.00', 'amount', '0.00', '150.00', 'cash', 'paid', '150.00', '', 1, 20250423, '2025-04-10 03:56:41', '2025-04-19 15:37:54'),
(5395, 'INV-00011', NULL, 1, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250423, '2025-04-10 04:40:14', '2025-04-19 15:37:54'),
(5396, 'INV-00012', NULL, 1, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250423, '2025-04-10 04:40:33', '2025-04-19 15:37:54'),
(5400, 'INV-00013', NULL, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250423, '2025-04-10 06:59:14', '2025-04-19 15:37:54'),
(5401, 'INV-00014', NULL, 8, NULL, '435.00', '0.00', 'amount', '0.00', '435.00', 'cash', 'paid', '435.00', '', 1, 20250423, '2025-04-10 11:19:17', '2025-04-19 15:37:54'),
(5402, 'INV-00015', NULL, 8, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250424, '2025-04-11 00:20:05', '2025-04-19 15:37:54'),
(5403, 'INV-00016', NULL, 8, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250424, '2025-04-11 01:06:33', '2025-04-19 15:37:54'),
(5404, 'INV-00017', 3029, 8, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250424, '2025-04-11 01:42:58', '2025-04-19 15:37:54'),
(5405, 'INV-00018', 3030, 8, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250424, '2025-04-11 01:52:01', '2025-04-19 15:37:54'),
(5406, 'INV-00019', NULL, 8, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250424, '2025-04-11 01:55:20', '2025-04-19 15:37:54'),
(5407, 'INV-00020', NULL, 8, NULL, '125.00', '0.00', 'amount', '0.00', '125.00', 'cash', 'paid', '125.00', '', 1, 20250424, '2025-04-11 01:55:57', '2025-04-19 15:37:54'),
(5408, 'INV-00021', NULL, 8, NULL, '45.00', '0.00', 'amount', '0.00', '45.00', 'cash', 'paid', '45.00', '', 1, 20250424, '2025-04-11 01:56:36', '2025-04-19 15:37:54'),
(5409, 'INV-00022', NULL, 8, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250424, '2025-04-11 01:58:30', '2025-04-19 15:37:54'),
(5410, 'INV-00023', NULL, 8, NULL, '155.00', '0.00', 'amount', '0.00', '155.00', 'cash', 'paid', '155.00', '', 1, 20250424, '2025-04-11 01:59:42', '2025-04-19 15:37:54'),
(5411, 'INV-00024', NULL, 8, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250424, '2025-04-11 02:08:00', '2025-04-19 15:37:54'),
(5412, 'INV-00025', 3031, 8, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250424, '2025-04-11 02:32:28', '2025-04-19 15:37:54'),
(5413, 'INV-00026', NULL, 8, NULL, '200.00', '0.00', 'amount', '0.00', '200.00', 'cash', 'paid', '200.00', '', 1, 20250424, '2025-04-11 03:34:42', '2025-04-19 15:37:54'),
(5414, 'INV-00027', 3032, 8, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250424, '2025-04-11 03:52:09', '2025-04-19 15:37:54'),
(5415, 'INV-00028', NULL, 8, NULL, '65.00', '0.00', 'amount', '0.00', '65.00', 'cash', 'paid', '65.00', '', 1, 20250424, '2025-04-11 04:07:35', '2025-04-19 15:37:54'),
(5416, 'INV-00029', NULL, 8, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250424, '2025-04-11 07:45:49', '2025-04-19 15:37:54'),
(5417, 'INV-00030', NULL, 8, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250424, '2025-04-11 07:46:14', '2025-04-19 15:37:54'),
(5418, 'INV-00031', NULL, 8, NULL, '45.00', '0.00', 'amount', '0.00', '45.00', 'cash', 'paid', '45.00', '', 1, 20250424, '2025-04-11 07:46:43', '2025-04-19 15:37:54'),
(5419, 'INV-00032', 3033, 8, NULL, '110.00', '49.99', 'amount', '0.00', '60.01', 'cash', 'paid', '60.01', '', 1, 20250425, '2025-04-11 23:42:38', '2025-04-19 15:37:54'),
(5420, 'INV-00033', NULL, 8, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250425, '2025-04-11 23:45:05', '2025-04-19 15:37:54'),
(5421, 'INV-00034', NULL, 8, NULL, '150.00', '75.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250425, '2025-04-12 00:02:18', '2025-04-19 15:37:54'),
(5422, 'INV-00035', 3034, 8, NULL, '110.00', '55.00', 'amount', '0.00', '55.00', 'cash', 'paid', '55.00', '', 1, 20250425, '2025-04-12 00:09:40', '2025-04-19 15:37:54'),
(5423, 'INV-00036', NULL, 8, NULL, '75.00', '37.00', 'amount', '0.00', '38.00', 'cash', 'paid', '38.00', '', 1, 20250425, '2025-04-12 00:11:15', '2025-04-19 15:37:54'),
(5424, 'INV-00037', NULL, 8, NULL, '120.00', '50.00', 'percentage', '0.00', '60.00', 'cash', 'paid', '60.00', '', 1, 20250425, '2025-04-12 00:23:31', '2025-04-19 15:37:54'),
(5425, 'INV-00038', NULL, 8, NULL, '185.00', '50.00', 'percentage', '0.00', '92.50', 'cash', 'paid', '92.50', '', 1, 20250425, '2025-04-12 00:25:55', '2025-04-19 15:37:54'),
(5426, 'INV-00039', NULL, 8, NULL, '50.00', '50.00', 'percentage', '0.00', '25.00', 'cash', 'paid', '25.00', '', 1, 20250425, '2025-04-12 00:46:11', '2025-04-19 15:37:54'),
(5427, 'INV-00040', 3035, 8, NULL, '110.00', '50.00', 'percentage', '0.00', '55.00', 'cash', 'paid', '55.00', '', 1, 20250425, '2025-04-12 00:50:38', '2025-04-19 15:37:54'),
(5428, 'INV-00041', 3036, 8, NULL, '110.00', '50.00', 'percentage', '0.00', '55.00', 'cash', 'paid', '55.00', '', 1, 20250425, '2025-04-12 00:52:04', '2025-04-19 15:37:54'),
(5429, 'INV-00042', NULL, 8, NULL, '125.00', '25.00', 'percentage', '0.00', '93.75', 'cash', 'paid', '93.75', '', 1, 20250425, '2025-04-12 02:30:47', '2025-04-19 15:37:54'),
(5430, 'INV-00043', NULL, 8, NULL, '30.00', '50.00', 'percentage', '0.00', '15.00', 'cash', 'paid', '15.00', '', 1, 20250425, '2025-04-12 03:13:22', '2025-04-19 15:37:54'),
(5431, 'INV-00044', NULL, 8, NULL, '210.00', '110.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250425, '2025-04-12 03:19:14', '2025-04-19 15:37:54'),
(5432, 'INV-00045', NULL, 8, NULL, '45.00', '50.00', 'percentage', '0.00', '22.50', 'cash', 'paid', '22.50', '', 1, 20250425, '2025-04-12 04:22:33', '2025-04-19 15:37:54'),
(5433, 'INV-00046', NULL, 8, NULL, '130.00', '50.00', 'percentage', '0.00', '65.00', 'cash', 'paid', '65.00', '', 1, 20250425, '2025-04-12 04:34:19', '2025-04-19 15:37:54'),
(5434, 'INV-00047', NULL, 8, NULL, '45.00', '50.00', 'percentage', '0.00', '22.50', 'cash', 'paid', '22.50', '', 1, 20250425, '2025-04-12 04:52:20', '2025-04-19 15:37:54'),
(5435, 'INV-00048', NULL, 8, NULL, '45.00', '50.00', 'percentage', '0.00', '22.50', 'cash', 'paid', '22.50', '', 1, 20250425, '2025-04-12 04:52:40', '2025-04-19 15:37:54'),
(5436, 'INV-00049', NULL, 8, NULL, '45.00', '50.00', 'percentage', '0.00', '22.50', 'cash', 'paid', '22.50', '', 1, 20250425, '2025-04-12 05:16:49', '2025-04-19 15:37:54'),
(5437, 'INV-00050', NULL, 8, NULL, '45.00', '50.00', 'percentage', '0.00', '22.50', 'cash', 'paid', '22.50', '', 1, 20250425, '2025-04-12 05:18:45', '2025-04-19 15:37:54'),
(5438, 'INV-00051', NULL, 8, NULL, '50.00', '50.00', 'percentage', '0.00', '25.00', 'cash', 'paid', '25.00', '', 1, 20250425, '2025-04-12 06:22:08', '2025-04-19 15:37:54'),
(5439, 'INV-00052', 3037, 8, NULL, '100.00', '20.00', 'percentage', '0.00', '80.00', 'cash', 'paid', '80.00', '', 1, 20250426, '2025-04-12 21:32:51', '2025-04-19 15:37:54'),
(5441, 'INV-00054', 3039, 8, NULL, '155.00', '20.00', 'percentage', '0.00', '124.00', 'cash', 'paid', '124.00', '', 1, 20250426, '2025-04-12 21:43:10', '2025-04-19 15:37:54'),
(5442, 'INV-00055', 3040, 8, NULL, '65.00', '20.00', 'percentage', '0.00', '52.00', 'cash', 'paid', '52.00', '', 1, 20250426, '2025-04-12 21:52:25', '2025-04-19 15:37:54'),
(5443, 'INV-00056', 3041, 8, NULL, '150.00', '20.00', 'percentage', '0.00', '120.00', 'cash', 'paid', '120.00', '', 1, 20250426, '2025-04-12 23:51:42', '2025-04-19 15:37:54'),
(5444, 'INV-00057', NULL, 8, NULL, '100.00', '20.00', 'percentage', '0.00', '80.00', 'cash', 'paid', '80.00', '', 1, 20250426, '2025-04-13 00:47:37', '2025-04-19 15:37:54'),
(5445, 'INV-00058', NULL, 8, NULL, '1550.00', '300.00', 'amount', '0.00', '1250.00', 'cash', 'paid', '1250.00', '', 1, 20250426, '2025-04-13 01:05:41', '2025-04-19 15:37:54'),
(5447, 'INV-00060', 3042, 8, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250426, '2025-04-13 01:47:13', '2025-04-19 15:37:54'),
(5448, 'INV-00061', NULL, 8, NULL, '545.00', '0.00', 'amount', '0.00', '545.00', 'cash', 'paid', '545.00', '', 1, 20250426, '2025-04-13 02:07:54', '2025-04-19 15:37:54'),
(5449, 'INV-00062', 3044, 8, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250426, '2025-04-13 02:22:58', '2025-04-19 15:37:54'),
(5450, 'INV-00063', NULL, 8, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250426, '2025-04-13 08:13:37', '2025-04-19 15:37:54'),
(5451, 'INV-00064', NULL, 8, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250426, '2025-04-13 08:14:08', '2025-04-19 15:37:54'),
(5452, 'INV-00065', 3045, 8, NULL, '195.00', '0.00', 'amount', '0.00', '195.00', 'cash', 'paid', '195.00', '', 1, 20250426, '2025-04-13 08:27:33', '2025-04-19 15:37:54'),
(5453, 'INV-00066', 3046, 8, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250426, '2025-04-13 08:37:52', '2025-04-19 15:37:54'),
(5454, 'INV-00067', 3047, 8, NULL, '250.00', '50.00', 'amount', '0.00', '200.00', 'cash', 'paid', '200.00', '', 1, 20250426, '2025-04-13 10:02:04', '2025-04-19 15:37:54'),
(5455, 'INV-00068', NULL, 8, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250426, '2025-04-13 10:13:46', '2025-04-19 15:37:54'),
(5456, 'INV-00069', NULL, 10, NULL, '35.00', '20.00', 'percentage', '0.00', '28.00', 'cash', 'paid', '28.00', '', 1, 20250427, '2025-04-13 21:23:14', '2025-04-19 15:37:54'),
(5457, 'INV-00070', NULL, 10, NULL, '35.00', '20.00', 'percentage', '0.00', '28.00', 'cash', 'paid', '28.00', '', 1, 20250427, '2025-04-13 21:24:31', '2025-04-19 15:37:54'),
(5458, 'INV-00071', 3048, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250427, '2025-04-13 23:49:55', '2025-04-19 15:37:54'),
(5459, 'INV-00072', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250427, '2025-04-14 01:02:45', '2025-04-19 15:37:54'),
(5460, 'INV-00073', 3049, 10, NULL, '45.00', '0.00', 'amount', '0.00', '45.00', 'cash', 'paid', '45.00', '', 1, 20250427, '2025-04-14 01:09:36', '2025-04-19 15:37:54'),
(5461, 'INV-00074', NULL, 10, NULL, '690.00', '0.00', 'amount', '0.00', '690.00', 'cash', 'paid', '690.00', '', 1, 20250427, '2025-04-14 02:17:59', '2025-04-19 15:37:54'),
(5462, 'INV-00075', 3050, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250427, '2025-04-14 03:05:20', '2025-04-19 15:37:54'),
(5463, 'INV-00076', 3051, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250427, '2025-04-14 03:43:45', '2025-04-19 15:37:54'),
(5464, 'INV-00077', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250427, '2025-04-14 04:08:47', '2025-04-19 15:37:54'),
(5465, 'INV-00078', NULL, 10, NULL, '405.00', '20.00', 'percentage', '0.00', '324.00', 'cash', 'paid', '324.00', '', 1, 20250427, '2025-04-14 04:14:53', '2025-04-19 15:37:54'),
(5466, 'INV-00079', NULL, 10, NULL, '110.00', '10.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250427, '2025-04-14 05:06:16', '2025-04-19 15:37:54'),
(5467, 'INV-00080', 3053, 10, NULL, '155.00', '0.00', 'amount', '0.00', '155.00', 'cash', 'paid', '155.00', '', 1, 20250427, '2025-04-14 06:22:37', '2025-04-19 15:37:54'),
(5468, 'INV-00081', 3054, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250427, '2025-04-14 06:31:57', '2025-04-19 15:37:54'),
(5469, 'INV-00082', 3055, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250427, '2025-04-14 06:59:26', '2025-04-19 15:37:54'),
(5470, 'INV-00083', 3056, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250428, '2025-04-14 21:11:51', '2025-04-19 15:37:54'),
(5471, 'INV-00084', NULL, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250428, '2025-04-15 00:43:04', '2025-04-19 15:37:54'),
(5472, 'INV-00085', 3057, 10, 1, '250.00', '0.00', 'amount', '0.00', '250.00', 'cash', 'paid', '250.00', '', 1, 20250428, '2025-04-15 00:46:17', '2025-04-19 15:37:54'),
(5473, 'INV-00086', 3058, 10, 2, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250428, '2025-04-15 00:49:04', '2025-04-19 15:37:54'),
(5474, 'INV-00087', 3059, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250428, '2025-04-15 02:57:25', '2025-04-19 15:37:54'),
(5475, 'INV-00088', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250428, '2025-04-15 04:16:13', '2025-04-19 15:37:54'),
(5476, 'INV-00089', NULL, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250428, '2025-04-15 04:16:30', '2025-04-19 15:37:54'),
(5477, 'INV-00090', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250428, '2025-04-15 04:16:46', '2025-04-19 15:37:54'),
(5478, 'INV-00091', NULL, 10, NULL, '30.00', '0.00', 'amount', '0.00', '30.00', 'cash', 'paid', '30.00', '', 1, 20250428, '2025-04-15 04:17:05', '2025-04-19 15:37:54'),
(5479, 'INV-00092', 3060, 10, NULL, '65.00', '0.00', 'amount', '0.00', '65.00', 'cash', 'paid', '65.00', '', 1, 20250428, '2025-04-15 04:30:16', '2025-04-19 15:37:54'),
(5480, 'INV-00093', NULL, 10, NULL, '515.00', '15.00', 'amount', '0.00', '500.00', 'cash', 'paid', '500.00', '', 1, 20250428, '2025-04-15 04:55:11', '2025-04-19 15:37:54'),
(5484, 'INV-00094', NULL, 10, NULL, '150.00', '0.00', 'amount', '0.00', '150.00', 'cash', 'paid', '150.00', '', 1, 20250428, '2025-04-15 07:58:48', '2025-04-19 15:37:54'),
(5485, 'INV-00095', 3061, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250429, '2025-04-15 22:32:26', '2025-04-19 15:37:54'),
(5486, 'INV-00096', NULL, 10, NULL, '30.00', '0.00', 'amount', '0.00', '30.00', 'cash', 'paid', '30.00', '', 1, 20250429, '2025-04-15 23:36:08', '2025-04-19 15:37:54'),
(5487, 'INV-00097', 3062, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250429, '2025-04-16 00:11:41', '2025-04-19 15:37:54'),
(5488, 'INV-00098', NULL, 10, NULL, '155.00', '10.00', 'percentage', '0.00', '139.50', 'cash', 'paid', '139.50', '', 1, 20250429, '2025-04-16 00:41:59', '2025-04-19 15:37:54'),
(5489, 'INV-00099', 3063, 10, NULL, '145.00', '0.00', 'amount', '0.00', '145.00', 'cash', 'paid', '145.00', '', 1, 20250429, '2025-04-16 03:52:02', '2025-04-19 15:37:54'),
(5490, 'INV-00100', 3064, 10, 1, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250429, '2025-04-16 04:00:11', '2025-04-19 15:37:54'),
(5491, 'INV-00101', NULL, 10, 1, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250429, '2025-04-16 04:11:23', '2025-04-19 15:37:54'),
(5492, 'INV-00102', 3065, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250429, '2025-04-16 04:46:29', '2025-04-19 15:37:54'),
(5493, 'INV-00103', 3066, 10, 4, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250429, '2025-04-16 05:05:27', '2025-04-19 15:37:54'),
(5496, 'INV-00104', NULL, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250429, '2025-04-16 05:15:34', '2025-04-19 15:37:54'),
(5499, 'INV-00105', 3067, 8, 1, '235.00', '35.00', 'amount', '0.00', '200.00', 'cash', 'paid', '200.00', '', 1, 20250429, '2025-04-16 08:24:51', '2025-04-19 15:37:54'),
(5511, 'INV-00107', NULL, 10, NULL, '40.00', '0.00', 'amount', '0.00', '40.00', 'cash', 'paid', '40.00', '', 1, 20250431, '2025-04-16 21:14:19', '2025-04-19 15:37:54'),
(5512, 'INV-00108', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250431, '2025-04-16 23:11:01', '2025-04-19 15:37:54'),
(5514, 'INV-00110', NULL, 10, NULL, '250.00', '0.00', 'amount', '0.00', '250.00', 'cash', 'paid', '250.00', '', 1, 20250431, '2025-04-16 23:36:34', '2025-04-19 15:37:54'),
(5515, 'INV-00111', NULL, 10, 4, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250431, '2025-04-17 01:19:37', '2025-04-19 15:37:54'),
(5516, 'INV-00112', NULL, 10, 2, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250431, '2025-04-17 01:32:57', '2025-04-19 15:37:54'),
(5517, 'INV-00113', NULL, 10, 2, '150.00', '0.00', 'amount', '0.00', '150.00', 'cash', 'paid', '150.00', '', 1, 20250431, '2025-04-17 01:33:46', '2025-04-19 15:37:54'),
(5518, 'INV-00114', NULL, 10, 2, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250431, '2025-04-17 01:34:13', '2025-04-19 15:37:54'),
(5519, 'INV-00115', NULL, 10, 4, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250431, '2025-04-17 02:01:17', '2025-04-19 15:37:54'),
(5520, 'INV-00116', 3068, 10, 2, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250431, '2025-04-17 03:17:12', '2025-04-19 15:37:54'),
(5521, 'INV-00117', NULL, 10, 3, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250431, '2025-04-17 04:24:08', '2025-04-19 15:37:54'),
(5522, 'INV-00118', 3069, 10, 1, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250431, '2025-04-17 04:26:16', '2025-04-19 15:37:54'),
(5523, 'INV-00119', 3070, 10, 2, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250431, '2025-04-17 06:16:03', '2025-04-19 15:37:54'),
(5524, 'INV-00120', NULL, 10, 3, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250431, '2025-04-17 06:16:40', '2025-04-19 15:37:54'),
(5526, 'INV-00121', 3071, 10, 2, '150.00', '0.00', 'amount', '0.00', '150.00', 'cash', 'paid', '150.00', '', 1, 20250431, '2025-04-17 07:30:14', '2025-04-19 15:37:54'),
(5528, 'INV-00123', NULL, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250432, '2025-04-17 22:42:17', '2025-04-19 15:37:54'),
(5529, 'INV-00124', NULL, 10, NULL, '200.00', '0.00', 'amount', '0.00', '200.00', 'cash', 'paid', '200.00', '', 1, 20250432, '2025-04-17 22:43:31', '2025-04-19 15:37:54'),
(5530, 'INV-00125', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250432, '2025-04-18 02:01:43', '2025-04-19 15:37:54'),
(5531, 'INV-00126', NULL, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250432, '2025-04-18 02:02:11', '2025-04-19 15:37:54'),
(5532, 'INV-00127', 3072, 10, 4, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250432, '2025-04-18 02:54:56', '2025-04-19 15:37:54'),
(5533, 'INV-00128', NULL, 10, 3, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250432, '2025-04-18 03:06:36', '2025-04-19 15:37:54'),
(5534, 'INV-00129', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250432, '2025-04-18 03:28:02', '2025-04-19 15:37:54'),
(5535, 'INV-00130', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250432, '2025-04-18 03:30:31', '2025-04-19 15:37:54'),
(5536, 'INV-00131', NULL, 10, 1, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250432, '2025-04-18 03:30:48', '2025-04-19 15:37:54'),
(5537, 'INV-00132', NULL, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250432, '2025-04-18 03:41:21', '2025-04-19 15:37:54'),
(5538, 'INV-00133', NULL, 10, 3, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250432, '2025-04-18 03:42:54', '2025-04-19 15:37:54'),
(5539, 'INV-00134', NULL, 10, NULL, '150.00', '0.00', 'amount', '0.00', '150.00', 'cash', 'paid', '150.00', '', 1, 20250432, '2025-04-18 04:28:17', '2025-04-19 15:37:54'),
(5540, 'INV-00135', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250432, '2025-04-18 06:11:45', '2025-04-19 15:37:54'),
(5541, 'INV-00136', NULL, 10, 1, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250432, '2025-04-18 07:10:24', '2025-04-19 15:37:54'),
(5542, 'INV-00137', 3073, 10, 4, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250433, '2025-04-18 21:50:43', '2025-04-19 15:37:54'),
(5543, 'INV-00138', NULL, 10, NULL, '110.00', '10.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250433, '2025-04-18 22:29:52', '2025-04-19 15:37:54'),
(5544, 'INV-00139', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250433, '2025-04-18 23:06:07', '2025-04-19 15:37:54'),
(5545, 'INV-00140', 3074, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250433, '2025-04-18 23:43:01', '2025-04-19 15:37:54'),
(5546, 'INV-00141', NULL, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250433, '2025-04-18 23:44:20', '2025-04-19 15:37:54'),
(5547, 'INV-00142', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250433, '2025-04-19 00:05:18', '2025-04-19 15:37:54'),
(5548, 'INV-00143', 3075, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250433, '2025-04-19 00:28:00', '2025-04-19 15:37:54'),
(5549, 'INV-00144', 3076, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250433, '2025-04-19 00:41:51', '2025-04-19 15:37:54'),
(5550, 'INV-00145', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250433, '2025-04-19 00:42:38', '2025-04-19 15:37:54'),
(5551, 'INV-00146', NULL, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250433, '2025-04-19 00:47:37', '2025-04-19 15:37:54'),
(5552, 'INV-00147', 3077, 10, NULL, '110.00', '0.00', 'amount', '0.00', '110.00', 'cash', 'paid', '110.00', '', 1, 20250433, '2025-04-19 01:05:19', '2025-04-19 15:37:54'),
(5553, 'INV-00148', NULL, 10, NULL, '45.00', '0.00', 'amount', '0.00', '45.00', 'cash', 'paid', '45.00', '', 1, 20250433, '2025-04-19 01:05:44', '2025-04-19 15:37:54'),
(5555, 'INV-00150', NULL, 10, NULL, '300.00', '15.00', 'percentage', '0.00', '255.00', 'cash', 'paid', '255.00', '', 1, 20250433, '2025-04-19 02:25:39', '2025-04-19 15:37:54'),
(5556, 'INV-00151', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250433, '2025-04-19 02:26:50', '2025-04-19 15:37:54'),
(5557, 'INV-00152', NULL, 10, NULL, '150.00', '0.00', 'amount', '0.00', '150.00', 'cash', 'paid', '150.00', '', 1, 20250433, '2025-04-19 03:18:48', '2025-04-19 15:37:54'),
(5558, 'INV-00153', NULL, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250433, '2025-04-19 03:59:41', '2025-04-19 15:37:54'),
(5559, 'INV-00154', NULL, 10, NULL, '145.00', '0.00', 'amount', '0.00', '145.00', 'cash', 'paid', '145.00', '', 1, 20250433, '2025-04-19 07:31:48', '2025-04-19 15:37:54'),
(5560, 'INV-00155', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250433, '2025-04-19 07:43:23', '2025-04-19 15:37:54'),
(5561, 'INV-00156', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250433, '2025-04-19 09:03:18', '2025-04-19 15:37:54'),
(5562, 'INV-00157', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250433, '2025-04-19 09:03:36', '2025-04-19 15:37:54'),
(5583, 'INV-00158', 3082, 10, NULL, '65.00', '0.00', 'amount', '0.00', '65.00', 'cash', 'paid', '65.00', '', 1, 20250435, '2025-04-20 19:34:08', NULL),
(5584, 'INV-00159', 3078, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250435, '2025-04-20 21:59:51', NULL),
(5585, 'INV-00160', NULL, 10, 1, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250435, '2025-04-20 22:42:36', NULL),
(5586, 'INV-00161', NULL, 10, 2, '150.00', '0.00', 'amount', '0.00', '150.00', 'cash', 'paid', '150.00', '', 1, 20250435, '2025-04-20 22:43:16', NULL),
(5587, 'INV-00162', NULL, 10, 4, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250435, '2025-04-20 22:43:55', NULL),
(5588, 'INV-00163', NULL, 10, 3, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250435, '2025-04-20 22:45:01', NULL),
(5589, 'INV-00164', 3083, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250435, '2025-04-20 23:23:32', NULL),
(5590, 'INV-00165', 3084, 10, 1, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250435, '2025-04-21 03:14:43', NULL),
(5591, 'INV-00166', 3, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250435, '2025-04-21 04:17:44', NULL),
(5592, 'INV-00167', 3085, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250435, '2025-04-21 05:25:41', NULL),
(5593, 'INV-00168', NULL, 10, NULL, '135.00', '0.00', 'amount', '0.00', '135.00', 'cash', 'paid', '135.00', '', 1, 20250435, '2025-04-21 05:37:32', NULL),
(5594, 'INV-00169', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250435, '2025-04-21 06:09:54', NULL),
(5595, 'INV-00170', 3055, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250436, '2025-04-21 20:34:40', NULL),
(5596, 'INV-00171', 3086, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250436, '2025-04-21 20:56:34', NULL),
(5597, 'INV-00172', 3087, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250436, '2025-04-21 22:29:32', NULL),
(5598, 'INV-00173', 3088, 10, NULL, '40.00', '0.00', 'amount', '0.00', '40.00', 'cash', 'paid', '40.00', '', 1, 20250436, '2025-04-21 23:04:11', NULL),
(5599, 'INV-00174', NULL, 10, NULL, '150.00', '0.00', 'amount', '0.00', '150.00', 'cash', 'paid', '150.00', '', 1, 20250436, '2025-04-21 23:57:36', NULL),
(5600, 'INV-00175', 3089, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250436, '2025-04-21 23:59:20', NULL),
(5601, 'INV-00176', NULL, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250436, '2025-04-21 23:59:55', NULL),
(5602, 'INV-00177', 3090, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250436, '2025-04-22 01:25:58', NULL),
(5603, 'INV-00178', NULL, 10, NULL, '35.00', '0.00', 'amount', '0.00', '35.00', 'cash', 'paid', '35.00', '', 1, 20250436, '2025-04-22 03:22:28', NULL),
(5604, 'INV-00179', 3091, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250436, '2025-04-22 04:19:36', NULL),
(5605, 'INV-00180', 3092, 10, NULL, '150.00', '0.00', 'amount', '0.00', '150.00', 'cash', 'paid', '150.00', '', 1, 20250436, '2025-04-22 05:47:01', NULL),
(5606, 'INV-00181', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250436, '2025-04-22 07:05:34', NULL),
(5607, 'INV-00182', NULL, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250436, '2025-04-22 08:45:00', NULL),
(5608, 'INV-00183', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250436, '2025-04-22 08:55:02', NULL),
(5612, 'INV-00185', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250437, '2025-04-22 22:47:51', NULL),
(5613, 'INV-00186', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250437, '2025-04-22 22:48:36', NULL),
(5614, 'INV-00187', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250437, '2025-04-23 00:20:52', NULL),
(5615, 'INV-00188', NULL, 10, NULL, '150.00', '0.00', 'amount', '0.00', '150.00', 'cash', 'paid', '150.00', '', 1, 20250437, '2025-04-23 00:21:52', NULL),
(5616, 'INV-00189', 3061, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250437, '2025-04-23 01:26:35', NULL),
(5617, 'INV-00190', NULL, 10, NULL, '40.00', '0.00', 'amount', '0.00', '40.00', 'cash', 'paid', '40.00', '', 1, 20250437, '2025-04-23 01:30:05', NULL),
(5618, 'INV-00191', NULL, 10, NULL, '140.00', '0.00', 'amount', '0.00', '140.00', 'cash', 'paid', '140.00', '', 1, 20250437, '2025-04-23 03:06:54', NULL),
(5619, 'INV-00192', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250437, '2025-04-23 03:14:00', NULL),
(5620, 'INV-00193', 3094, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250437, '2025-04-23 04:01:40', NULL),
(5621, 'INV-00194', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250437, '2025-04-23 04:28:26', NULL),
(5622, 'INV-00195', 3095, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250437, '2025-04-23 06:04:16', NULL),
(5623, 'INV-00196', NULL, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250437, '2025-04-23 07:25:21', NULL),
(5624, 'INV-00197', 3096, 10, NULL, '105.00', '0.00', 'amount', '0.00', '105.00', 'cash', 'paid', '105.00', '', 1, 20250438, '2025-04-24 00:05:12', NULL),
(5625, 'INV-00198', NULL, 10, NULL, '145.00', '0.00', 'amount', '0.00', '145.00', 'cash', 'paid', '145.00', '', 1, 20250438, '2025-04-24 20:51:06', NULL),
(5626, 'INV-00199', NULL, 10, NULL, '175.00', '0.00', 'amount', '0.00', '175.00', 'cash', 'paid', '175.00', '', 1, 20250438, '2025-04-24 20:52:24', NULL),
(5627, 'INV-00200', 3097, 10, NULL, '80.00', '0.00', 'amount', '0.00', '80.00', 'cash', 'paid', '80.00', '', 1, 20250439, '2025-04-24 20:55:23', NULL),
(5628, 'INV-00201', 3035, 10, NULL, '150.00', '0.00', 'amount', '0.00', '150.00', 'cash', 'paid', '150.00', '', 1, 20250439, '2025-04-24 22:19:27', NULL),
(5629, 'INV-00202', 3098, 10, NULL, '125.00', '0.00', 'amount', '0.00', '125.00', 'cash', 'paid', '125.00', '', 1, 20250439, '2025-04-24 23:17:37', NULL),
(5630, 'INV-00203', 3030, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250439, '2025-04-24 23:29:09', NULL),
(5631, 'INV-00204', 3099, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250439, '2025-04-25 00:45:44', NULL),
(5632, 'INV-00205', 3029, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250439, '2025-04-25 00:57:51', NULL),
(5633, 'INV-00206', 3100, 10, NULL, '240.00', '0.00', 'amount', '0.00', '240.00', 'cash', 'paid', '240.00', '', 1, 20250439, '2025-04-25 02:01:50', NULL),
(5634, 'INV-00207', 3101, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250439, '2025-04-25 03:00:09', NULL),
(5635, 'INV-00208', 3102, 10, NULL, '40.00', '0.00', 'amount', '0.00', '40.00', 'cash', 'paid', '40.00', '', 1, 20250439, '2025-04-25 03:18:15', NULL),
(5636, 'INV-00209', 3085, 10, NULL, '30.00', '0.00', 'amount', '0.00', '30.00', 'cash', 'paid', '30.00', '', 1, 20250439, '2025-04-25 03:52:53', NULL),
(5637, 'INV-00210', 3103, 10, NULL, '300.00', '0.00', 'amount', '0.00', '300.00', 'cash', 'paid', '300.00', '', 1, 20250439, '2025-04-25 04:16:35', NULL),
(5638, 'INV-00211', 3104, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250439, '2025-04-25 04:35:21', NULL),
(5639, 'INV-00212', 3105, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250439, '2025-04-25 06:10:42', NULL),
(5640, 'INV-00213', 3106, 10, NULL, '65.00', '0.00', 'amount', '0.00', '65.00', 'cash', 'paid', '65.00', '', 1, 20250439, '2025-04-25 06:15:42', NULL),
(5641, 'INV-00214', 3107, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250439, '2025-04-25 09:46:35', NULL),
(5642, 'INV-00215', 3108, 10, NULL, '315.00', '10.00', 'percentage', '0.00', '283.50', 'cash', 'paid', '283.50', '', 1, 20250440, '2025-04-25 20:41:20', NULL),
(5643, 'INV-00216', NULL, 10, NULL, '110.00', '10.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250440, '2025-04-25 22:51:46', NULL),
(5644, 'INV-00217', 3109, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250440, '2025-04-25 23:06:51', NULL),
(5645, 'INV-00218', 3070, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250440, '2025-04-26 00:05:36', NULL),
(5646, 'INV-00219', 3110, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250440, '2025-04-26 00:38:21', NULL),
(5647, 'INV-00220', 3070, 10, NULL, '65.00', '0.00', 'amount', '0.00', '65.00', 'cash', 'paid', '65.00', '', 1, 20250440, '2025-04-26 00:41:52', NULL),
(5648, 'INV-00221', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250440, '2025-04-26 00:47:18', NULL),
(5649, 'INV-00222', NULL, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250440, '2025-04-26 00:47:45', NULL),
(5650, 'INV-00223', 3043, 10, NULL, '205.00', '0.00', 'amount', '0.00', '205.00', 'cash', 'paid', '205.00', '', 1, 20250440, '2025-04-26 00:49:27', NULL),
(5652, 'INV-00225', 3111, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250440, '2025-04-26 02:14:48', NULL),
(5653, 'INV-00226', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250440, '2025-04-26 05:57:42', NULL),
(5654, 'INV-00227', 3112, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250440, '2025-04-26 07:20:24', NULL),
(5655, 'INV-00228', 3113, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250440, '2025-04-26 07:46:32', NULL),
(5656, 'INV-00229', 3114, 10, NULL, '145.00', '0.00', 'amount', '0.00', '145.00', 'cash', 'paid', '145.00', '', 1, 20250441, '2025-04-26 22:44:29', NULL),
(5657, 'INV-00230', 3055, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250441, '2025-04-27 01:29:06', NULL),
(5658, 'INV-00231', 3115, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250441, '2025-04-27 02:57:28', NULL),
(5660, 'INV-00233', NULL, 10, NULL, '205.00', '0.00', 'amount', '0.00', '205.00', 'cash', 'paid', '205.00', '', 1, 20250441, '2025-04-27 04:25:41', NULL),
(5661, 'INV-00234', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250441, '2025-04-27 06:04:58', NULL),
(5662, 'INV-00235', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250441, '2025-04-27 06:05:35', NULL),
(5663, 'INV-00236', 3052, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250441, '2025-04-27 07:17:22', NULL),
(5664, 'INV-00237', 3056, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250442, '2025-04-27 19:26:11', NULL),
(5665, 'INV-00238', 3116, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250442, '2025-04-27 20:16:05', NULL),
(5666, 'INV-00239', 3117, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250442, '2025-04-27 21:18:45', NULL),
(5667, 'INV-00240', 3118, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250442, '2025-04-27 22:59:59', NULL),
(5668, 'INV-00241', 3045, 10, NULL, '105.00', '0.00', 'amount', '0.00', '105.00', 'cash', 'paid', '105.00', '', 1, 20250442, '2025-04-28 01:59:14', NULL),
(5669, 'INV-00242', 3119, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250442, '2025-04-28 02:50:10', NULL),
(5671, 'INV-00244', 3120, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250442, '2025-04-28 02:52:07', NULL),
(5672, 'INV-00245', 3121, 10, NULL, '30.00', '0.00', 'amount', '0.00', '30.00', 'cash', 'paid', '30.00', '', 1, 20250442, '2025-04-28 05:13:14', NULL),
(5673, 'INV-00246', 3009, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250442, '2025-04-28 05:35:01', NULL),
(5674, 'INV-00247', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250442, '2025-04-28 06:28:01', NULL),
(5675, 'INV-00248', 3123, 10, NULL, '40.00', '0.00', 'amount', '0.00', '40.00', 'cash', 'paid', '40.00', '', 1, 20250443, '2025-04-28 20:32:08', NULL),
(5676, 'INV-00249', NULL, 10, NULL, '40.00', '0.00', 'amount', '0.00', '40.00', 'cash', 'paid', '40.00', '', 1, 20250443, '2025-04-29 00:13:55', NULL),
(5677, 'INV-00250', 3124, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250443, '2025-04-29 00:17:36', NULL),
(5678, 'INV-00251', 3125, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250443, '2025-04-29 01:41:42', NULL),
(5679, 'INV-00252', 3126, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250443, '2025-04-29 01:43:19', NULL),
(5680, 'INV-00253', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250443, '2025-04-29 01:45:22', NULL),
(5681, 'INV-00254', NULL, 10, NULL, '90.00', '0.00', 'amount', '0.00', '90.00', 'cash', 'paid', '90.00', '', 1, 20250443, '2025-04-29 02:35:22', NULL),
(5682, 'INV-00255', 3128, 10, NULL, '150.00', '0.00', 'amount', '0.00', '150.00', 'cash', 'paid', '150.00', '', 1, 20250443, '2025-04-29 03:38:02', NULL),
(5683, 'INV-00256', 5, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250443, '2025-04-29 04:58:41', NULL),
(5684, 'INV-00257', 3129, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250444, '2025-04-29 22:57:22', NULL),
(5685, 'INV-00258', 3130, 10, NULL, '195.00', '0.00', 'amount', '0.00', '195.00', 'cash', 'paid', '195.00', '', 1, 20250444, '2025-04-29 23:51:07', NULL),
(5686, 'INV-00259', 3131, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250444, '2025-04-30 00:31:38', NULL),
(5688, 'INV-00260', NULL, 10, NULL, '45.00', '0.00', 'amount', '0.00', '45.00', 'cash', 'paid', '45.00', '', 1, 20250444, '2025-04-30 04:30:05', NULL),
(5689, 'INV-00261', NULL, 10, NULL, '30.00', '0.00', 'amount', '0.00', '30.00', 'cash', 'paid', '30.00', '', 1, 20250444, '2025-04-30 05:11:03', NULL),
(5690, 'INV-00262', NULL, 10, NULL, '150.00', '0.00', 'amount', '0.00', '150.00', 'cash', 'paid', '150.00', '', 1, 20250444, '2025-04-30 05:13:23', NULL),
(5691, 'INV-00263', NULL, 10, NULL, '45.00', '0.00', 'amount', '0.00', '45.00', 'cash', 'paid', '45.00', '', 1, 20250444, '2025-04-30 05:13:34', NULL),
(5692, 'INV-00264', 3014, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250444, '2025-04-30 05:49:25', NULL),
(5693, 'INV-00265', 3132, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250445, '2025-04-30 22:05:35', NULL),
(5694, 'INV-00266', 3133, 10, NULL, '40.00', '0.00', 'amount', '0.00', '40.00', 'cash', 'paid', '40.00', '', 1, 20250445, '2025-05-01 00:37:46', NULL),
(5695, 'INV-00267', 3134, 10, NULL, '155.00', '0.00', 'amount', '0.00', '155.00', 'cash', 'paid', '155.00', '', 1, 20250445, '2025-05-01 01:05:18', NULL),
(5696, 'INV-00268', 3083, 10, NULL, '125.00', '0.00', 'amount', '0.00', '125.00', 'cash', 'paid', '125.00', '', 1, 20250445, '2025-05-01 01:06:31', NULL),
(5697, 'INV-00269', 3121, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250445, '2025-05-01 02:42:45', NULL),
(5698, 'INV-00270', 3135, 10, NULL, '150.00', '0.00', 'amount', '0.00', '150.00', 'cash', 'paid', '150.00', '', 1, 20250445, '2025-05-01 04:44:00', NULL),
(5699, 'INV-00271', 3136, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250445, '2025-05-01 05:04:09', NULL),
(5700, 'INV-00272', 3137, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250445, '2025-05-01 05:20:43', NULL),
(5701, 'INV-00273', 3138, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250445, '2025-05-01 05:44:22', NULL),
(5702, 'INV-00274', NULL, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250446, '2025-05-01 21:04:57', NULL),
(5703, 'INV-00275', 3006, 10, NULL, '150.00', '0.00', 'amount', '0.00', '150.00', 'cash', 'paid', '150.00', '', 1, 20250446, '2025-05-01 21:12:26', NULL),
(5704, 'INV-00276', NULL, 10, NULL, '155.00', '0.00', 'amount', '0.00', '155.00', 'cash', 'paid', '155.00', '', 1, 20250446, '2025-05-01 21:17:05', NULL),
(5705, 'INV-00277', 3139, 10, NULL, '200.00', '0.00', 'amount', '0.00', '200.00', 'cash', 'paid', '200.00', '', 1, 20250446, '2025-05-02 02:48:23', NULL),
(5706, 'INV-00278', 3140, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250446, '2025-05-02 02:49:43', NULL),
(5707, 'INV-00279', 3141, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250446, '2025-05-02 04:21:20', NULL),
(5708, 'INV-00280', 3142, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250446, '2025-05-02 04:27:28', NULL),
(5709, 'INV-00281', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250446, '2025-05-02 04:48:46', NULL),
(5710, 'INV-00282', 3143, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250446, '2025-05-02 04:58:59', NULL),
(5711, 'INV-00283', NULL, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250446, '2025-05-02 06:23:04', NULL),
(5712, 'INV-00284', NULL, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250446, '2025-05-02 06:23:33', NULL),
(5713, 'INV-00285', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250446, '2025-05-02 08:38:34', NULL),
(5714, 'INV-00286', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250446, '2025-05-02 08:39:05', NULL),
(5715, 'INV-00287', 3144, 10, NULL, '180.00', '0.00', 'amount', '0.00', '180.00', 'cash', 'paid', '180.00', '', 1, 20250446, '2025-05-02 08:52:17', NULL),
(5716, 'INV-00288', 3145, 10, NULL, '155.00', '0.00', 'amount', '0.00', '155.00', 'cash', 'paid', '155.00', '', 1, 20250446, '2025-05-02 09:24:16', NULL),
(5717, 'INV-00289', 3146, 10, NULL, '180.00', '0.00', 'amount', '0.00', '180.00', 'cash', 'paid', '180.00', '', 1, 20250446, '2025-05-02 09:27:06', NULL),
(5718, 'INV-00290', 3147, 10, NULL, '85.00', '0.00', 'amount', '0.00', '85.00', 'cash', 'paid', '85.00', '', 1, 20250447, '2025-05-02 21:12:37', NULL),
(5720, 'INV-00291', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250447, '2025-05-02 22:32:27', NULL),
(5721, 'INV-00292', NULL, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250447, '2025-05-02 22:49:08', NULL),
(5722, 'INV-00293', 3061, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250447, '2025-05-03 00:14:38', NULL),
(5723, 'INV-00294', NULL, 10, NULL, '200.00', '0.00', 'amount', '0.00', '200.00', 'cash', 'paid', '200.00', '', 1, 20250447, '2025-05-03 00:18:36', NULL),
(5724, 'INV-00295', 3088, 10, NULL, '40.00', '0.00', 'amount', '0.00', '40.00', 'cash', 'paid', '40.00', '', 1, 20250447, '2025-05-03 00:51:13', NULL),
(5725, 'INV-00296', 3148, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250447, '2025-05-03 01:31:03', NULL),
(5726, 'INV-00297', 3149, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250447, '2025-05-03 02:18:43', NULL),
(5727, 'INV-00298', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250447, '2025-05-03 03:30:45', NULL),
(5728, 'INV-00299', 3150, 8, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250447, '2025-05-03 04:33:54', NULL),
(5729, 'INV-00300', 3054, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250447, '2025-05-03 05:34:33', NULL),
(5730, 'INV-00301', NULL, 10, NULL, '400.00', '0.00', 'amount', '0.00', '400.00', 'cash', 'paid', '400.00', '', 1, 20250447, '2025-05-03 06:34:47', NULL),
(5731, 'INV-00302', 3043, 10, NULL, '205.00', '0.00', 'amount', '0.00', '205.00', 'cash', 'paid', '205.00', '', 1, 20250447, '2025-05-03 09:02:27', NULL),
(5732, 'INV-00303', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250448, '2025-05-03 21:44:33', NULL),
(5733, 'INV-00304', NULL, 10, NULL, '90.00', '0.00', 'amount', '0.00', '90.00', 'cash', 'paid', '90.00', '', 1, 20250448, '2025-05-03 21:45:13', NULL),
(5735, 'INV-00305', NULL, 10, NULL, '130.00', '0.00', 'amount', '0.00', '130.00', 'cash', 'paid', '130.00', '', 1, 20250448, '2025-05-04 00:31:32', NULL),
(5736, 'INV-00306', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250448, '2025-05-04 01:00:19', NULL),
(5737, 'INV-00307', NULL, 10, NULL, '45.00', '0.00', 'amount', '0.00', '45.00', 'cash', 'paid', '45.00', '', 1, 20250448, '2025-05-04 01:55:15', NULL),
(5738, 'INV-00308', NULL, 10, NULL, '45.00', '0.00', 'amount', '0.00', '45.00', 'cash', 'paid', '45.00', '', 1, 20250448, '2025-05-04 01:55:39', NULL),
(5739, 'INV-00309', 3151, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250448, '2025-05-04 02:22:27', NULL),
(5740, 'INV-00310', 3152, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250448, '2025-05-04 04:58:26', NULL),
(5741, 'INV-00311', NULL, 10, NULL, '205.00', '0.00', 'amount', '0.00', '205.00', 'cash', 'paid', '205.00', '', 1, 20250448, '2025-05-04 05:00:20', NULL),
(5742, 'INV-00312', 3104, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250448, '2025-05-04 06:53:54', NULL),
(5743, 'INV-00313', 3153, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250448, '2025-05-04 07:13:47', NULL),
(5744, 'INV-00314', 3154, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250448, '2025-05-04 08:01:15', NULL),
(5745, 'INV-00315', 6, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250449, '2025-05-04 22:38:05', NULL),
(5746, 'INV-00316', 3155, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250449, '2025-05-04 23:21:49', NULL),
(5747, 'INV-00317', NULL, 10, NULL, '150.00', '0.00', 'amount', '0.00', '150.00', 'cash', 'paid', '150.00', '', 1, 20250449, '2025-05-04 23:41:02', NULL),
(5748, 'INV-00318', NULL, 10, NULL, '45.00', '0.00', 'amount', '0.00', '45.00', 'cash', 'paid', '45.00', '', 1, 20250449, '2025-05-05 00:18:15', NULL),
(5749, 'INV-00319', 3156, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250449, '2025-05-05 00:56:44', NULL),
(5750, 'INV-00320', 3133, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250449, '2025-05-05 02:18:53', NULL),
(5751, 'INV-00321', 3123, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250449, '2025-05-05 02:20:53', NULL);
INSERT INTO `invoices` (`id`, `invoice_number`, `customer_id`, `cashier_id`, `employee_id`, `total_amount`, `discount_amount`, `discount_type`, `tax_amount`, `final_amount`, `payment_method`, `payment_status`, `paid_amount`, `notes`, `branch_id`, `end_day_id`, `created_at`, `updated_at`) VALUES
(5752, 'INV-00322', NULL, 10, NULL, '65.00', '0.00', 'amount', '0.00', '65.00', 'cash', 'paid', '65.00', '', 1, 20250449, '2025-05-05 02:34:25', NULL),
(5753, 'INV-00323', 3087, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250449, '2025-05-05 02:41:05', NULL),
(5754, 'INV-00324', 3157, 10, NULL, '65.00', '0.00', 'amount', '0.00', '65.00', 'cash', 'paid', '65.00', '', 1, 20250449, '2025-05-05 03:27:12', NULL),
(5755, 'INV-00325', NULL, 10, NULL, '45.00', '0.00', 'amount', '0.00', '45.00', 'cash', 'paid', '45.00', '', 1, 20250449, '2025-05-05 04:51:44', NULL),
(5756, 'INV-00326', 3158, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250449, '2025-05-05 08:11:13', NULL),
(5757, 'INV-00327', NULL, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250449, '2025-05-05 08:52:12', NULL),
(5758, 'INV-00328', 3083, 10, NULL, '90.00', '0.00', 'amount', '0.00', '90.00', 'cash', 'paid', '90.00', '', 1, 20250450, '2025-05-05 21:39:26', NULL),
(5759, 'INV-00329', 3043, 10, NULL, '150.00', '0.00', 'amount', '0.00', '150.00', 'cash', 'paid', '150.00', '', 1, 20250450, '2025-05-05 21:41:29', NULL),
(5760, 'INV-00330', 3006, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250450, '2025-05-05 21:53:41', NULL),
(5761, 'INV-00331', 3121, 10, NULL, '40.00', '0.00', 'amount', '0.00', '40.00', 'cash', 'paid', '40.00', '', 1, 20250450, '2025-05-06 00:04:54', NULL),
(5762, 'INV-00332', 3062, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250450, '2025-05-06 00:07:07', NULL),
(5763, 'INV-00333', 3159, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250450, '2025-05-06 01:05:06', NULL),
(5764, 'INV-00334', NULL, 10, NULL, '45.00', '0.00', 'amount', '0.00', '45.00', 'cash', 'paid', '45.00', '', 1, 20250450, '2025-05-06 01:05:45', NULL),
(5765, 'INV-00335', 3160, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250450, '2025-05-06 02:44:14', NULL),
(5766, 'INV-00336', 3018, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250450, '2025-05-06 04:29:11', NULL),
(5767, 'INV-00337', 3161, 10, NULL, '40.00', '0.00', 'amount', '0.00', '40.00', 'cash', 'paid', '40.00', '', 1, 20250450, '2025-05-06 04:34:01', NULL),
(5768, 'INV-00338', 3162, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250450, '2025-05-06 04:47:48', NULL),
(5769, 'INV-00339', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250450, '2025-05-06 05:53:16', NULL),
(5770, 'INV-00340', 3046, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250450, '2025-05-06 06:45:19', NULL),
(5771, 'INV-00341', 3163, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250450, '2025-05-06 07:37:12', NULL),
(5772, 'INV-00342', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250451, '2025-05-06 19:12:50', NULL),
(5773, 'INV-00343', 3164, 10, NULL, '40.00', '0.00', 'amount', '0.00', '40.00', 'cash', 'paid', '40.00', '', 1, 20250451, '2025-05-06 19:37:11', NULL),
(5774, 'INV-00344', 3165, 10, NULL, '40.00', '0.00', 'amount', '0.00', '40.00', 'cash', 'paid', '40.00', '', 1, 20250451, '2025-05-06 20:05:40', NULL),
(5775, 'INV-00345', 3166, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250451, '2025-05-07 00:50:16', NULL),
(5776, 'INV-00346', 3135, 10, NULL, '80.00', '0.00', 'amount', '0.00', '80.00', 'cash', 'paid', '80.00', '', 1, 20250451, '2025-05-07 02:15:30', NULL),
(5777, 'INV-00347', NULL, 10, NULL, '575.00', '0.00', 'amount', '0.00', '575.00', 'cash', 'paid', '575.00', '', 1, 20250451, '2025-05-07 02:17:05', NULL),
(5778, 'INV-00348', 3167, 10, NULL, '190.00', '0.00', 'amount', '0.00', '190.00', 'cash', 'paid', '190.00', '', 1, 20250451, '2025-05-07 03:31:15', NULL),
(5779, 'INV-00349', 3168, 10, NULL, '155.00', '0.00', 'amount', '0.00', '155.00', 'cash', 'paid', '155.00', '', 1, 20250451, '2025-05-07 03:33:46', NULL),
(5780, 'INV-00350', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250451, '2025-05-07 04:40:47', NULL),
(5781, 'INV-00351', NULL, 8, NULL, '55.00', '0.00', 'amount', '0.00', '55.00', 'cash', 'paid', '55.00', '', 1, 20250451, '2025-05-07 04:47:03', NULL),
(5782, 'INV-00352', NULL, 10, NULL, '190.00', '0.00', 'amount', '0.00', '190.00', 'cash', 'paid', '190.00', '', 1, 20250451, '2025-05-07 06:40:52', NULL),
(5783, 'INV-00353', 3131, 10, NULL, '150.00', '0.00', 'amount', '0.00', '150.00', 'cash', 'paid', '150.00', '', 1, 20250452, '2025-05-07 21:44:34', NULL),
(5784, 'INV-00354', 3059, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250452, '2025-05-07 23:38:47', NULL),
(5785, 'INV-00355', NULL, 10, NULL, '155.00', '0.00', 'amount', '0.00', '155.00', 'cash', 'paid', '155.00', '', 1, 20250452, '2025-05-08 02:42:17', NULL),
(5786, 'INV-00356', 3169, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250452, '2025-05-08 03:34:21', NULL),
(5787, 'INV-00357', 2, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250452, '2025-05-08 04:45:01', NULL),
(5789, 'INV-00359', 3073, 10, NULL, '110.00', '0.00', 'amount', '0.00', '110.00', 'cash', 'paid', '110.00', '', 1, 20250452, '2025-05-08 04:57:28', NULL),
(5790, 'INV-00360', NULL, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250452, '2025-05-08 06:13:12', NULL),
(5791, 'INV-00361', 3170, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250452, '2025-05-08 06:44:34', NULL),
(5792, 'INV-00362', 3171, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250452, '2025-05-08 06:52:50', NULL),
(5793, 'INV-00363', 3172, 10, NULL, '65.00', '0.00', 'amount', '0.00', '65.00', 'cash', 'paid', '65.00', '', 1, 20250452, '2025-05-08 07:13:16', NULL),
(5794, 'INV-00364', 3052, 10, NULL, '180.00', '0.00', 'amount', '0.00', '180.00', 'cash', 'paid', '180.00', '', 1, 20250452, '2025-05-08 08:36:17', NULL),
(5795, 'INV-00365', 3173, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250453, '2025-05-08 19:34:10', NULL),
(5796, 'INV-00366', NULL, 10, NULL, '30.00', '0.00', 'amount', '0.00', '30.00', 'cash', 'paid', '30.00', '', 1, 20250453, '2025-05-08 19:35:58', NULL),
(5797, 'INV-00367', 3035, 10, NULL, '150.00', '0.00', 'amount', '0.00', '150.00', 'cash', 'paid', '150.00', '', 1, 20250453, '2025-05-08 21:47:00', NULL),
(5798, 'INV-00368', 3174, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250453, '2025-05-08 23:01:46', NULL),
(5799, 'INV-00369', 3175, 10, NULL, '200.00', '0.00', 'amount', '0.00', '200.00', 'cash', 'paid', '200.00', '', 1, 20250453, '2025-05-09 00:22:40', NULL),
(5800, 'INV-00370', 3176, 10, NULL, '155.00', '0.00', 'amount', '0.00', '155.00', 'cash', 'paid', '155.00', '', 1, 20250453, '2025-05-09 01:16:22', NULL),
(5801, 'INV-00371', 3177, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250453, '2025-05-09 01:25:29', NULL),
(5802, 'INV-00372', NULL, 10, NULL, '485.00', '0.00', 'amount', '0.00', '485.00', 'cash', 'paid', '485.00', '', 1, 20250453, '2025-05-09 01:29:11', NULL),
(5803, 'INV-00373', 3178, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250453, '2025-05-09 01:31:08', NULL),
(5804, 'INV-00374', 3179, 10, NULL, '145.00', '0.00', 'amount', '0.00', '145.00', 'cash', 'paid', '145.00', '', 1, 20250453, '2025-05-09 01:49:39', NULL),
(5805, 'INV-00375', 3180, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250453, '2025-05-09 02:38:14', NULL),
(5806, 'INV-00376', 3181, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250453, '2025-05-09 02:41:21', NULL),
(5807, 'INV-00377', 3182, 10, NULL, '150.00', '0.00', 'amount', '0.00', '150.00', 'cash', 'paid', '150.00', '', 1, 20250453, '2025-05-09 03:45:40', NULL),
(5808, 'INV-00378', 3183, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250453, '2025-05-09 04:33:54', NULL),
(5809, 'INV-00379', 3184, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250453, '2025-05-09 04:34:43', NULL),
(5810, 'INV-00380', 3029, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250453, '2025-05-09 04:48:03', NULL),
(5811, 'INV-00381', 3095, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250453, '2025-05-09 05:33:51', NULL),
(5812, 'INV-00382', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250453, '2025-05-09 05:57:20', NULL),
(5813, 'INV-00383', NULL, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250453, '2025-05-09 05:57:47', NULL),
(5814, 'INV-00384', 3006, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250453, '2025-05-09 07:09:03', NULL),
(5815, 'INV-00385', 3187, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250453, '2025-05-09 07:32:53', NULL),
(5816, 'INV-00386', 3034, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250454, '2025-05-09 19:22:08', NULL),
(5818, 'INV-00388', 3188, 10, NULL, '185.00', '0.00', 'amount', '0.00', '185.00', 'cash', 'paid', '185.00', '', 1, 20250454, '2025-05-09 21:15:40', NULL),
(5819, 'INV-00389', 3189, 10, NULL, '400.00', '0.00', 'amount', '0.00', '400.00', 'cash', 'paid', '400.00', '', 1, 20250454, '2025-05-09 21:47:53', NULL),
(5820, 'INV-00390', 3033, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250454, '2025-05-09 22:10:48', NULL),
(5821, 'INV-00391', 3190, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250454, '2025-05-10 01:39:27', NULL),
(5822, 'INV-00392', 3061, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250454, '2025-05-10 01:41:04', NULL),
(5823, 'INV-00393', NULL, 10, NULL, '120.00', '0.00', 'amount', '0.00', '120.00', 'cash', 'paid', '120.00', '', 1, 20250454, '2025-05-10 01:51:22', NULL),
(5824, 'INV-00394', 3014, 10, NULL, '125.00', '0.00', 'amount', '0.00', '125.00', 'cash', 'paid', '125.00', '', 1, 20250454, '2025-05-10 01:55:13', NULL),
(5825, 'INV-00395', NULL, 10, NULL, '40.00', '0.00', 'amount', '0.00', '40.00', 'cash', 'paid', '40.00', '', 1, 20250454, '2025-05-10 02:02:54', NULL),
(5826, 'INV-00396', NULL, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250454, '2025-05-10 03:21:30', NULL),
(5827, 'INV-00397', 3191, 10, NULL, '150.00', '0.00', 'amount', '0.00', '150.00', 'cash', 'paid', '150.00', '', 1, 20250454, '2025-05-10 03:24:08', NULL),
(5828, 'INV-00398', 2, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250454, '2025-05-10 03:24:36', NULL),
(5829, 'INV-00399', 2, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250454, '2025-05-10 03:24:37', NULL),
(5831, 'INV-00400', NULL, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250454, '2025-05-10 05:49:11', NULL),
(5832, 'INV-00401', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250454, '2025-05-10 05:49:20', NULL),
(5833, 'INV-00402', NULL, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250454, '2025-05-10 05:51:20', NULL),
(5834, 'INV-00403', 3080, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250454, '2025-05-10 06:23:03', NULL),
(5835, 'INV-00404', 3047, 10, NULL, '155.00', '0.00', 'amount', '0.00', '155.00', 'cash', 'paid', '155.00', '', 1, 20250454, '2025-05-10 08:28:58', NULL),
(5836, 'INV-00405', 3112, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250455, '2025-05-10 19:37:25', NULL),
(5837, 'INV-00406', 3192, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250455, '2025-05-10 20:44:19', NULL),
(5838, 'INV-00407', 3104, 10, NULL, '130.00', '0.00', 'amount', '0.00', '130.00', 'cash', 'paid', '130.00', '', 1, 20250455, '2025-05-10 20:47:41', NULL),
(5839, 'INV-00408', 3123, 10, NULL, '40.00', '0.00', 'amount', '0.00', '40.00', 'cash', 'paid', '40.00', '', 1, 20250455, '2025-05-10 21:04:59', NULL),
(5840, 'INV-00409', 3193, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250455, '2025-05-10 21:14:56', NULL),
(5841, 'INV-00410', 3082, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250455, '2025-05-10 21:38:40', NULL),
(5842, 'INV-00411', 3126, 10, NULL, '200.00', '0.00', 'amount', '0.00', '200.00', 'cash', 'paid', '200.00', '', 1, 20250455, '2025-05-10 21:45:20', NULL),
(5843, 'INV-00412', 3117, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250455, '2025-05-10 22:32:33', NULL),
(5844, 'INV-00413', 3194, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250455, '2025-05-10 23:20:18', NULL),
(5845, 'INV-00414', NULL, 10, NULL, '45.00', '0.00', 'amount', '0.00', '45.00', 'cash', 'paid', '45.00', '', 1, 20250455, '2025-05-11 01:18:32', NULL),
(5846, 'INV-00415', 3195, 10, NULL, '160.00', '0.00', 'amount', '0.00', '160.00', 'cash', 'paid', '160.00', '', 1, 20250455, '2025-05-11 01:42:31', NULL),
(5847, 'INV-00416', 3196, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250455, '2025-05-11 01:43:25', NULL),
(5848, 'INV-00417', 3110, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250455, '2025-05-11 02:04:10', NULL),
(5849, 'INV-00418', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250455, '2025-05-11 02:31:42', NULL),
(5850, 'INV-00419', 3197, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250455, '2025-05-11 02:55:04', NULL),
(5851, 'INV-00420', 3088, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250455, '2025-05-11 03:10:39', NULL),
(5852, 'INV-00421', NULL, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250455, '2025-05-11 03:53:28', NULL),
(5853, 'INV-00422', 3198, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250455, '2025-05-11 04:35:07', NULL),
(5854, 'INV-00423', 3199, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250455, '2025-05-11 04:53:41', NULL),
(5855, 'INV-00424', NULL, 10, NULL, '110.00', '0.00', 'amount', '0.00', '110.00', 'cash', 'paid', '110.00', '', 1, 20250455, '2025-05-11 05:03:56', NULL),
(5856, 'INV-00425', 3138, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250455, '2025-05-11 05:22:04', NULL),
(5857, 'INV-00426', NULL, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250455, '2025-05-11 06:48:23', NULL),
(5858, 'INV-00427', 3045, 10, NULL, '245.00', '0.00', 'amount', '0.00', '245.00', 'cash', 'paid', '245.00', '', 1, 20250455, '2025-05-11 07:32:11', NULL),
(5859, 'INV-00428', 3066, 10, NULL, '350.00', '0.00', 'amount', '0.00', '350.00', 'cash', 'paid', '350.00', '', 1, 20250455, '2025-05-11 07:53:45', NULL),
(5860, 'INV-00429', 3065, 10, NULL, '250.00', '0.00', 'amount', '0.00', '250.00', 'cash', 'paid', '250.00', '', 1, 20250455, '2025-05-11 08:17:32', NULL),
(5861, 'INV-00430', 3006, 10, NULL, '135.00', '0.00', 'amount', '0.00', '135.00', 'cash', 'paid', '135.00', '', 1, 20250455, '2025-05-11 08:58:56', NULL),
(5862, 'INV-00431', 3200, 10, NULL, '110.00', '0.00', 'amount', '0.00', '110.00', 'cash', 'paid', '110.00', '', 1, 20250456, '2025-05-11 22:29:59', NULL),
(5863, 'INV-00432', 3201, 10, NULL, '40.00', '0.00', 'amount', '0.00', '40.00', 'cash', 'paid', '40.00', '', 1, 20250456, '2025-05-12 00:06:45', NULL),
(5864, 'INV-00433', 3202, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250456, '2025-05-12 00:45:39', NULL),
(5865, 'INV-00434', 3203, 10, NULL, '190.00', '0.00', 'amount', '0.00', '190.00', 'cash', 'paid', '190.00', '', 1, 20250456, '2025-05-12 02:41:15', NULL),
(5866, 'INV-00435', 3056, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250456, '2025-05-12 04:11:06', NULL),
(5867, 'INV-00436', 3204, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250456, '2025-05-12 04:12:14', NULL),
(5868, 'INV-00437', 3141, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250456, '2025-05-12 04:32:44', NULL),
(5869, 'INV-00438', 3205, 10, NULL, '190.00', '0.00', 'amount', '0.00', '190.00', 'cash', 'paid', '190.00', '', 1, 20250456, '2025-05-12 07:39:14', NULL),
(5870, 'INV-00439', NULL, 10, NULL, '250.00', '0.00', 'amount', '0.00', '250.00', 'cash', 'paid', '250.00', '', 1, 20250456, '2025-05-12 09:15:45', NULL),
(5871, 'INV-00440', NULL, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250457, '2025-05-12 20:51:46', NULL),
(5872, 'INV-00441', 3127, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250457, '2025-05-12 22:27:26', NULL),
(5873, 'INV-00442', 3121, 10, NULL, '40.00', '0.00', 'amount', '0.00', '40.00', 'cash', 'paid', '40.00', '', 1, 20250457, '2025-05-12 22:29:02', NULL),
(5874, 'INV-00443', 3122, 10, NULL, '40.00', '0.00', 'amount', '0.00', '40.00', 'cash', 'paid', '40.00', '', 1, 20250457, '2025-05-12 22:31:48', NULL),
(5875, 'INV-00444', 3116, 10, NULL, '155.00', '0.00', 'amount', '0.00', '155.00', 'cash', 'paid', '155.00', '', 1, 20250457, '2025-05-12 22:44:29', NULL),
(5876, 'INV-00445', 3206, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250457, '2025-05-12 23:28:50', NULL),
(5877, 'INV-00446', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250457, '2025-05-13 00:11:28', NULL),
(5878, 'INV-00447', NULL, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250457, '2025-05-13 01:50:56', NULL),
(5879, 'INV-00448', 3207, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250457, '2025-05-13 02:03:30', NULL),
(5880, 'INV-00449', 3208, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250457, '2025-05-13 02:09:02', NULL),
(5881, 'INV-00450', NULL, 10, NULL, '45.00', '0.00', 'amount', '0.00', '45.00', 'cash', 'paid', '45.00', '', 1, 20250457, '2025-05-13 03:31:11', NULL),
(5882, 'INV-00451', 3209, 10, NULL, '35.00', '0.00', 'amount', '0.00', '35.00', 'cash', 'paid', '35.00', '', 1, 20250457, '2025-05-13 06:13:07', NULL),
(5883, 'INV-00452', 3010, 10, NULL, '90.00', '0.00', 'amount', '0.00', '90.00', 'cash', 'paid', '90.00', '', 1, 20250457, '2025-05-13 07:53:12', NULL),
(5884, 'INV-00453', 3210, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250458, '2025-05-13 21:49:23', NULL),
(5885, 'INV-00454', 3211, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250458, '2025-05-13 22:52:23', NULL),
(5886, 'INV-00455', NULL, 10, NULL, '145.00', '0.00', 'amount', '0.00', '145.00', 'cash', 'paid', '145.00', '', 1, 20250458, '2025-05-13 23:23:46', NULL),
(5887, 'INV-00456', 3006, 10, NULL, '155.00', '0.00', 'amount', '0.00', '155.00', 'cash', 'paid', '155.00', '', 1, 20250458, '2025-05-14 00:22:41', NULL),
(5888, 'INV-00457', 3116, 10, NULL, '155.00', '0.00', 'amount', '0.00', '155.00', 'cash', 'paid', '155.00', '', 1, 20250458, '2025-05-14 00:55:35', NULL),
(5889, 'INV-00458', 3212, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250458, '2025-05-14 01:57:37', NULL),
(5890, 'INV-00459', 3213, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250458, '2025-05-14 02:03:02', NULL),
(5891, 'INV-00460', 3005, 10, NULL, '125.00', '0.00', 'amount', '0.00', '125.00', 'cash', 'paid', '125.00', '', 1, 20250458, '2025-05-14 02:25:08', NULL),
(5892, 'INV-00461', 3214, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250458, '2025-05-14 03:45:10', NULL),
(5893, 'INV-00462', 3215, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250458, '2025-05-14 04:46:41', NULL),
(5894, 'INV-00463', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250458, '2025-05-14 07:34:06', NULL),
(5895, 'INV-00464', NULL, 10, NULL, '40.00', '0.00', 'amount', '0.00', '40.00', 'cash', 'paid', '40.00', '', 1, 20250458, '2025-05-14 07:42:45', NULL),
(5896, 'INV-00465', 3216, 10, NULL, '85.00', '0.00', 'amount', '0.00', '85.00', 'cash', 'paid', '85.00', '', 1, 20250459, '2025-05-14 20:09:07', NULL),
(5897, 'INV-00466', 3018, 10, NULL, '30.00', '0.00', 'amount', '0.00', '30.00', 'cash', 'paid', '30.00', '', 1, 20250459, '2025-05-14 21:52:17', NULL),
(5898, 'INV-00467', 3217, 10, NULL, '125.00', '0.00', 'amount', '0.00', '125.00', 'cash', 'paid', '125.00', '', 1, 20250459, '2025-05-14 23:00:38', NULL),
(5899, 'INV-00468', 3218, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250459, '2025-05-15 02:04:57', NULL),
(5900, 'INV-00469', 3219, 10, NULL, '205.00', '0.00', 'amount', '0.00', '205.00', 'cash', 'paid', '205.00', '', 1, 20250459, '2025-05-15 03:20:30', NULL),
(5901, 'INV-00470', 3087, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250459, '2025-05-15 04:07:43', NULL),
(5902, 'INV-00471', NULL, 10, NULL, '40.00', '0.00', 'amount', '0.00', '40.00', 'cash', 'paid', '40.00', '', 1, 20250459, '2025-05-15 06:24:20', NULL),
(5903, 'INV-00472', 3191, 10, NULL, '45.00', '0.00', 'amount', '0.00', '45.00', 'cash', 'paid', '45.00', '', 1, 20250460, '2025-05-15 20:18:02', NULL),
(5904, 'INV-00473', 3220, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250460, '2025-05-15 22:20:37', NULL),
(5905, 'INV-00474', 3063, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250460, '2025-05-15 23:17:05', NULL),
(5906, 'INV-00475', 3123, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250460, '2025-05-15 23:44:22', NULL),
(5907, 'INV-00476', NULL, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250460, '2025-05-15 23:49:03', NULL),
(5908, 'INV-00477', 3221, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250460, '2025-05-16 01:01:55', NULL),
(5909, 'INV-00478', 3222, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250460, '2025-05-16 01:30:27', NULL),
(5910, 'INV-00479', 3223, 10, NULL, '350.00', '0.00', 'amount', '0.00', '350.00', 'cash', 'paid', '350.00', '', 1, 20250460, '2025-05-16 01:40:12', NULL),
(5911, 'INV-00480', 3224, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250460, '2025-05-16 01:59:21', NULL),
(5912, 'INV-00481', 3225, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250460, '2025-05-16 02:20:39', NULL),
(5913, 'INV-00482', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250460, '2025-05-16 02:21:58', NULL),
(5914, 'INV-00483', 3226, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250460, '2025-05-16 03:24:00', NULL),
(5915, 'INV-00484', NULL, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250460, '2025-05-16 04:30:20', NULL),
(5916, 'INV-00485', 3139, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250460, '2025-05-16 04:34:08', NULL),
(5917, 'INV-00486', 3227, 10, NULL, '155.00', '0.00', 'amount', '0.00', '155.00', 'cash', 'paid', '155.00', '', 1, 20250460, '2025-05-16 05:17:28', NULL),
(5919, 'INV-00487', 3229, 10, NULL, '400.00', '0.00', 'amount', '0.00', '400.00', 'cash', 'paid', '400.00', '', 1, 20250460, '2025-05-16 08:19:08', NULL),
(5920, 'INV-00488', 3108, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250461, '2025-05-16 21:25:21', '2025-05-16 11:51:52'),
(5921, 'INV-00489', 3099, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250461, '2025-05-16 21:53:54', NULL),
(5923, 'INV-00491', 3006, 10, NULL, '135.00', '0.00', 'amount', '0.00', '135.00', 'cash', 'paid', '135.00', '', 1, 20250461, '2025-05-16 23:51:04', NULL),
(5924, 'INV-00492', 3033, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250461, '2025-05-17 00:27:24', NULL),
(5925, 'INV-00493', 3229, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250461, '2025-05-17 00:50:59', NULL),
(5926, 'INV-00494', 3151, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250461, '2025-05-17 01:59:25', NULL),
(5927, 'INV-00495', 3230, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250461, '2025-05-17 02:04:28', NULL),
(5928, 'INV-00496', 3231, 10, NULL, '150.00', '0.00', 'amount', '0.00', '150.00', 'cash', 'paid', '150.00', '', 1, 20250461, '2025-05-17 03:14:12', NULL),
(5929, 'INV-00497', 3232, 10, NULL, '185.00', '0.00', 'amount', '0.00', '185.00', 'cash', 'paid', '185.00', '', 1, 20250461, '2025-05-17 05:02:11', NULL),
(5930, 'INV-00498', 3192, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250461, '2025-05-17 05:51:49', NULL),
(5931, 'INV-00499', 3233, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250461, '2025-05-17 05:54:05', NULL),
(5932, 'INV-00500', 3028, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250461, '2025-05-17 05:54:37', NULL),
(5933, 'INV-00501', 3234, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250461, '2025-05-17 06:48:49', NULL),
(5934, 'INV-00502', 3117, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250462, '2025-05-17 19:13:31', NULL),
(5935, 'INV-00503', 3235, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250462, '2025-05-18 02:08:01', NULL),
(5936, 'INV-00504', 3141, 10, NULL, '45.00', '0.00', 'amount', '0.00', '45.00', 'cash', 'paid', '45.00', '', 1, 20250462, '2025-05-18 03:55:18', NULL),
(5937, 'INV-00505', 3164, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250462, '2025-05-18 06:06:29', NULL),
(5938, 'INV-00506', 3236, 10, NULL, '420.00', '55.00', 'amount', '0.00', '365.00', 'cash', 'paid', '365.00', '', 1, 20250462, '2025-05-18 06:07:51', '2025-05-17 20:12:01'),
(5939, 'INV-00507', NULL, 10, NULL, '45.00', '0.00', 'amount', '0.00', '45.00', 'cash', 'paid', '45.00', '', 1, 20250462, '2025-05-18 06:08:14', NULL),
(5940, 'INV-00508', 3006, 10, NULL, '85.00', '0.00', 'amount', '0.00', '85.00', 'cash', 'paid', '85.00', '', 1, 20250463, '2025-05-18 22:50:07', NULL),
(5941, 'INV-00509', 3169, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250463, '2025-05-18 23:40:08', NULL),
(5942, 'INV-00510', 3110, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250463, '2025-05-19 01:04:02', NULL),
(5943, 'INV-00511', 3237, 10, NULL, '180.00', '0.00', 'amount', '0.00', '180.00', 'cash', 'paid', '180.00', '', 1, 20250463, '2025-05-19 01:50:36', NULL),
(5944, 'INV-00512', 3238, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250463, '2025-05-19 04:00:04', '2025-05-18 18:05:41'),
(5945, 'INV-00513', 3239, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250463, '2025-05-19 04:58:33', NULL),
(5946, 'INV-00514', 3096, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250463, '2025-05-19 05:33:28', NULL),
(5947, 'INV-00515', 3056, 10, NULL, '110.00', '0.00', 'amount', '0.00', '110.00', 'cash', 'paid', '110.00', '', 1, 20250463, '2025-05-19 07:15:26', NULL),
(5948, 'INV-00516', 3240, 10, NULL, '350.00', '0.00', 'amount', '0.00', '350.00', 'cash', 'paid', '350.00', '', 1, 20250463, '2025-05-19 07:55:36', NULL),
(5949, 'INV-00517', 3241, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250463, '2025-05-19 08:27:38', NULL),
(5950, 'INV-00518', 3242, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250464, '2025-05-19 20:43:29', NULL),
(5951, 'INV-00519', 3166, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250464, '2025-05-19 23:30:49', NULL),
(5952, 'INV-00520', 3243, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250464, '2025-05-20 01:45:40', NULL),
(5953, 'INV-00521', 3244, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250464, '2025-05-20 01:47:30', NULL),
(5954, 'INV-00522', 3245, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250464, '2025-05-20 02:46:58', NULL),
(5955, 'INV-00523', 3104, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250464, '2025-05-20 04:50:59', NULL),
(5956, 'INV-00524', 3246, 10, NULL, '30.00', '0.00', 'amount', '0.00', '30.00', 'cash', 'paid', '30.00', '', 1, 20250464, '2025-05-20 05:19:20', NULL),
(5957, 'INV-00525', 3106, 10, NULL, '110.00', '0.00', 'amount', '0.00', '110.00', 'cash', 'paid', '110.00', '', 1, 20250464, '2025-05-20 06:35:56', NULL),
(5959, 'INV-00527', 3013, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250465, '2025-05-20 19:08:34', NULL),
(5960, 'INV-00528', 3191, 10, NULL, '150.00', '0.00', 'amount', '0.00', '150.00', 'cash', 'paid', '150.00', '', 1, 20250465, '2025-05-21 00:09:29', NULL),
(5961, 'INV-00529', 3099, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250465, '2025-05-21 00:23:18', NULL),
(5962, 'INV-00530', 3247, 10, NULL, '190.00', '0.00', 'amount', '0.00', '190.00', 'cash', 'paid', '190.00', '', 1, 20250465, '2025-05-21 00:35:26', NULL),
(5963, 'INV-00531', 3248, 10, NULL, '65.00', '0.00', 'amount', '0.00', '65.00', 'cash', 'paid', '65.00', '', 1, 20250465, '2025-05-21 00:42:58', NULL),
(5964, 'INV-00532', 3116, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250465, '2025-05-21 01:06:20', NULL),
(5965, 'INV-00533', 3249, 10, 4, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250465, '2025-05-21 01:08:28', '2025-05-20 18:05:47'),
(5966, 'INV-00534', 3250, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250465, '2025-05-21 01:49:19', NULL),
(5967, 'INV-00535', 3251, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250465, '2025-05-21 03:48:28', NULL),
(5968, 'INV-00536', 3064, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250465, '2025-05-21 03:55:37', NULL),
(5969, 'INV-00537', 3131, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250465, '2025-05-21 04:40:00', NULL),
(5970, 'INV-00538', 3252, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250465, '2025-05-21 08:07:13', NULL),
(5971, 'INV-00539', 3253, 10, NULL, '350.00', '0.00', 'amount', '0.00', '350.00', 'cash', 'paid', '350.00', '', 1, 20250466, '2025-05-21 20:15:19', NULL),
(5972, 'INV-00540', 3253, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250466, '2025-05-21 20:16:18', NULL),
(5973, 'INV-00541', 3088, 10, NULL, '30.00', '0.00', 'amount', '0.00', '30.00', 'cash', 'paid', '30.00', '', 1, 20250466, '2025-05-21 21:12:55', NULL),
(5974, 'INV-00542', 3107, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250466, '2025-05-21 21:42:58', NULL),
(5975, 'INV-00543', 3082, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250466, '2025-05-21 21:58:03', NULL),
(5976, 'INV-00544', 3123, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250466, '2025-05-21 23:40:09', NULL),
(5977, 'INV-00545', 3030, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250466, '2025-05-22 01:33:54', NULL),
(5978, 'INV-00546', 3184, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250466, '2025-05-22 03:18:03', NULL),
(5979, 'INV-00547', 3183, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250466, '2025-05-22 05:05:20', NULL),
(5980, 'INV-00548', NULL, 10, NULL, '40.00', '0.00', 'amount', '0.00', '40.00', 'cash', 'paid', '40.00', '', 1, 20250466, '2025-05-22 05:13:43', NULL),
(5981, 'INV-00549', 3121, 10, NULL, '30.00', '0.00', 'amount', '0.00', '30.00', 'cash', 'paid', '30.00', '', 1, 20250466, '2025-05-22 05:44:49', NULL),
(5982, 'INV-00550', 3126, 10, NULL, '190.00', '0.00', 'amount', '0.00', '190.00', 'cash', 'paid', '190.00', '', 1, 20250466, '2025-05-22 07:42:10', NULL),
(5983, 'INV-00551', 3254, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250466, '2025-05-22 07:49:13', NULL),
(5984, 'INV-00552', 3174, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250467, '2025-05-22 22:11:41', NULL),
(5985, 'INV-00553', 3255, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250467, '2025-05-23 00:04:14', NULL),
(5986, 'INV-00554', 3256, 10, NULL, '120.00', '0.00', 'amount', '0.00', '120.00', 'cash', 'paid', '120.00', '', 1, 20250467, '2025-05-23 00:11:42', NULL),
(5987, 'INV-00555', 3257, 10, NULL, '65.00', '0.00', 'amount', '0.00', '65.00', 'cash', 'paid', '65.00', '', 1, 20250467, '2025-05-23 00:14:32', NULL),
(5988, 'INV-00556', 3006, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250467, '2025-05-23 02:42:28', NULL),
(5989, 'INV-00557', 3258, 10, NULL, '105.00', '0.00', 'amount', '0.00', '105.00', 'cash', 'paid', '105.00', '', 1, 20250467, '2025-05-23 02:50:01', NULL),
(5990, 'INV-00558', 3187, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250467, '2025-05-23 07:26:47', NULL),
(5991, 'INV-00559', 3134, 10, NULL, '155.00', '0.00', 'amount', '0.00', '155.00', 'cash', 'paid', '155.00', '', 1, 20250467, '2025-05-23 09:14:51', NULL),
(5992, 'INV-00560', 3259, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250468, '2025-05-23 23:14:00', NULL),
(5993, 'INV-00561', 3033, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250468, '2025-05-23 23:15:11', '2025-05-23 13:15:48'),
(5994, 'INV-00562', 3260, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250468, '2025-05-24 00:03:38', NULL),
(5995, 'INV-00563', 3018, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250468, '2025-05-24 00:05:35', NULL),
(5996, 'INV-00564', 3062, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250468, '2025-05-24 00:06:38', NULL),
(5998, 'INV-00565', 3061, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250468, '2025-05-24 00:09:56', NULL),
(5999, 'INV-00566', 3223, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250468, '2025-05-24 00:25:06', NULL),
(6000, 'INV-00567', 3229, 10, NULL, '200.00', '0.00', 'amount', '0.00', '200.00', 'cash', 'paid', '200.00', '', 1, 20250468, '2025-05-24 00:27:40', NULL),
(6002, 'INV-00568', 3190, 10, NULL, '190.00', '0.00', 'amount', '0.00', '190.00', 'cash', 'paid', '190.00', '', 1, 20250468, '2025-05-24 01:00:41', NULL),
(6006, 'INV-00569', 3261, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250468, '2025-05-24 01:42:42', NULL),
(6007, 'INV-00570', 3262, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250468, '2025-05-24 01:57:53', NULL),
(6008, 'INV-00571', 3263, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250468, '2025-05-24 01:59:37', NULL),
(6009, 'INV-00572', 3213, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250468, '2025-05-24 02:04:48', NULL),
(6010, 'INV-00573', 3264, 10, NULL, '115.00', '0.00', 'amount', '0.00', '115.00', 'cash', 'paid', '115.00', '', 1, 20250468, '2025-05-24 03:41:08', NULL),
(6011, 'INV-00574', 3265, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250468, '2025-05-24 03:43:02', NULL),
(6012, 'INV-00575', 3266, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250468, '2025-05-24 03:48:56', NULL),
(6013, 'INV-00576', 3182, 10, NULL, '200.00', '0.00', 'amount', '0.00', '200.00', 'cash', 'paid', '200.00', '', 1, 20250468, '2025-05-24 03:57:57', NULL),
(6014, 'INV-00577', 3267, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250468, '2025-05-24 04:57:09', NULL),
(6015, 'INV-00578', 3205, 10, NULL, '150.00', '0.00', 'amount', '0.00', '150.00', 'cash', 'paid', '150.00', '', 1, 20250468, '2025-05-24 07:26:22', NULL),
(6016, 'INV-00579', 3114, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250469, '2025-05-24 21:56:20', NULL),
(6017, 'INV-00580', 3115, 10, NULL, '150.00', '0.00', 'amount', '0.00', '150.00', 'cash', 'paid', '150.00', '', 1, 20250469, '2025-05-25 00:56:36', NULL),
(6018, 'INV-00581', 3268, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250469, '2025-05-25 02:28:50', NULL),
(6019, 'INV-00582', 3269, 10, NULL, '145.00', '0.00', 'amount', '0.00', '145.00', 'cash', 'paid', '145.00', '', 1, 20250469, '2025-05-25 03:10:58', NULL),
(6020, 'INV-00583', 3163, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250469, '2025-05-25 03:33:30', NULL),
(6021, 'INV-00584', 3270, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250469, '2025-05-25 03:37:07', NULL),
(6022, 'INV-00585', 3117, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250469, '2025-05-25 03:38:56', NULL),
(6023, 'INV-00586', 3066, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250469, '2025-05-25 05:54:01', NULL),
(6024, 'INV-00587', 3065, 10, NULL, '155.00', '0.00', 'amount', '0.00', '155.00', 'cash', 'paid', '155.00', '', 1, 20250469, '2025-05-25 05:55:17', NULL),
(6025, 'INV-00588', 3271, 10, NULL, '65.00', '0.00', 'amount', '0.00', '65.00', 'cash', 'paid', '65.00', '', 1, 20250469, '2025-05-25 05:56:41', NULL),
(6026, 'INV-00589', 3043, 10, NULL, '150.00', '0.00', 'amount', '0.00', '150.00', 'cash', 'paid', '150.00', '', 1, 20250469, '2025-05-25 07:11:03', NULL),
(6027, 'INV-00590', 3047, 10, NULL, '155.00', '0.00', 'amount', '0.00', '155.00', 'cash', 'paid', '155.00', '', 1, 20250469, '2025-05-25 07:13:44', NULL),
(6028, 'INV-00591', 3272, 10, NULL, '600.00', '0.00', 'amount', '0.00', '600.00', 'cash', 'paid', '600.00', '', 1, 20250469, '2025-05-25 07:59:32', NULL),
(6029, 'INV-00592', 3273, 10, NULL, '155.00', '0.00', 'amount', '0.00', '155.00', 'cash', 'paid', '155.00', '', 1, 20250469, '2025-05-25 08:36:39', NULL),
(6030, 'INV-00593', 5, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250470, '2025-05-25 22:07:43', NULL),
(6031, 'INV-00594', 3045, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250470, '2025-05-25 23:24:04', NULL),
(6032, 'INV-00595', 3274, 10, NULL, '120.00', '0.00', 'amount', '0.00', '120.00', 'cash', 'paid', '120.00', '', 1, 20250470, '2025-05-26 00:17:48', NULL),
(6033, 'INV-00596', NULL, 10, NULL, '40.00', '0.00', 'amount', '0.00', '40.00', 'cash', 'paid', '40.00', '', 1, 20250470, '2025-05-26 00:21:46', NULL),
(6034, 'INV-00597', 3275, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250470, '2025-05-26 02:35:42', NULL),
(6035, 'INV-00598', 3276, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250470, '2025-05-26 02:55:36', NULL),
(6036, 'INV-00599', 3087, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250470, '2025-05-26 04:11:25', NULL),
(6037, 'INV-00600', 3277, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250470, '2025-05-26 04:15:34', NULL),
(6038, 'INV-00601', 3029, 10, NULL, '65.00', '0.00', 'amount', '0.00', '65.00', 'cash', 'paid', '65.00', '', 1, 20250470, '2025-05-26 05:33:39', NULL),
(6039, 'INV-00602', NULL, 10, NULL, '45.00', '0.00', 'amount', '0.00', '45.00', 'cash', 'paid', '45.00', '', 1, 20250470, '2025-05-26 06:14:23', NULL),
(6040, 'INV-00603', 3278, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250470, '2025-05-26 08:05:35', NULL),
(6041, 'INV-00604', 3063, 10, NULL, '205.00', '0.00', 'amount', '0.00', '205.00', 'cash', 'paid', '205.00', '', 1, 20250470, '2025-05-26 08:11:32', NULL),
(6042, 'INV-00605', 3006, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250471, '2025-05-27 01:37:52', NULL),
(6043, 'INV-00606', 3148, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250471, '2025-05-27 02:43:13', NULL),
(6044, 'INV-00607', 3209, 10, NULL, '30.00', '0.00', 'amount', '0.00', '30.00', 'cash', 'paid', '30.00', '', 1, 20250471, '2025-05-27 04:51:54', NULL),
(6045, 'INV-00608', 3279, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250471, '2025-05-27 05:18:04', NULL),
(6046, 'INV-00609', 3204, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250471, '2025-05-27 05:21:58', NULL),
(6047, 'INV-00610', 3280, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250471, '2025-05-27 06:18:49', NULL),
(6048, 'INV-00611', 3048, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250471, '2025-05-27 06:19:33', NULL),
(6049, 'INV-00612', 3281, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250472, '2025-05-27 20:59:24', NULL),
(6050, 'INV-00613', 3125, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250472, '2025-05-27 23:06:00', NULL),
(6051, 'INV-00614', 3127, 10, NULL, '150.00', '0.00', 'amount', '0.00', '150.00', 'cash', 'paid', '150.00', '', 1, 20250472, '2025-05-27 23:22:21', NULL),
(6052, 'INV-00615', 3282, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250472, '2025-05-28 00:35:27', NULL),
(6053, 'INV-00616', 3283, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250472, '2025-05-28 00:43:47', NULL),
(6054, 'INV-00617', 3284, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250472, '2025-05-28 01:46:55', NULL),
(6055, 'INV-00618', 3084, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250472, '2025-05-28 04:33:21', NULL),
(6056, 'INV-00619', 3285, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250472, '2025-05-28 04:37:41', NULL),
(6057, 'INV-00620', 3286, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250472, '2025-05-28 05:40:35', NULL),
(6058, 'INV-00621', 3287, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250472, '2025-05-28 05:42:08', NULL),
(6059, 'INV-00622', 3123, 10, NULL, '40.00', '0.00', 'amount', '0.00', '40.00', 'cash', 'paid', '40.00', '', 1, 20250472, '2025-05-28 06:57:42', NULL),
(6060, 'INV-00623', 3288, 10, 4, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250472, '2025-05-28 08:13:11', NULL),
(6061, 'INV-00624', 3289, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250472, '2025-05-28 08:25:38', NULL),
(6062, 'INV-00625', 3290, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250472, '2025-05-28 09:01:12', NULL),
(6063, 'INV-00626', 3104, 10, NULL, '185.00', '0.00', 'amount', '0.00', '185.00', 'cash', 'paid', '185.00', '', 1, 20250472, '2025-05-28 09:18:00', NULL),
(6064, 'INV-00627', 3007, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250473, '2025-05-29 00:09:11', NULL),
(6065, 'INV-00628', 2005, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250473, '2025-05-29 00:25:24', NULL),
(6066, 'INV-00629', 2004, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250473, '2025-05-29 00:26:08', NULL),
(6067, 'INV-00630', 3169, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250473, '2025-05-29 00:26:57', NULL),
(6068, 'INV-00631', 3291, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250473, '2025-05-29 00:46:54', NULL),
(6069, 'INV-00632', 3292, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250473, '2025-05-29 03:02:26', NULL),
(6070, 'INV-00633', 3293, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250473, '2025-05-29 04:23:07', NULL),
(6071, 'INV-00634', 3294, 10, NULL, '235.00', '0.00', 'amount', '0.00', '235.00', 'cash', 'paid', '235.00', '', 1, 20250473, '2025-05-29 04:30:37', NULL),
(6072, 'INV-00635', 3143, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250473, '2025-05-29 04:37:41', NULL),
(6073, 'INV-00636', NULL, 10, NULL, '65.00', '0.00', 'amount', '0.00', '65.00', 'cash', 'paid', '65.00', '', 1, 20250473, '2025-05-29 04:47:03', NULL),
(6074, 'INV-00637', 3246, 10, NULL, '30.00', '0.00', 'amount', '0.00', '30.00', 'cash', 'paid', '30.00', '', 1, 20250473, '2025-05-29 05:03:18', NULL),
(6075, 'INV-00638', 3120, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250473, '2025-05-29 05:59:47', NULL),
(6077, 'INV-00639', 3296, 10, NULL, '1150.00', '150.00', 'amount', '0.00', '1000.00', 'cash', 'paid', '1000.00', '', 1, 20250473, '2025-05-29 08:09:49', NULL),
(6078, 'INV-00640', 3214, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250473, '2025-05-29 08:10:28', NULL),
(6079, 'INV-00641', 3297, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250473, '2025-05-29 08:49:29', NULL),
(6080, 'INV-00642', NULL, 8, NULL, '40.00', '0.00', 'amount', '0.00', '40.00', 'cash', 'paid', '40.00', '', 1, 20250474, '2025-05-29 17:59:23', NULL),
(6081, 'INV-00643', 3030, 8, NULL, '125.00', '0.00', 'amount', '0.00', '125.00', 'cash', 'paid', '125.00', '', 1, 20250474, '2025-05-29 21:30:45', NULL),
(6083, 'INV-00645', 3298, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250474, '2025-05-29 21:41:33', NULL),
(6085, 'INV-00647', 3045, 10, NULL, '45.00', '0.00', 'amount', '0.00', '45.00', 'cash', 'paid', '45.00', '', 1, 20250474, '2025-05-29 22:27:49', NULL),
(6086, 'INV-00648', 3258, 10, NULL, '105.00', '0.00', 'amount', '0.00', '105.00', 'cash', 'paid', '105.00', '', 1, 20250474, '2025-05-30 00:41:50', NULL),
(6087, 'INV-00649', 3299, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250474, '2025-05-30 02:36:45', NULL),
(6088, 'INV-00650', 3088, 10, NULL, '30.00', '0.00', 'amount', '0.00', '30.00', 'cash', 'paid', '30.00', '', 1, 20250474, '2025-05-30 02:37:49', NULL),
(6089, 'INV-00651', 3191, 10, NULL, '195.00', '0.00', 'amount', '0.00', '195.00', 'cash', 'paid', '195.00', '', 1, 20250474, '2025-05-30 02:41:36', NULL),
(6090, 'INV-00652', 3183, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250474, '2025-05-30 04:50:45', NULL),
(6091, 'INV-00653', NULL, 10, NULL, '45.00', '0.00', 'amount', '0.00', '45.00', 'cash', 'paid', '45.00', '', 1, 20250474, '2025-05-30 05:45:19', NULL);
INSERT INTO `invoices` (`id`, `invoice_number`, `customer_id`, `cashier_id`, `employee_id`, `total_amount`, `discount_amount`, `discount_type`, `tax_amount`, `final_amount`, `payment_method`, `payment_status`, `paid_amount`, `notes`, `branch_id`, `end_day_id`, `created_at`, `updated_at`) VALUES
(6092, 'INV-00654', 3102, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250474, '2025-05-30 06:31:06', '2025-05-29 22:03:20'),
(6093, 'INV-00655', 3034, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250474, '2025-05-30 06:36:28', NULL),
(6094, 'INV-00656', 3300, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250474, '2025-05-30 08:29:12', NULL),
(6096, 'INV-00657', 3174, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250474, '2025-05-30 10:13:30', NULL),
(6097, 'INV-00658', 3301, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250475, '2025-05-30 21:37:29', NULL),
(6098, 'INV-00659', 3302, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250475, '2025-05-30 22:10:40', NULL),
(6099, 'INV-00660', 3303, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250475, '2025-05-30 22:16:53', NULL),
(6100, 'INV-00661', 3033, 10, NULL, '40.00', '0.00', 'amount', '0.00', '40.00', 'cash', 'paid', '40.00', '', 1, 20250475, '2025-05-30 23:01:39', NULL),
(6101, 'INV-00662', 3304, 10, NULL, '90.00', '0.00', 'amount', '0.00', '90.00', 'cash', 'paid', '90.00', '', 1, 20250475, '2025-05-31 00:19:47', NULL),
(6102, 'INV-00663', NULL, 10, NULL, '45.00', '0.00', 'amount', '0.00', '45.00', 'cash', 'paid', '45.00', '', 1, 20250475, '2025-05-31 00:35:18', NULL),
(6103, 'INV-00664', 3305, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250475, '2025-05-31 02:17:23', NULL),
(6104, 'INV-00665', 3306, 10, NULL, '65.00', '0.00', 'amount', '0.00', '65.00', 'cash', 'paid', '65.00', '', 1, 20250475, '2025-05-31 02:19:46', NULL),
(6105, 'INV-00666', 3259, 10, NULL, '250.00', '0.00', 'amount', '0.00', '250.00', 'cash', 'paid', '250.00', '', 1, 20250475, '2025-05-31 04:08:59', NULL),
(6106, 'INV-00667', 3307, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250475, '2025-05-31 05:01:05', NULL),
(6107, 'INV-00668', 3308, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250475, '2025-05-31 05:03:47', NULL),
(6108, 'INV-00669', 3309, 10, NULL, '325.00', '0.00', 'amount', '0.00', '325.00', 'cash', 'paid', '325.00', '', 1, 20250475, '2025-05-31 05:52:05', NULL),
(6109, 'INV-00670', 3020, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250475, '2025-05-31 06:18:56', NULL),
(6110, 'INV-00671', 3174, 10, NULL, '75.00', '0.00', 'amount', '0.00', '75.00', 'cash', 'paid', '75.00', '', 1, 20250476, '2025-06-01 01:30:22', NULL),
(6111, 'INV-00672', 3301, 10, NULL, '50.00', '0.00', 'amount', '0.00', '50.00', 'cash', 'paid', '50.00', '', 1, 20250476, '2025-06-01 01:33:02', NULL),
(6112, 'INV-00673', 3130, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250476, '2025-06-01 02:03:45', NULL),
(6113, 'INV-00674', 3166, 10, NULL, '145.00', '0.00', 'amount', '0.00', '145.00', 'cash', 'paid', '145.00', '', 1, 20250476, '2025-06-01 02:40:03', NULL),
(6114, 'INV-00675', 3310, 10, NULL, '45.00', '0.00', 'amount', '0.00', '45.00', 'cash', 'paid', '45.00', '', 1, 20250476, '2025-06-01 03:17:46', NULL),
(6115, 'INV-00676', 3100, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250476, '2025-06-01 03:28:14', NULL),
(6116, 'INV-00677', 3043, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250476, '2025-06-01 04:31:54', NULL),
(6117, 'INV-00678', 1004, 10, NULL, '100.00', '0.00', 'amount', '0.00', '100.00', 'cash', 'paid', '100.00', '', 1, 20250476, '2025-06-01 06:00:09', NULL),
(6118, 'INV-00679', NULL, 8, NULL, '45.00', '0.00', 'amount', '0.00', '45.00', 'cash', 'paid', '45.00', '', 1, 20250476, '2025-06-01 08:22:31', NULL),
(6119, 'INV-00680', NULL, 8, NULL, '45.00', '0.00', 'amount', '0.00', '45.00', 'cash', 'paid', '45.00', '', 1, 20250476, '2025-06-01 08:22:46', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `invoice_items`
--

CREATE TABLE `invoice_items` (
  `id` int(11) NOT NULL,
  `invoice_id` int(11) NOT NULL,
  `item_type` enum('service','product') NOT NULL,
  `item_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `price` decimal(10,2) NOT NULL,
  `discount` decimal(10,2) DEFAULT 0.00,
  `total` decimal(10,2) NOT NULL,
  `employee_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `invoice_items`
--

INSERT INTO `invoice_items` (`id`, `invoice_id`, `item_type`, `item_id`, `quantity`, `price`, `discount`, `total`, `employee_id`) VALUES
(5538, 5384, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5539, 5385, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5540, 5386, 'service', 55, 1, '50.00', '8.00', '42.00', 3),
(5542, 5388, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5543, 5389, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(5544, 5390, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(5546, 5391, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5547, 5392, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(5550, 5394, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5551, 5394, 'service', 55, 1, '50.00', '0.00', '50.00', 1),
(5552, 5395, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5553, 5396, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5558, 5400, 'service', 44, 1, '75.00', '0.00', '75.00', 4),
(5559, 5401, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5560, 5401, 'service', 69, 1, '250.00', '0.00', '250.00', 1),
(5561, 5401, 'service', 56, 1, '85.00', '0.00', '85.00', 1),
(5562, 5402, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(5563, 5403, 'service', 44, 1, '75.00', '0.00', '75.00', 4),
(5564, 5404, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(5565, 5405, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(5566, 5406, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(5567, 5407, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(5568, 5407, 'service', 55, 1, '50.00', '0.00', '50.00', 3),
(5569, 5408, 'service', 48, 1, '45.00', '0.00', '45.00', 3),
(5570, 5409, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5571, 5410, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5572, 5410, 'service', 53, 1, '55.00', '0.00', '55.00', 1),
(5573, 5411, 'service', 55, 1, '50.00', '0.00', '50.00', 3),
(5574, 5412, 'service', 55, 1, '50.00', '0.00', '50.00', 3),
(5575, 5413, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5576, 5413, 'service', 55, 1, '50.00', '0.00', '50.00', 1),
(5577, 5413, 'service', 67, 1, '50.00', '0.00', '50.00', 1),
(5578, 5414, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5579, 5415, 'service', 46, 1, '65.00', '0.00', '65.00', 1),
(5580, 5416, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5581, 5417, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5582, 5418, 'service', 48, 1, '45.00', '0.00', '45.00', 4),
(5583, 5419, 'service', 44, 1, '75.00', '18.60', '56.40', 3),
(5584, 5419, 'service', 58, 1, '35.00', '8.68', '26.32', 3),
(5585, 5420, 'service', 55, 1, '50.00', '0.00', '50.00', 1),
(5586, 5421, 'service', 49, 1, '100.00', '25.00', '75.00', 4),
(5587, 5421, 'service', 55, 1, '50.00', '12.50', '37.50', 4),
(5588, 5422, 'service', 44, 1, '75.00', '18.75', '56.25', 3),
(5589, 5422, 'service', 58, 1, '35.00', '8.75', '26.25', 3),
(5590, 5423, 'service', 44, 1, '75.00', '18.75', '56.25', 3),
(5591, 5424, 'service', 44, 1, '75.00', '18.75', '56.25', 2),
(5592, 5424, 'service', 48, 1, '45.00', '11.25', '33.75', 2),
(5593, 5425, 'service', 56, 1, '85.00', '21.25', '63.75', 1),
(5594, 5425, 'service', 49, 1, '100.00', '25.00', '75.00', 1),
(5595, 5426, 'service', 55, 1, '50.00', '12.50', '37.50', 2),
(5596, 5427, 'service', 44, 1, '75.00', '18.75', '56.25', 3),
(5597, 5427, 'service', 58, 1, '35.00', '8.75', '26.25', 3),
(5598, 5428, 'service', 44, 1, '75.00', '18.75', '56.25', 3),
(5599, 5428, 'service', 58, 1, '35.00', '8.75', '26.25', 3),
(5600, 5429, 'service', 54, 1, '25.00', '4.69', '20.31', 2),
(5601, 5429, 'service', 50, 1, '50.00', '9.38', '40.63', 2),
(5602, 5429, 'service', 67, 1, '50.00', '9.38', '40.63', 2),
(5603, 5430, 'service', 52, 1, '30.00', '7.50', '22.50', 4),
(5604, 5431, 'service', 44, 1, '75.00', '18.71', '56.29', 3),
(5605, 5431, 'service', 63, 1, '80.00', '19.96', '60.04', 3),
(5606, 5431, 'service', 53, 1, '55.00', '13.72', '41.28', 3),
(5607, 5432, 'service', 48, 1, '45.00', '11.25', '33.75', 2),
(5608, 5433, 'service', 63, 1, '80.00', '20.00', '60.00', 4),
(5609, 5433, 'service', 50, 1, '50.00', '12.50', '37.50', 4),
(5610, 5434, 'service', 48, 1, '45.00', '11.25', '33.75', 3),
(5611, 5435, 'service', 48, 1, '45.00', '11.25', '33.75', 2),
(5612, 5436, 'service', 48, 1, '45.00', '11.25', '33.75', 4),
(5613, 5437, 'service', 48, 1, '45.00', '11.25', '33.75', 2),
(5614, 5438, 'service', 50, 1, '50.00', '12.50', '37.50', 3),
(5615, 5439, 'service', 49, 1, '100.00', '16.00', '84.00', 4),
(5620, 5441, 'service', 49, 1, '100.00', '16.00', '84.00', 3),
(5621, 5441, 'service', 53, 1, '55.00', '8.80', '46.20', 3),
(5622, 5442, 'service', 46, 1, '65.00', '10.40', '54.60', 2),
(5623, 5443, 'service', 49, 1, '100.00', '16.00', '84.00', 3),
(5624, 5443, 'service', 67, 1, '50.00', '8.00', '42.00', 3),
(5625, 5444, 'service', 49, 1, '100.00', '16.00', '84.00', 1),
(5626, 5445, 'service', 73, 1, '1550.00', '241.94', '1308.06', 1),
(5628, 5447, 'service', 50, 1, '50.00', '0.00', '50.00', 4),
(5629, 5448, 'service', 59, 1, '545.00', '0.00', '545.00', 1),
(5630, 5449, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5631, 5450, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5632, 5451, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5633, 5452, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5634, 5452, 'service', 53, 1, '55.00', '0.00', '55.00', 3),
(5635, 5452, 'service', 64, 1, '40.00', '0.00', '40.00', 3),
(5636, 5453, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(5637, 5454, 'service', 49, 1, '100.00', '16.00', '84.00', 1),
(5638, 5454, 'service', 53, 1, '55.00', '8.80', '46.20', 1),
(5639, 5454, 'service', 62, 1, '95.00', '15.20', '79.80', 1),
(5640, 5455, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5641, 5456, 'service', 58, 1, '35.00', '5.60', '29.40', 3),
(5642, 5457, 'service', 58, 1, '35.00', '5.60', '29.40', 3),
(5643, 5458, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5644, 5459, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5645, 5460, 'service', 48, 1, '45.00', '0.00', '45.00', 4),
(5646, 5461, 'service', 49, 1, '100.00', '0.00', '100.00', 6),
(5647, 5461, 'service', 59, 1, '545.00', '0.00', '545.00', 6),
(5648, 5461, 'service', 48, 1, '45.00', '0.00', '45.00', 6),
(5649, 5462, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(5650, 5463, 'service', 44, 1, '75.00', '0.00', '75.00', 1),
(5651, 5464, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(5652, 5465, 'service', 44, 1, '75.00', '12.00', '63.00', 3),
(5653, 5465, 'service', 63, 1, '80.00', '12.80', '67.20', 3),
(5654, 5465, 'service', 69, 1, '250.00', '40.00', '210.00', 3),
(5655, 5466, 'service', 44, 1, '75.00', '6.20', '68.80', 3),
(5656, 5466, 'service', 58, 1, '35.00', '2.89', '32.11', 3),
(5657, 5467, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(5658, 5467, 'service', 53, 1, '55.00', '0.00', '55.00', 4),
(5659, 5468, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5660, 5469, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5661, 5470, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(5662, 5471, 'service', 44, 1, '75.00', '0.00', '75.00', 4),
(5663, 5472, 'service', 69, 1, '250.00', '0.00', '250.00', 1),
(5664, 5473, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5665, 5474, 'service', 55, 1, '50.00', '0.00', '50.00', 2),
(5666, 5475, 'service', 49, 1, '100.00', '0.00', '100.00', 6),
(5667, 5476, 'service', 55, 1, '50.00', '0.00', '50.00', 2),
(5668, 5477, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5669, 5478, 'service', 52, 1, '30.00', '0.00', '30.00', 3),
(5670, 5479, 'service', 46, 1, '65.00', '0.00', '65.00', 4),
(5671, 5480, 'service', 49, 1, '100.00', '2.83', '97.17', 1),
(5672, 5480, 'service', 69, 1, '250.00', '7.07', '242.93', 1),
(5673, 5480, 'service', 63, 1, '80.00', '2.26', '77.74', 1),
(5674, 5480, 'service', 56, 1, '85.00', '2.40', '82.60', 1),
(5679, 5484, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5680, 5484, 'service', 55, 1, '50.00', '0.00', '50.00', 1),
(5681, 5485, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5682, 5486, 'service', 52, 1, '30.00', '0.00', '30.00', 4),
(5683, 5487, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5684, 5488, 'service', 49, 1, '100.00', '9.00', '91.00', 2),
(5685, 5488, 'service', 53, 1, '55.00', '4.95', '50.05', 2),
(5686, 5489, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5687, 5489, 'service', 48, 1, '45.00', '0.00', '45.00', 2),
(5688, 5490, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5689, 5491, 'service', 44, 1, '75.00', '0.00', '75.00', 1),
(5690, 5492, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5691, 5493, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(5694, 5496, 'service', 44, 1, '75.00', '0.00', '75.00', 1),
(5698, 5499, 'service', 49, 1, '100.00', '12.68', '87.32', 1),
(5699, 5499, 'service', 63, 1, '80.00', '10.14', '69.86', 1),
(5700, 5499, 'service', 53, 1, '55.00', '6.97', '48.03', 1),
(5712, 5511, 'service', 51, 1, '40.00', '0.00', '40.00', 3),
(5713, 5512, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5717, 5514, 'service', 62, 1, '95.00', '0.00', '95.00', 3),
(5718, 5514, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5719, 5514, 'service', 53, 1, '55.00', '0.00', '55.00', 3),
(5720, 5515, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(5721, 5516, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5722, 5517, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5723, 5517, 'service', 55, 1, '50.00', '0.00', '50.00', 2),
(5724, 5518, 'service', 55, 1, '50.00', '0.00', '50.00', 2),
(5725, 5519, 'service', 44, 1, '75.00', '0.00', '75.00', 4),
(5726, 5520, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5727, 5521, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5728, 5522, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5729, 5523, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5730, 5524, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5733, 5526, 'service', 47, 1, '75.00', '0.00', '75.00', 3),
(5734, 5526, 'service', 47, 1, '75.00', '0.00', '75.00', 3),
(5736, 5528, 'service', 55, 1, '50.00', '0.00', '50.00', 1),
(5737, 5529, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5738, 5529, 'service', 55, 1, '50.00', '0.00', '50.00', 1),
(5739, 5529, 'service', 67, 1, '50.00', '0.00', '50.00', 1),
(5740, 5530, 'service', 58, 1, '35.00', '0.00', '35.00', 1),
(5741, 5530, 'service', 46, 1, '65.00', '0.00', '65.00', 1),
(5742, 5531, 'service', 44, 1, '75.00', '0.00', '75.00', 4),
(5743, 5532, 'service', 44, 1, '75.00', '0.00', '75.00', 4),
(5744, 5533, 'service', 50, 1, '50.00', '0.00', '50.00', 1),
(5745, 5534, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5746, 5535, 'service', 49, 1, '100.00', '0.00', '100.00', 6),
(5747, 5536, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5748, 5537, 'service', 44, 1, '75.00', '0.00', '75.00', 2),
(5749, 5538, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(5750, 5539, 'service', 55, 1, '50.00', '0.00', '50.00', 4),
(5751, 5539, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(5752, 5540, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5753, 5541, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5754, 5542, 'service', 44, 1, '75.00', '0.00', '75.00', 4),
(5755, 5543, 'service', 44, 1, '75.00', '6.82', '68.18', 3),
(5756, 5543, 'service', 58, 1, '35.00', '3.18', '31.82', 3),
(5757, 5544, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5758, 5545, 'service', 55, 1, '50.00', '0.00', '50.00', 3),
(5759, 5546, 'service', 55, 1, '50.00', '0.00', '50.00', 2),
(5760, 5547, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5761, 5548, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(5762, 5549, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(5763, 5550, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(5764, 5551, 'service', 47, 1, '75.00', '0.00', '75.00', 1),
(5765, 5552, 'service', 44, 1, '75.00', '0.00', '75.00', 2),
(5766, 5552, 'service', 58, 1, '35.00', '0.00', '35.00', 2),
(5767, 5553, 'service', 48, 1, '45.00', '0.00', '45.00', 3),
(5769, 5555, 'service', 49, 1, '100.00', '15.00', '85.00', 4),
(5770, 5555, 'service', 55, 1, '50.00', '7.50', '42.50', 4),
(5771, 5555, 'service', 50, 1, '50.00', '7.50', '42.50', 3),
(5772, 5555, 'service', 49, 1, '100.00', '15.00', '85.00', 2),
(5773, 5556, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5774, 5557, 'service', 44, 1, '75.00', '0.00', '75.00', 2),
(5775, 5557, 'service', 44, 1, '75.00', '0.00', '75.00', 1),
(5776, 5558, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(5777, 5559, 'service', 63, 1, '80.00', '0.00', '80.00', 1),
(5778, 5559, 'service', 46, 1, '65.00', '0.00', '65.00', 1),
(5779, 5560, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5780, 5561, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5781, 5562, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5803, 5583, 'service', 46, 1, '65.00', '0.00', '65.00', 3),
(5804, 5584, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(5805, 5585, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5806, 5586, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5807, 5586, 'service', 55, 1, '50.00', '0.00', '50.00', 2),
(5808, 5587, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(5809, 5588, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5810, 5589, 'service', 55, 1, '50.00', '0.00', '50.00', 3),
(5811, 5589, 'service', 50, 1, '50.00', '0.00', '50.00', 3),
(5812, 5590, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5813, 5591, 'service', 44, 1, '75.00', '0.00', '75.00', 1),
(5814, 5592, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5815, 5593, 'service', 58, 1, '35.00', '0.00', '35.00', 2),
(5816, 5593, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5817, 5594, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5818, 5595, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5819, 5596, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(5820, 5597, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5821, 5598, 'service', 51, 1, '40.00', '0.00', '40.00', 3),
(5822, 5599, 'service', 55, 1, '50.00', '0.00', '50.00', 4),
(5823, 5599, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(5824, 5600, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5825, 5601, 'service', 44, 1, '75.00', '0.00', '75.00', 2),
(5826, 5602, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(5827, 5603, 'service', 58, 1, '35.00', '0.00', '35.00', 3),
(5828, 5604, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5829, 5605, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5830, 5605, 'service', 55, 1, '50.00', '0.00', '50.00', 1),
(5831, 5606, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5832, 5607, 'service', 50, 1, '50.00', '0.00', '50.00', 1),
(5833, 5608, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5837, 5612, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5838, 5613, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5839, 5614, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5840, 5615, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5841, 5615, 'service', 67, 1, '50.00', '0.00', '50.00', 2),
(5842, 5616, 'service', 50, 1, '50.00', '0.00', '50.00', 1),
(5843, 5617, 'product', 3, 1, '40.00', '0.00', '40.00', NULL),
(5844, 5618, 'service', 46, 1, '65.00', '0.00', '65.00', 1),
(5845, 5618, 'service', 44, 1, '75.00', '0.00', '75.00', 4),
(5846, 5619, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5847, 5620, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(5848, 5621, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5849, 5622, 'service', 44, 1, '75.00', '0.00', '75.00', 2),
(5850, 5623, 'service', 50, 1, '50.00', '0.00', '50.00', 1),
(5851, 5624, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(5852, 5624, 'service', 71, 1, '30.00', '0.00', '30.00', 3),
(5853, 5625, 'service', 48, 1, '45.00', '0.00', '45.00', 3),
(5854, 5625, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5855, 5626, 'service', 44, 1, '75.00', '0.00', '75.00', 2),
(5856, 5626, 'service', 53, 1, '55.00', '0.00', '55.00', 2),
(5857, 5626, 'service', 48, 1, '45.00', '0.00', '45.00', 2),
(5858, 5627, 'service', 52, 1, '30.00', '0.00', '30.00', 3),
(5859, 5627, 'service', 55, 1, '50.00', '0.00', '50.00', 3),
(5860, 5628, 'service', 44, 1, '75.00', '0.00', '75.00', 1),
(5861, 5628, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(5862, 5629, 'service', 44, 1, '75.00', '0.00', '75.00', 1),
(5863, 5629, 'service', 55, 1, '50.00', '0.00', '50.00', 1),
(5864, 5630, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(5865, 5631, 'service', 58, 1, '35.00', '0.00', '35.00', 3),
(5866, 5631, 'service', 46, 1, '65.00', '0.00', '65.00', 1),
(5867, 5632, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(5868, 5633, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5869, 5633, 'service', 55, 1, '50.00', '0.00', '50.00', 2),
(5870, 5633, 'service', 48, 2, '45.00', '0.00', '90.00', 2),
(5871, 5634, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5872, 5635, 'service', 51, 1, '40.00', '0.00', '40.00', 2),
(5873, 5636, 'service', 52, 1, '30.00', '0.00', '30.00', 3),
(5874, 5637, 'service', 69, 1, '250.00', '0.00', '250.00', 1),
(5875, 5637, 'service', 55, 1, '50.00', '0.00', '50.00', 1),
(5876, 5638, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5877, 5639, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5878, 5640, 'service', 46, 1, '65.00', '0.00', '65.00', 2),
(5879, 5641, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5880, 5642, 'service', 69, 1, '250.00', '25.00', '225.00', 3),
(5881, 5642, 'service', 46, 1, '65.00', '6.50', '58.50', 3),
(5882, 5643, 'service', 44, 1, '75.00', '6.82', '68.18', 3),
(5883, 5643, 'service', 58, 1, '35.00', '3.18', '31.82', 3),
(5884, 5644, 'service', 55, 1, '50.00', '0.00', '50.00', 3),
(5885, 5645, 'service', 55, 1, '50.00', '0.00', '50.00', 2),
(5886, 5645, 'service', 55, 1, '50.00', '0.00', '50.00', 2),
(5887, 5646, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5888, 5647, 'service', 52, 1, '30.00', '0.00', '30.00', 3),
(5889, 5647, 'service', 58, 1, '35.00', '0.00', '35.00', 3),
(5890, 5648, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5891, 5649, 'service', 55, 1, '50.00', '0.00', '50.00', 1),
(5892, 5650, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5893, 5650, 'service', 55, 1, '50.00', '0.00', '50.00', 3),
(5894, 5650, 'service', 53, 1, '55.00', '0.00', '55.00', 3),
(5896, 5652, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5897, 5653, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5898, 5654, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5899, 5655, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5900, 5656, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5901, 5656, 'service', 48, 1, '45.00', '0.00', '45.00', 2),
(5902, 5657, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5903, 5658, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5907, 5660, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5908, 5660, 'service', 55, 1, '50.00', '0.00', '50.00', 2),
(5909, 5660, 'service', 53, 1, '55.00', '0.00', '55.00', 2),
(5910, 5661, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5911, 5662, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5912, 5663, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5913, 5664, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(5914, 5665, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(5915, 5666, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5916, 5667, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(5917, 5668, 'service', 53, 1, '55.00', '0.00', '55.00', 3),
(5918, 5668, 'service', 50, 1, '50.00', '0.00', '50.00', 3),
(5919, 5669, 'service', 44, 1, '75.00', '0.00', '75.00', 4),
(5921, 5671, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5922, 5672, 'service', 52, 1, '30.00', '0.00', '30.00', 3),
(5923, 5673, 'service', 44, 1, '75.00', '0.00', '75.00', 4),
(5924, 5674, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5925, 5675, 'service', 51, 1, '40.00', '0.00', '40.00', 2),
(5926, 5676, 'service', 51, 1, '40.00', '0.00', '40.00', 2),
(5927, 5677, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5928, 5678, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5929, 5679, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5930, 5680, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5931, 5681, 'service', 48, 1, '45.00', '0.00', '45.00', 1),
(5932, 5681, 'service', 48, 1, '45.00', '0.00', '45.00', 4),
(5933, 5682, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5934, 5682, 'service', 55, 1, '50.00', '0.00', '50.00', 3),
(5935, 5683, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(5936, 5684, 'service', 55, 1, '50.00', '0.00', '50.00', 4),
(5937, 5685, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5938, 5685, 'service', 53, 1, '55.00', '0.00', '55.00', 3),
(5939, 5685, 'service', 64, 1, '40.00', '0.00', '40.00', 3),
(5940, 5686, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5942, 5688, 'service', 48, 1, '45.00', '0.00', '45.00', 4),
(5943, 5689, 'service', 52, 1, '30.00', '0.00', '30.00', 3),
(5944, 5690, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5945, 5690, 'service', 55, 1, '50.00', '0.00', '50.00', 1),
(5946, 5691, 'service', 48, 1, '45.00', '0.00', '45.00', 2),
(5947, 5692, 'service', 55, 1, '50.00', '0.00', '50.00', 4),
(5948, 5693, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5949, 5694, 'service', 51, 1, '40.00', '0.00', '40.00', 4),
(5950, 5695, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5951, 5695, 'service', 53, 1, '55.00', '0.00', '55.00', 4),
(5952, 5696, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(5953, 5696, 'service', 55, 1, '50.00', '0.00', '50.00', 3),
(5954, 5697, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5955, 5698, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5956, 5698, 'service', 55, 1, '50.00', '0.00', '50.00', 2),
(5957, 5699, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5958, 5700, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5959, 5701, 'service', 43, 1, '75.00', '0.00', '75.00', 2),
(5960, 5702, 'service', 50, 1, '50.00', '0.00', '50.00', 3),
(5961, 5703, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(5962, 5703, 'service', 55, 1, '50.00', '0.00', '50.00', 4),
(5963, 5704, 'service', 53, 1, '55.00', '0.00', '55.00', 1),
(5964, 5704, 'service', 55, 1, '50.00', '0.00', '50.00', 1),
(5965, 5704, 'service', 50, 1, '50.00', '0.00', '50.00', 1),
(5966, 5705, 'service', 55, 1, '50.00', '0.00', '50.00', 1),
(5967, 5705, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5968, 5705, 'service', 67, 1, '50.00', '0.00', '50.00', 1),
(5969, 5706, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5970, 5707, 'service', 50, 1, '50.00', '0.00', '50.00', 4),
(5971, 5708, 'service', 44, 1, '75.00', '0.00', '75.00', 1),
(5972, 5709, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(5973, 5710, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(5974, 5711, 'service', 44, 1, '75.00', '0.00', '75.00', 1),
(5975, 5712, 'service', 50, 1, '50.00', '0.00', '50.00', 4),
(5976, 5713, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5977, 5714, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5978, 5715, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(5979, 5715, 'service', 63, 1, '80.00', '0.00', '80.00', 4),
(5980, 5716, 'service', 44, 1, '75.00', '0.00', '75.00', 1),
(5981, 5716, 'service', 63, 1, '80.00', '0.00', '80.00', 1),
(5982, 5717, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(5983, 5717, 'service', 63, 1, '80.00', '0.00', '80.00', 4),
(5984, 5718, 'service', 56, 1, '85.00', '0.00', '85.00', 4),
(5986, 5720, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5987, 5721, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(5988, 5722, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5989, 5723, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(5990, 5723, 'service', 67, 1, '50.00', '0.00', '50.00', 1),
(5991, 5723, 'service', 55, 1, '50.00', '0.00', '50.00', 1),
(5992, 5724, 'service', 51, 1, '40.00', '0.00', '40.00', 3),
(5993, 5725, 'service', 50, 1, '50.00', '0.00', '50.00', 4),
(5994, 5726, 'service', 50, 1, '50.00', '0.00', '50.00', 4),
(5995, 5727, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(5996, 5728, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(5997, 5729, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(5998, 5730, 'service', 49, 1, '100.00', '0.00', '100.00', 14),
(5999, 5730, 'service', 44, 1, '75.00', '0.00', '75.00', 14),
(6000, 5730, 'service', 44, 1, '75.00', '0.00', '75.00', 14),
(6001, 5730, 'service', 44, 1, '75.00', '0.00', '75.00', 14),
(6002, 5730, 'service', 44, 1, '75.00', '0.00', '75.00', 14),
(6003, 5731, 'service', 50, 1, '50.00', '0.00', '50.00', 14),
(6004, 5731, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6005, 5731, 'service', 53, 1, '55.00', '0.00', '55.00', 1),
(6006, 5732, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6007, 5733, 'service', 48, 1, '45.00', '0.00', '45.00', 2),
(6008, 5733, 'service', 48, 1, '45.00', '0.00', '45.00', 2),
(6010, 5735, 'service', 46, 1, '65.00', '0.00', '65.00', 14),
(6011, 5735, 'service', 46, 1, '65.00', '0.00', '65.00', 4),
(6012, 5736, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6013, 5737, 'service', 48, 1, '45.00', '0.00', '45.00', 4),
(6014, 5738, 'service', 48, 1, '45.00', '0.00', '45.00', 14),
(6015, 5739, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6016, 5740, 'service', 46, 1, '65.00', '0.00', '65.00', 2),
(6017, 5740, 'service', 58, 1, '35.00', '0.00', '35.00', 2),
(6018, 5741, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6019, 5741, 'service', 53, 1, '55.00', '0.00', '55.00', 1),
(6020, 5741, 'service', 67, 1, '50.00', '0.00', '50.00', 1),
(6021, 5742, 'service', 43, 1, '75.00', '0.00', '75.00', 2),
(6022, 5743, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6023, 5744, 'service', 49, 1, '100.00', '0.00', '100.00', 14),
(6024, 5745, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6025, 5746, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(6026, 5747, 'service', 67, 1, '50.00', '0.00', '50.00', 2),
(6027, 5747, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6028, 5748, 'service', 48, 1, '45.00', '0.00', '45.00', 14),
(6029, 5749, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6030, 5750, 'service', 47, 1, '75.00', '0.00', '75.00', 14),
(6031, 5751, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6032, 5752, 'service', 46, 1, '65.00', '0.00', '65.00', 2),
(6033, 5753, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6034, 5754, 'service', 46, 1, '65.00', '0.00', '65.00', 2),
(6035, 5755, 'service', 48, 1, '45.00', '0.00', '45.00', 14),
(6036, 5756, 'service', 49, 1, '100.00', '0.00', '100.00', 14),
(6037, 5757, 'service', 44, 1, '75.00', '0.00', '75.00', 1),
(6038, 5758, 'service', 55, 1, '50.00', '0.00', '50.00', 3),
(6039, 5758, 'service', 51, 1, '40.00', '0.00', '40.00', 3),
(6040, 5759, 'service', 55, 1, '50.00', '0.00', '50.00', 3),
(6041, 5759, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6042, 5760, 'service', 51, 1, '40.00', '0.00', '40.00', 4),
(6043, 5760, 'service', 58, 1, '35.00', '0.00', '35.00', 4),
(6044, 5761, 'service', 51, 1, '40.00', '0.00', '40.00', 2),
(6045, 5762, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6046, 5763, 'service', 49, 1, '100.00', '0.00', '100.00', 14),
(6047, 5764, 'service', 48, 1, '45.00', '0.00', '45.00', 3),
(6048, 5765, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6049, 5766, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6050, 5767, 'service', 51, 1, '40.00', '0.00', '40.00', 2),
(6051, 5768, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6052, 5769, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6053, 5770, 'service', 50, 1, '50.00', '0.00', '50.00', 4),
(6054, 5771, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6055, 5772, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6056, 5773, 'service', 51, 1, '40.00', '0.00', '40.00', 3),
(6057, 5774, 'service', 51, 1, '40.00', '0.00', '40.00', 3),
(6058, 5775, 'service', 49, 1, '100.00', '0.00', '100.00', 14),
(6059, 5776, 'service', 52, 1, '30.00', '0.00', '30.00', 2),
(6060, 5776, 'service', 55, 1, '50.00', '0.00', '50.00', 2),
(6061, 5777, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6062, 5777, 'service', 55, 1, '50.00', '0.00', '50.00', 1),
(6063, 5777, 'service', 47, 1, '75.00', '0.00', '75.00', 3),
(6064, 5777, 'service', 69, 1, '250.00', '0.00', '250.00', 4),
(6065, 5777, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6066, 5778, 'service', 49, 1, '100.00', '0.00', '100.00', 14),
(6067, 5778, 'service', 48, 1, '45.00', '0.00', '45.00', 3),
(6068, 5778, 'service', 48, 1, '45.00', '0.00', '45.00', 4),
(6069, 5779, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6070, 5779, 'service', 53, 1, '55.00', '0.00', '55.00', 1),
(6071, 5780, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6072, 5781, 'service', 45, 1, '55.00', '0.00', '55.00', 3),
(6073, 5782, 'service', 48, 1, '45.00', '0.00', '45.00', 14),
(6074, 5782, 'service', 48, 1, '45.00', '0.00', '45.00', 1),
(6075, 5782, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6076, 5783, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6077, 5783, 'service', 55, 1, '50.00', '0.00', '50.00', 3),
(6078, 5784, 'service', 55, 1, '50.00', '0.00', '50.00', 2),
(6079, 5785, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6080, 5785, 'service', 53, 1, '55.00', '0.00', '55.00', 1),
(6081, 5786, 'service', 49, 1, '100.00', '0.00', '100.00', 14),
(6082, 5787, 'service', 43, 1, '75.00', '0.00', '75.00', 1),
(6085, 5789, 'service', 44, 1, '75.00', '0.00', '75.00', 4),
(6086, 5789, 'service', 58, 1, '35.00', '0.00', '35.00', 4),
(6087, 5790, 'service', 50, 1, '50.00', '0.00', '50.00', 14),
(6088, 5791, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6089, 5792, 'service', 50, 1, '50.00', '0.00', '50.00', 3),
(6090, 5793, 'service', 46, 1, '65.00', '0.00', '65.00', 1),
(6091, 5794, 'service', 49, 1, '100.00', '0.00', '100.00', 6),
(6092, 5794, 'service', 63, 1, '80.00', '0.00', '80.00', 2),
(6093, 5795, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6094, 5796, 'service', 52, 1, '30.00', '0.00', '30.00', 3),
(6095, 5797, 'service', 43, 1, '75.00', '0.00', '75.00', 2),
(6096, 5797, 'service', 43, 1, '75.00', '0.00', '75.00', 4),
(6097, 5798, 'service', 49, 1, '100.00', '0.00', '100.00', 14),
(6098, 5799, 'product', 96, 1, '200.00', '0.00', '200.00', NULL),
(6099, 5800, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6100, 5800, 'service', 53, 1, '55.00', '0.00', '55.00', 4),
(6101, 5801, 'service', 49, 1, '100.00', '0.00', '100.00', 14),
(6102, 5802, 'service', 69, 1, '250.00', '0.00', '250.00', 1),
(6103, 5802, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6104, 5802, 'service', 56, 1, '85.00', '0.00', '85.00', 1),
(6105, 5802, 'service', 55, 1, '50.00', '0.00', '50.00', 1),
(6106, 5803, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6107, 5804, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6108, 5804, 'service', 48, 1, '45.00', '0.00', '45.00', 2),
(6109, 5805, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6110, 5806, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6111, 5807, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6112, 5807, 'service', 55, 1, '50.00', '0.00', '50.00', 1),
(6113, 5808, 'service', 44, 1, '75.00', '0.00', '75.00', 14),
(6114, 5809, 'service', 44, 1, '75.00', '0.00', '75.00', 14),
(6115, 5810, 'service', 44, 1, '75.00', '0.00', '75.00', 1),
(6116, 5811, 'service', 44, 1, '75.00', '0.00', '75.00', 2),
(6117, 5812, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6118, 5813, 'service', 44, 1, '75.00', '0.00', '75.00', 4),
(6119, 5814, 'service', 44, 1, '75.00', '0.00', '75.00', 14),
(6120, 5815, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6121, 5816, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(6123, 5818, 'service', 56, 1, '85.00', '0.00', '85.00', 4),
(6124, 5818, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6125, 5819, 'service', 69, 1, '250.00', '0.00', '250.00', 2),
(6126, 5819, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6127, 5819, 'service', 55, 1, '50.00', '0.00', '50.00', 2),
(6128, 5820, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6129, 5821, 'service', 49, 1, '100.00', '0.00', '100.00', 14),
(6130, 5822, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6131, 5823, 'service', 48, 1, '45.00', '0.00', '45.00', 3),
(6132, 5823, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(6133, 5824, 'service', 55, 1, '50.00', '0.00', '50.00', 4),
(6134, 5824, 'service', 44, 1, '75.00', '0.00', '75.00', 4),
(6135, 5825, 'service', 51, 1, '40.00', '0.00', '40.00', 2),
(6136, 5826, 'service', 44, 1, '75.00', '0.00', '75.00', 14),
(6137, 5827, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6138, 5827, 'service', 67, 1, '50.00', '0.00', '50.00', 1),
(6139, 5828, 'service', 55, 1, '50.00', '0.00', '50.00', 1),
(6140, 5829, 'service', 55, 1, '50.00', '0.00', '50.00', 1),
(6142, 5831, 'service', 44, 1, '75.00', '0.00', '75.00', 1),
(6143, 5832, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6144, 5833, 'product', 28, 1, '50.00', '0.00', '50.00', NULL),
(6145, 5834, 'service', 44, 1, '75.00', '0.00', '75.00', 4),
(6146, 5835, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6147, 5835, 'service', 53, 1, '55.00', '0.00', '55.00', 2),
(6148, 5836, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6149, 5837, 'service', 43, 1, '75.00', '0.00', '75.00', 4),
(6150, 5838, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6151, 5838, 'service', 71, 1, '30.00', '0.00', '30.00', 3),
(6152, 5839, 'service', 51, 1, '40.00', '0.00', '40.00', 2),
(6153, 5840, 'service', 43, 1, '75.00', '0.00', '75.00', 14),
(6154, 5841, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(6155, 5842, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6156, 5842, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6157, 5843, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6158, 5844, 'service', 49, 1, '100.00', '0.00', '100.00', 14),
(6159, 5845, 'service', 48, 1, '45.00', '0.00', '45.00', 2),
(6160, 5846, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(6161, 5846, 'service', 56, 1, '85.00', '0.00', '85.00', 3),
(6162, 5847, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6163, 5848, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6164, 5849, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6165, 5850, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6166, 5851, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(6167, 5852, 'service', 50, 1, '50.00', '0.00', '50.00', 4),
(6168, 5853, 'service', 49, 1, '100.00', '0.00', '100.00', 14),
(6169, 5854, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6170, 5855, 'service', 44, 1, '75.00', '0.00', '75.00', 4),
(6171, 5855, 'service', 58, 1, '35.00', '0.00', '35.00', 4),
(6172, 5856, 'service', 44, 1, '75.00', '0.00', '75.00', 2),
(6173, 5857, 'service', 44, 1, '75.00', '0.00', '75.00', 14),
(6174, 5858, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6175, 5858, 'service', 53, 1, '55.00', '0.00', '55.00', 3),
(6176, 5858, 'service', 48, 1, '45.00', '0.00', '45.00', 2),
(6177, 5858, 'service', 48, 1, '45.00', '0.00', '45.00', 3),
(6178, 5859, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6179, 5859, 'service', 69, 1, '250.00', '0.00', '250.00', 4),
(6180, 5860, 'service', 44, 1, '75.00', '0.00', '75.00', 2),
(6181, 5860, 'service', 44, 1, '75.00', '0.00', '75.00', 2),
(6182, 5860, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6183, 5861, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6184, 5861, 'service', 58, 1, '35.00', '0.00', '35.00', 4),
(6185, 5862, 'service', 44, 1, '75.00', '0.00', '75.00', 14),
(6186, 5862, 'service', 58, 1, '35.00', '0.00', '35.00', 14),
(6187, 5863, 'service', 51, 1, '40.00', '0.00', '40.00', 14),
(6188, 5864, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6189, 5865, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6190, 5865, 'service', 48, 1, '45.00', '0.00', '45.00', 3),
(6191, 5865, 'service', 48, 1, '45.00', '0.00', '45.00', 3),
(6192, 5866, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(6193, 5867, 'service', 44, 1, '75.00', '0.00', '75.00', 2),
(6194, 5868, 'service', 50, 1, '50.00', '0.00', '50.00', 14),
(6195, 5869, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6196, 5869, 'service', 55, 1, '50.00', '0.00', '50.00', 3),
(6197, 5869, 'service', 64, 1, '40.00', '0.00', '40.00', 3),
(6198, 5870, 'service', 50, 1, '50.00', '0.00', '50.00', 2),
(6199, 5870, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6200, 5870, 'service', 55, 1, '50.00', '0.00', '50.00', 1),
(6201, 5870, 'service', 55, 1, '50.00', '0.00', '50.00', 1),
(6202, 5871, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(6203, 5872, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6204, 5873, 'service', 51, 1, '40.00', '0.00', '40.00', 3),
(6205, 5874, 'service', 51, 1, '40.00', '0.00', '40.00', 1),
(6206, 5875, 'service', 44, 1, '75.00', '0.00', '75.00', 4),
(6207, 5875, 'service', 63, 1, '80.00', '0.00', '80.00', 4),
(6208, 5876, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6209, 5877, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6210, 5878, 'service', 44, 1, '75.00', '0.00', '75.00', 2),
(6211, 5879, 'service', 50, 1, '50.00', '0.00', '50.00', 3),
(6212, 5880, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6213, 5881, 'service', 48, 1, '45.00', '0.00', '45.00', 2),
(6214, 5882, 'service', 58, 1, '35.00', '0.00', '35.00', 4),
(6215, 5883, 'service', 48, 1, '45.00', '0.00', '45.00', 1),
(6216, 5883, 'service', 48, 1, '45.00', '0.00', '45.00', 2),
(6217, 5884, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6218, 5885, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(6219, 5886, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6220, 5886, 'service', 48, 1, '45.00', '0.00', '45.00', 14),
(6221, 5887, 'service', 58, 1, '35.00', '0.00', '35.00', 4),
(6222, 5887, 'service', 63, 1, '80.00', '0.00', '80.00', 4),
(6223, 5887, 'service', 51, 1, '40.00', '0.00', '40.00', 4),
(6224, 5888, 'service', 44, 1, '75.00', '0.00', '75.00', 14),
(6225, 5888, 'service', 63, 1, '80.00', '0.00', '80.00', 3),
(6226, 5889, 'service', 46, 1, '65.00', '0.00', '65.00', 3),
(6227, 5889, 'service', 58, 1, '35.00', '0.00', '35.00', 3),
(6228, 5890, 'service', 50, 1, '50.00', '0.00', '50.00', 4),
(6229, 5891, 'service', 44, 1, '75.00', '0.00', '75.00', 14),
(6230, 5891, 'service', 55, 1, '50.00', '0.00', '50.00', 14),
(6231, 5892, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6232, 5893, 'service', 50, 1, '50.00', '0.00', '50.00', 3),
(6233, 5894, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6234, 5895, 'service', 51, 1, '40.00', '0.00', '40.00', 1),
(6235, 5896, 'service', 56, 1, '85.00', '0.00', '85.00', 14),
(6236, 5897, 'service', 52, 1, '30.00', '0.00', '30.00', 4),
(6237, 5898, 'service', 44, 1, '75.00', '0.00', '75.00', 2),
(6238, 5898, 'service', 55, 1, '50.00', '0.00', '50.00', 2),
(6239, 5899, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6240, 5900, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6241, 5900, 'service', 53, 1, '55.00', '0.00', '55.00', 1),
(6242, 5900, 'service', 55, 1, '50.00', '0.00', '50.00', 1),
(6243, 5901, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6244, 5902, 'service', 51, 1, '40.00', '0.00', '40.00', 1),
(6245, 5903, 'service', 48, 1, '45.00', '0.00', '45.00', 4),
(6246, 5904, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(6247, 5905, 'service', 49, 1, '100.00', '0.00', '100.00', 14),
(6248, 5906, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6250, 5907, 'service', 44, 1, '75.00', '0.00', '75.00', 2),
(6251, 5908, 'service', 55, 1, '50.00', '0.00', '50.00', 14),
(6252, 5909, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6253, 5910, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6254, 5910, 'service', 69, 1, '250.00', '0.00', '250.00', 4),
(6255, 5911, 'service', 50, 1, '50.00', '0.00', '50.00', 2),
(6256, 5912, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6257, 5913, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6258, 5914, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6259, 5915, 'service', 49, 1, '100.00', '0.00', '100.00', 14),
(6260, 5916, 'service', 44, 1, '75.00', '0.00', '75.00', 1),
(6261, 5917, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6262, 5917, 'service', 53, 1, '55.00', '0.00', '55.00', 4),
(6264, 5919, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6265, 5919, 'service', 69, 1, '250.00', '0.00', '250.00', 1),
(6266, 5919, 'service', 55, 1, '50.00', '0.00', '50.00', 1),
(6270, 5920, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(6271, 5921, 'service', 55, 1, '50.00', '0.00', '50.00', 14),
(6274, 5923, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6275, 5923, 'service', 58, 1, '35.00', '0.00', '35.00', 4),
(6276, 5924, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6277, 5925, 'service', 55, 1, '50.00', '0.00', '50.00', 1),
(6278, 5925, 'service', 50, 1, '50.00', '0.00', '50.00', 2),
(6279, 5926, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6280, 5927, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6281, 5928, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6282, 5928, 'service', 55, 1, '50.00', '0.00', '50.00', 2),
(6283, 5929, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6284, 5929, 'service', 56, 1, '85.00', '0.00', '85.00', 2),
(6285, 5930, 'service', 44, 1, '75.00', '0.00', '75.00', 14),
(6286, 5931, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6287, 5932, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6288, 5933, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6289, 5934, 'service', 49, 1, '100.00', '0.00', '100.00', 14),
(6290, 5935, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6291, 5936, 'service', 48, 1, '45.00', '0.00', '45.00', 3),
(6292, 5937, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6296, 5939, 'service', 48, 1, '45.00', '0.00', '45.00', 14),
(6297, 5938, 'service', 44, 1, '75.00', '9.82', '65.18', 4),
(6298, 5938, 'service', 62, 1, '95.00', '12.44', '82.56', 4),
(6299, 5938, 'service', 69, 1, '250.00', '32.74', '217.26', 4),
(6300, 5940, 'service', 56, 1, '85.00', '0.00', '85.00', 1),
(6301, 5941, 'service', 49, 1, '100.00', '0.00', '100.00', 14),
(6302, 5942, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6303, 5943, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6304, 5943, 'service', 63, 1, '80.00', '0.00', '80.00', 3),
(6307, 5944, 'service', 49, 1, '100.00', '0.00', '100.00', 14),
(6308, 5945, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6309, 5946, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(6310, 5947, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(6311, 5947, 'service', 58, 1, '35.00', '0.00', '35.00', 3),
(6312, 5948, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6313, 5948, 'service', 69, 1, '250.00', '0.00', '250.00', 1),
(6314, 5949, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(6315, 5950, 'service', 44, 1, '75.00', '0.00', '75.00', 4),
(6316, 5951, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6317, 5952, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6318, 5953, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6319, 5954, 'service', 44, 1, '75.00', '0.00', '75.00', 1),
(6320, 5955, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(6321, 5956, 'service', 52, 1, '30.00', '0.00', '30.00', 2),
(6322, 5957, 'service', 58, 1, '35.00', '0.00', '35.00', 2),
(6323, 5957, 'service', 44, 1, '75.00', '0.00', '75.00', 2),
(6325, 5959, 'service', 49, 1, '100.00', '0.00', '100.00', 14),
(6326, 5960, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6327, 5960, 'service', 67, 1, '50.00', '0.00', '50.00', 1),
(6328, 5961, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6329, 5962, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6330, 5962, 'service', 48, 1, '45.00', '0.00', '45.00', 2),
(6331, 5962, 'service', 48, 1, '45.00', '0.00', '45.00', 2),
(6332, 5963, 'service', 46, 1, '65.00', '0.00', '65.00', 1),
(6333, 5964, 'service', 44, 1, '75.00', '0.00', '75.00', 14),
(6335, 5966, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6336, 5967, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6337, 5968, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6339, 5969, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6340, 5970, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6341, 5971, 'service', 49, 1, '100.00', '0.00', '100.00', 14),
(6342, 5971, 'service', 69, 1, '250.00', '0.00', '250.00', 14),
(6343, 5972, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6344, 5973, 'service', 52, 1, '30.00', '0.00', '30.00', 3),
(6345, 5974, 'service', 49, 1, '100.00', '0.00', '100.00', 6),
(6346, 5975, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(6350, 5965, 'service', 44, 1, '75.00', '0.00', '75.00', 4),
(6351, 5976, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6352, 5977, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(6353, 5978, 'service', 44, 1, '75.00', '0.00', '75.00', 14),
(6354, 5979, 'service', 44, 1, '75.00', '0.00', '75.00', 14),
(6355, 5980, 'service', 51, 1, '40.00', '0.00', '40.00', 4),
(6356, 5981, 'service', 52, 1, '30.00', '0.00', '30.00', 2),
(6357, 5982, 'service', 49, 1, '100.00', '0.00', '100.00', 14),
(6358, 5982, 'service', 48, 1, '45.00', '0.00', '45.00', 14),
(6359, 5982, 'service', 48, 1, '45.00', '0.00', '45.00', 3),
(6360, 5983, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6361, 5984, 'service', 49, 1, '100.00', '0.00', '100.00', 14),
(6362, 5985, 'service', 44, 1, '75.00', '0.00', '75.00', 14),
(6363, 5986, 'service', 63, 1, '80.00', '0.00', '80.00', 3),
(6364, 5986, 'service', 51, 1, '40.00', '0.00', '40.00', 3),
(6365, 5987, 'service', 46, 1, '65.00', '0.00', '65.00', 4),
(6366, 5988, 'service', 55, 1, '50.00', '0.00', '50.00', 4),
(6367, 5989, 'service', 53, 1, '55.00', '0.00', '55.00', 3),
(6368, 5989, 'service', 50, 1, '50.00', '0.00', '50.00', 3),
(6369, 5990, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6370, 5991, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6371, 5991, 'service', 53, 1, '55.00', '0.00', '55.00', 3),
(6372, 5992, 'service', 55, 1, '50.00', '0.00', '50.00', 14),
(6373, 5992, 'service', 50, 1, '50.00', '0.00', '50.00', 14),
(6375, 5993, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6376, 5994, 'service', 50, 1, '50.00', '0.00', '50.00', 4),
(6377, 5994, 'service', 55, 1, '50.00', '0.00', '50.00', 4),
(6378, 5995, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6379, 5996, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6381, 5998, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6382, 5999, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6383, 6000, 'service', 55, 1, '50.00', '0.00', '50.00', 2),
(6384, 6000, 'service', 55, 1, '50.00', '0.00', '50.00', 2),
(6385, 6000, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6389, 6002, 'service', 49, 1, '100.00', '0.00', '100.00', 14),
(6390, 6002, 'service', 48, 1, '45.00', '0.00', '45.00', 14),
(6391, 6002, 'service', 48, 1, '45.00', '0.00', '45.00', 14),
(6395, 6006, 'service', 44, 1, '75.00', '0.00', '75.00', 4),
(6396, 6007, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(6397, 6008, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6398, 6009, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6399, 6010, 'service', 56, 1, '85.00', '0.00', '85.00', 4),
(6400, 6010, 'service', 52, 1, '30.00', '0.00', '30.00', 4),
(6401, 6011, 'service', 44, 1, '75.00', '0.00', '75.00', 2),
(6402, 6012, 'service', 49, 1, '100.00', '0.00', '100.00', 14),
(6403, 6013, 'service', 55, 1, '50.00', '0.00', '50.00', 1),
(6404, 6013, 'service', 67, 1, '50.00', '0.00', '50.00', 1),
(6405, 6013, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6406, 6014, 'service', 48, 1, '45.00', '0.00', '45.00', 1),
(6407, 6014, 'service', 45, 1, '55.00', '0.00', '55.00', 1),
(6408, 6015, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6409, 6015, 'service', 55, 1, '50.00', '0.00', '50.00', 3),
(6410, 6016, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6411, 6017, 'service', 67, 1, '50.00', '0.00', '50.00', 2),
(6412, 6017, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6413, 6018, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6414, 6019, 'service', 48, 1, '45.00', '0.00', '45.00', 14),
(6415, 6019, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6416, 6020, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6417, 6021, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6418, 6022, 'service', 49, 1, '100.00', '0.00', '100.00', 14),
(6419, 6023, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6420, 6024, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6421, 6024, 'service', 53, 1, '55.00', '0.00', '55.00', 1),
(6422, 6025, 'service', 46, 1, '65.00', '0.00', '65.00', 3),
(6423, 6026, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6424, 6026, 'service', 55, 1, '50.00', '0.00', '50.00', 3),
(6425, 6027, 'service', 53, 1, '55.00', '0.00', '55.00', 1),
(6426, 6027, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6427, 6028, 'service', 59, 1, '545.00', '0.00', '545.00', 2),
(6428, 6028, 'service', 45, 1, '55.00', '0.00', '55.00', 2),
(6429, 6029, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6430, 6029, 'service', 53, 1, '55.00', '0.00', '55.00', 3),
(6431, 6030, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6432, 6031, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6433, 6032, 'service', 63, 1, '80.00', '0.00', '80.00', 14),
(6434, 6032, 'service', 51, 1, '40.00', '0.00', '40.00', 14),
(6435, 6033, 'service', 51, 1, '40.00', '0.00', '40.00', 2),
(6436, 6034, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6437, 6035, 'service', 43, 1, '75.00', '0.00', '75.00', 1),
(6438, 6036, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6439, 6037, 'service', 44, 1, '75.00', '0.00', '75.00', 2),
(6440, 6038, 'service', 46, 1, '65.00', '0.00', '65.00', 1),
(6441, 6039, 'service', 48, 1, '45.00', '0.00', '45.00', 14),
(6442, 6040, 'service', 67, 1, '50.00', '0.00', '50.00', 14),
(6443, 6041, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6444, 6041, 'service', 67, 1, '50.00', '0.00', '50.00', 2),
(6445, 6041, 'service', 53, 1, '55.00', '0.00', '55.00', 2),
(6446, 6042, 'service', 44, 1, '75.00', '0.00', '75.00', 2),
(6447, 6043, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6448, 6044, 'service', 52, 1, '30.00', '0.00', '30.00', 2),
(6449, 6045, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6450, 6046, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(6451, 6047, 'service', 50, 1, '50.00', '0.00', '50.00', 1),
(6452, 6047, 'service', 50, 1, '50.00', '0.00', '50.00', 4),
(6453, 6048, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6454, 6049, 'service', 49, 1, '100.00', '0.00', '100.00', 14),
(6455, 6050, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6456, 6051, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6457, 6051, 'service', 55, 1, '50.00', '0.00', '50.00', 1),
(6458, 6052, 'service', 44, 1, '75.00', '0.00', '75.00', 14),
(6459, 6053, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6460, 6054, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6461, 6055, 'service', 44, 1, '75.00', '0.00', '75.00', 1),
(6462, 6056, 'service', 44, 1, '75.00', '0.00', '75.00', 1),
(6463, 6057, 'service', 44, 1, '75.00', '0.00', '75.00', 1),
(6464, 6058, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(6465, 6059, 'service', 51, 1, '40.00', '0.00', '40.00', 1),
(6466, 6060, 'service', 44, 1, '75.00', '0.00', '75.00', 4),
(6467, 6061, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6468, 6062, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6469, 6063, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(6470, 6063, 'service', 71, 1, '30.00', '0.00', '30.00', 3);
INSERT INTO `invoice_items` (`id`, `invoice_id`, `item_type`, `item_id`, `quantity`, `price`, `discount`, `total`, `employee_id`) VALUES
(6471, 6063, 'service', 63, 1, '80.00', '0.00', '80.00', 3),
(6472, 6064, 'service', 44, 1, '75.00', '0.00', '75.00', 2),
(6473, 6065, 'service', 44, 1, '75.00', '0.00', '75.00', 2),
(6474, 6066, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6475, 6067, 'service', 49, 1, '100.00', '0.00', '100.00', 14),
(6476, 6068, 'service', 44, 1, '75.00', '0.00', '75.00', 14),
(6477, 6069, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6478, 6070, 'service', 49, 1, '100.00', '0.00', '100.00', 14),
(6479, 6071, 'service', 63, 1, '80.00', '0.00', '80.00', 2),
(6480, 6071, 'service', 53, 1, '55.00', '0.00', '55.00', 2),
(6481, 6071, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6482, 6072, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6483, 6073, 'service', 46, 1, '65.00', '0.00', '65.00', 2),
(6484, 6074, 'service', 52, 1, '30.00', '0.00', '30.00', 2),
(6485, 6075, 'service', 50, 1, '50.00', '0.00', '50.00', 1),
(6487, 6077, 'service', 72, 1, '1150.00', '150.00', '1000.00', 1),
(6488, 6078, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6489, 6079, 'service', 44, 1, '75.00', '0.00', '75.00', 1),
(6490, 6080, 'service', 51, 1, '40.00', '0.00', '40.00', 14),
(6491, 6081, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(6492, 6081, 'service', 55, 1, '50.00', '0.00', '50.00', 3),
(6494, 6083, 'service', 49, 1, '100.00', '0.00', '100.00', 14),
(6496, 6085, 'service', 48, 1, '45.00', '0.00', '45.00', 3),
(6497, 6086, 'service', 50, 1, '50.00', '0.00', '50.00', 3),
(6498, 6086, 'service', 53, 1, '55.00', '0.00', '55.00', 3),
(6499, 6087, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6500, 6088, 'service', 52, 1, '30.00', '0.00', '30.00', 3),
(6501, 6089, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6502, 6089, 'service', 48, 1, '45.00', '0.00', '45.00', 1),
(6503, 6089, 'service', 67, 1, '50.00', '0.00', '50.00', 1),
(6504, 6090, 'service', 44, 1, '75.00', '0.00', '75.00', 14),
(6505, 6091, 'service', 48, 1, '45.00', '0.00', '45.00', 4),
(6507, 6093, 'service', 44, 1, '75.00', '0.00', '75.00', 3),
(6508, 6092, 'service', 50, 1, '50.00', '0.00', '50.00', 3),
(6509, 6094, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6512, 6096, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6513, 6097, 'service', 44, 1, '75.00', '0.00', '75.00', 14),
(6514, 6098, 'service', 44, 1, '75.00', '0.00', '75.00', 2),
(6515, 6099, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6516, 6100, 'service', 51, 1, '40.00', '0.00', '40.00', 3),
(6517, 6101, 'service', 48, 1, '45.00', '0.00', '45.00', 2),
(6518, 6101, 'service', 48, 1, '45.00', '0.00', '45.00', 2),
(6519, 6102, 'service', 48, 1, '45.00', '0.00', '45.00', 2),
(6520, 6103, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6521, 6104, 'service', 46, 1, '65.00', '0.00', '65.00', 1),
(6522, 6105, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6523, 6105, 'service', 53, 1, '55.00', '0.00', '55.00', 2),
(6524, 6105, 'service', 62, 1, '95.00', '0.00', '95.00', 2),
(6525, 6106, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6526, 6107, 'service', 49, 1, '100.00', '0.00', '100.00', 1),
(6527, 6108, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6528, 6108, 'service', 62, 1, '95.00', '0.00', '95.00', 2),
(6529, 6108, 'service', 55, 1, '50.00', '0.00', '50.00', 2),
(6530, 6108, 'service', 63, 1, '80.00', '0.00', '80.00', 2),
(6531, 6109, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6532, 6110, 'service', 51, 1, '40.00', '0.00', '40.00', 4),
(6533, 6110, 'service', 58, 1, '35.00', '0.00', '35.00', 4),
(6534, 6111, 'service', 55, 1, '50.00', '0.00', '50.00', 14),
(6535, 6112, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6536, 6113, 'service', 49, 1, '100.00', '0.00', '100.00', 3),
(6537, 6113, 'service', 48, 1, '45.00', '0.00', '45.00', 1),
(6538, 6114, 'service', 48, 1, '45.00', '0.00', '45.00', 14),
(6539, 6115, 'service', 49, 1, '100.00', '0.00', '100.00', 2),
(6540, 6116, 'service', 55, 1, '50.00', '0.00', '50.00', 1),
(6541, 6116, 'service', 50, 1, '50.00', '0.00', '50.00', 1),
(6542, 6117, 'service', 49, 1, '100.00', '0.00', '100.00', 4),
(6543, 6118, 'service', 48, 1, '45.00', '0.00', '45.00', 2),
(6544, 6119, 'service', 48, 1, '45.00', '0.00', '45.00', 2);

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` int(11) NOT NULL,
  `type` enum('appointment_reminder','system','custom') NOT NULL DEFAULT 'system' COMMENT 'نوع الإشعار',
  `recipient_id` int(11) DEFAULT NULL COMMENT 'معرف المستلم (العميل أو المستخدم)',
  `recipient_type` enum('customer','user') NOT NULL DEFAULT 'customer' COMMENT 'نوع المستلم',
  `title` varchar(255) NOT NULL COMMENT 'عنوان الإشعار',
  `message` text NOT NULL COMMENT 'نص الإشعار',
  `related_id` int(11) DEFAULT NULL COMMENT 'معرف العنصر المرتبط (مثل معرف الموعد)',
  `related_type` varchar(50) DEFAULT NULL COMMENT 'نوع العنصر المرتبط (مثل appointment)',
  `branch_id` int(11) DEFAULT NULL COMMENT 'معرف الفرع',
  `is_read` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'هل تم قراءة الإشعار',
  `is_sent` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'هل تم إرسال الإشعار',
  `send_email` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'هل يجب إرسال بريد إلكتروني',
  `send_sms` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'هل يجب إرسال رسالة نصية',
  `scheduled_at` datetime DEFAULT NULL COMMENT 'وقت جدولة الإشعار',
  `sent_at` datetime DEFAULT NULL COMMENT 'وقت إرسال الإشعار',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `notifications`
--

INSERT INTO `notifications` (`id`, `type`, `recipient_id`, `recipient_type`, `title`, `message`, `related_id`, `related_type`, `branch_id`, `is_read`, `is_sent`, `send_email`, `send_sms`, `scheduled_at`, `sent_at`, `created_at`, `updated_at`) VALUES
(520, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00593 بنجاح إلى العميل جو ميدكال', 6030, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-25 12:08:43', NULL),
(521, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00594 بنجاح إلى العميل محمد زياده', 6031, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-25 13:25:04', NULL),
(522, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00595 بنجاح إلى العميل محمد حسين صالح', 6032, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-25 14:18:48', NULL),
(523, 'system', 10, 'user', 'إشعار فاتورة', 'فشل إرسال إشعار الفاتورة رقم INV-00596 إلى العميل ', 6033, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-25 14:21:46', NULL),
(524, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00597 بنجاح إلى العميل احمد السيد رزق', 6034, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-25 16:36:18', NULL),
(525, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00599 بنجاح إلى العميل عبدالعليم', 6036, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-25 18:12:25', NULL),
(526, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00600 بنجاح إلى العميل محمد تامر', 6037, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-25 18:16:34', NULL),
(527, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00601 بنجاح إلى العميل محمد يحيي', 6038, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-25 19:34:39', NULL),
(528, 'system', 10, 'user', 'إشعار فاتورة', 'فشل إرسال إشعار الفاتورة رقم INV-00602 إلى العميل ', 6039, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-25 20:14:23', NULL),
(529, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00603 بنجاح إلى العميل محمد  عبد الفتاح فؤد', 6040, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-25 22:06:35', NULL),
(530, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00606 بنجاح إلى العميل ابو عمر احمد شعبان', 6043, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-26 16:43:50', NULL),
(531, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00608 بنجاح إلى العميل عمر السعيد كربال', 6045, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-26 19:19:04', NULL),
(532, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00613 بنجاح إلى العميل خالد زياده', 6050, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-27 13:07:00', NULL),
(533, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00614 بنجاح إلى العميل عمرو علي', 6051, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-27 13:23:21', NULL),
(534, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00615 بنجاح إلى العميل احمد عادل سمير', 6052, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-27 14:36:05', NULL),
(535, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00616 بنجاح إلى العميل مجدي طارق عبد المجيد', 6053, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-27 14:44:48', NULL),
(536, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00617 بنجاح إلى العميل احمد محمد حامد', 6054, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-27 15:47:55', NULL),
(537, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00621 بنجاح إلى العميل حسن مجدي', 6058, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-27 19:43:08', NULL),
(538, 'system', 10, 'user', 'إشعار فاتورة', 'فشل إرسال إشعار الفاتورة رقم INV-00622 إلى العميل ثروت زينهم', 6059, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-27 20:57:42', NULL),
(539, 'system', 10, 'user', 'إشعار فاتورة', 'فشل إرسال إشعار الفاتورة رقم INV-00623 إلى العميل محمد سيد احمد', 6060, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-27 22:13:11', NULL),
(540, 'system', 10, 'user', 'إشعار فاتورة', 'فشل إرسال إشعار الفاتورة رقم INV-00624 إلى العميل احمد عارف', 6061, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-27 22:25:38', NULL),
(541, 'system', 10, 'user', 'إشعار فاتورة', 'فشل إرسال إشعار الفاتورة رقم INV-00625 إلى العميل زيدان المهدي بركه', 6062, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-27 23:01:12', NULL),
(542, 'system', 10, 'user', 'إشعار فاتورة', 'فشل إرسال إشعار الفاتورة رقم INV-00626 إلى العميل ابراهيم محمد ابراهيم', 6063, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-27 23:18:00', NULL),
(543, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00627 بنجاح إلى العميل شيكا', 6064, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-28 14:10:12', NULL),
(544, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00631 بنجاح إلى العميل محمد السيد محمد', 6068, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-28 14:47:54', NULL),
(545, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00632 بنجاح إلى العميل عبد الراجمن رضا العزب', 6069, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-28 17:03:26', NULL),
(546, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00633 بنجاح إلى العميل محمد جمل ابو الوفا', 6070, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-28 18:24:07', NULL),
(547, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00634 بنجاح إلى العميل فوزي شعبان محمد', 6071, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-28 18:31:38', NULL),
(548, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00635 بنجاح إلى العميل السيد السعيد السيد', 6072, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-28 18:38:41', NULL),
(549, 'system', 10, 'user', 'إشعار فاتورة', 'فشل إرسال إشعار الفاتورة رقم INV-00636 إلى العميل ', 6073, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-28 18:47:03', NULL),
(550, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00637 بنجاح إلى العميل زياد علي المتولي', 6074, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-28 19:04:19', NULL),
(551, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00638 بنجاح إلى العميل محمد عبدلله', 6075, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-28 20:00:47', NULL),
(552, 'system', 10, 'user', 'إشعار فاتورة', 'فشل إرسال إشعار الفاتورة رقم INV-00639 إلى العميل ', 6076, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-28 20:01:12', NULL),
(553, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00640 بنجاح إلى العميل محمد ابو مسعد', 6078, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-28 22:11:28', NULL),
(554, 'system', 8, 'user', 'إشعار فاتورة', 'فشل إرسال إشعار الفاتورة رقم INV-00642 إلى العميل ', 6080, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-29 07:59:24', NULL),
(555, 'system', 8, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00643 بنجاح إلى العميل محمد احمد البسطؤيسي', 6081, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-29 11:31:46', NULL),
(556, 'system', 10, 'user', 'إشعار فاتورة', 'فشل إرسال إشعار الفاتورة رقم INV-00644 إلى العميل ', 6082, 'invoice', 1, 1, 1, 0, 0, NULL, NULL, '2025-05-29 11:37:55', '2025-05-29 18:13:16'),
(557, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00645 بنجاح إلى العميل احمد عصام احمد', 6083, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-29 11:42:33', NULL),
(558, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00647 بنجاح إلى العميل محمد زياده', 6085, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-29 12:28:49', NULL),
(559, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00648 بنجاح إلى العميل محمد  علي يونس', 6086, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-29 14:42:50', NULL),
(560, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00651 بنجاح إلى العميل احمد زكي', 6089, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-29 16:42:36', NULL),
(561, 'system', 10, 'user', 'إشعار فاتورة', 'فشل إرسال إشعار الفاتورة رقم INV-00652 إلى العميل عبد الراحمن السعيد الخولي', 6090, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-29 18:50:46', NULL),
(562, 'system', 10, 'user', 'إشعار فاتورة', 'فشل إرسال إشعار الفاتورة رقم INV-00653 إلى العميل ', 6091, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-29 19:45:20', NULL),
(563, 'system', 10, 'user', 'إشعار فاتورة', 'فشل إرسال إشعار الفاتورة رقم INV-00654 إلى العميل ', 6092, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-29 20:31:06', NULL),
(564, 'system', 10, 'user', 'إشعار فاتورة', 'فشل إرسال إشعار الفاتورة رقم INV-00655 إلى العميل اسلام سامي', 6093, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-29 20:36:29', NULL),
(565, 'system', 10, 'user', 'إشعار فاتورة', 'فشل إرسال إشعار الفاتورة رقم INV-00654 إلى العميل محمد علاق', 6092, 'invoice', 1, 1, 1, 0, 0, NULL, NULL, '2025-05-29 22:03:21', '2025-05-29 22:06:34'),
(566, 'system', 10, 'user', 'إشعار فاتورة', 'فشل إرسال إشعار الفاتورة رقم INV-00654 إلى العميل محمد علاق', 6092, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-29 22:06:36', NULL),
(567, 'system', 10, 'user', 'إشعار فاتورة', 'فشل إرسال إشعار الفاتورة رقم INV-00654 إلى العميل محمد علاق', 6092, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-29 22:06:39', NULL),
(568, 'system', 10, 'user', 'إشعار فاتورة', 'فشل إرسال إشعار الفاتورة رقم INV-00656 إلى العميل محمود حسن القضب', 6094, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-29 22:29:12', NULL),
(569, 'system', 10, 'user', 'إشعار فاتورة', 'فشل إرسال إشعار الفاتورة رقم INV-00657 إلى العميل ', 6095, 'invoice', 1, 1, 1, 0, 0, NULL, NULL, '2025-05-29 23:13:18', '2025-05-30 00:20:26'),
(570, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00657 بنجاح إلى العميل محمد محمود البنداري', 6096, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-30 00:14:30', NULL),
(571, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00658 بنجاح إلى العميل يوسف محمد علاء', 6097, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-30 11:38:29', NULL),
(572, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00659 بنجاح إلى العميل حسن سمير حسن', 6098, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-30 12:11:41', NULL),
(573, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00660 بنجاح إلى العميل كمال زكريا', 6099, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-30 12:17:53', NULL),
(574, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00661 بنجاح إلى العميل احمد السباعي', 6100, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-30 13:02:39', NULL),
(575, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00662 بنجاح إلى العميل احمد شحاته', 6101, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-30 14:20:47', NULL),
(576, 'system', 10, 'user', 'إشعار فاتورة', 'فشل إرسال إشعار الفاتورة رقم INV-00663 إلى العميل ', 6102, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-30 14:35:18', NULL),
(577, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00666 بنجاح إلى العميل زكي مسعد الصياد', 6105, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-30 18:09:59', NULL),
(578, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00668 بنجاح إلى العميل محمود محمد فواد', 6107, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-30 19:04:47', NULL),
(579, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00669 بنجاح إلى العميل احمد عبد الحميد الموفي', 6108, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-30 19:53:05', NULL),
(580, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00670 بنجاح إلى العميل احمد محمد فتحي', 6109, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-30 20:19:56', NULL),
(581, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00672 بنجاح إلى العميل يوسف محمد علاء', 6111, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-31 15:34:02', NULL),
(582, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00673 بنجاح إلى العميل محمدالسيد الجعيدي', 6112, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-31 16:04:46', NULL),
(583, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00674 بنجاح إلى العميل احمد سعد', 6113, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-31 16:40:59', NULL),
(584, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00675 بنجاح إلى العميل عبد الرحمن محمد محمد', 6114, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-31 17:18:46', NULL),
(585, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00676 بنجاح إلى العميل محمد اكرام محمود', 6115, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-31 17:29:14', NULL),
(586, 'system', 10, 'user', 'إشعار فاتورة', 'تم إرسال إشعار الفاتورة رقم INV-00678 بنجاح إلى العميل ابراهيم حسن علي', 6117, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-31 20:01:10', NULL),
(587, 'system', 8, 'user', 'إشعار فاتورة', 'فشل إرسال إشعار الفاتورة رقم INV-00679 إلى العميل ', 6118, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-31 22:22:31', NULL),
(588, 'system', 8, 'user', 'إشعار فاتورة', 'فشل إرسال إشعار الفاتورة رقم INV-00680 إلى العميل ', 6119, 'invoice', 1, 0, 1, 0, 0, NULL, NULL, '2025-05-31 22:22:46', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `products`
--

CREATE TABLE `products` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `cost` decimal(10,2) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  `is_for_sale` tinyint(1) DEFAULT 1 COMMENT '1=للبيع, 0=للاستخدام الداخلي',
  `min_quantity` int(11) DEFAULT 5,
  `is_active` tinyint(1) DEFAULT 1,
  `branch_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `products`
--

INSERT INTO `products` (`id`, `name`, `description`, `price`, `cost`, `category_id`, `is_for_sale`, `min_quantity`, `is_active`, `branch_id`, `created_at`, `updated_at`) VALUES
(3, 'جل فرى مان', '', '40.00', '30.00', 1, 1, 5, 1, 1, '2025-03-20 14:10:36', '2025-03-20 14:10:36'),
(4, 'جل اكسلنت', '', '50.00', '40.00', 1, 1, 5, 1, 1, '2025-03-20 14:11:45', '2025-03-20 14:11:45'),
(5, 'توبيك', '', '200.00', '150.00', 2, 1, 5, 1, 1, '2025-03-20 14:12:54', '2025-03-20 14:12:54'),
(6, 'صبغه برو استايل', '', '100.00', '80.00', 4, 1, 5, 1, 1, '2025-03-20 14:13:53', '2025-03-20 14:13:53'),
(7, 'جل نيو ستار', '', '35.00', '28.00', 1, 1, 5, 1, 1, '2025-03-20 14:14:57', '2025-03-20 14:14:57'),
(8, 'مناديل مبلله', '', '40.00', '30.00', 7, 1, 5, 1, 1, '2025-03-20 14:15:35', '2025-03-20 14:15:35'),
(9, 'مثبت هير اسبراى', '', '100.00', '75.00', 1, 1, 5, 1, 1, '2025-03-20 14:16:37', '2025-03-20 14:16:37'),
(10, 'مخمريه كيكو', '', '50.00', '32.00', 3, 1, 5, 1, 1, '2025-03-20 14:17:14', '2025-03-20 14:17:14'),
(11, 'كريم كرلي بيج سوفت', '', '90.00', '70.00', 2, 1, 5, 1, 1, '2025-03-20 14:18:20', '2025-03-20 14:18:20'),
(12, 'كريم كرلى بليس', '', '100.00', '80.00', 2, 1, 5, 1, 1, '2025-03-20 14:19:10', '2025-03-20 14:19:10'),
(13, 'فرد كلاسيك', '', '50.00', '30.00', 2, 1, 5, 1, 1, '2025-03-20 14:19:50', '2025-03-20 14:19:50'),
(14, 'كريم فرد بالكرياتين', '', '50.00', '30.00', 2, 1, 5, 1, 1, '2025-03-20 14:21:47', '2025-03-20 14:21:47'),
(15, 'كريم فرد كوجيك', '', '50.00', '30.00', 2, 1, 5, 1, 1, '2025-03-20 14:22:23', '2025-03-20 14:22:23'),
(16, 'ماسك بلو رمان', '', '60.00', '50.00', 3, 1, 5, 1, 1, '2025-03-20 14:23:57', '2025-03-20 14:23:57'),
(17, 'طمى مغر بي', '', '20.00', '15.00', 3, 1, 5, 1, 1, '2025-03-20 14:24:46', '2025-03-20 14:24:46'),
(18, 'ماسك بلو لوز', '', '50.00', '35.00', 3, 1, 5, 1, 1, '2025-03-20 14:25:28', '2025-03-20 14:25:28'),
(19, 'ماسك سنفره هيمرا', '', '50.00', '35.00', 3, 1, 5, 1, 1, '2025-03-20 14:26:08', '2025-03-20 14:26:08'),
(20, 'كريم طرطيب كالوديا', '', '30.00', '22.00', 3, 1, 5, 1, 1, '2025-03-20 14:27:11', '2025-03-20 14:27:11'),
(21, 'جل حلاقه كلاسيك', '', '50.00', '35.00', 6, 1, 5, 1, 1, '2025-03-20 14:28:25', '2025-03-20 14:28:25'),
(22, 'سلكات ودان', '', '15.00', '10.00', 7, 1, 10, 1, 1, '2025-03-20 14:30:08', '2025-03-20 14:30:08'),
(23, 'اسبونشه كيرلي', '', '50.00', '35.00', 7, 1, 1, 1, 1, '2025-03-20 14:30:37', '2025-05-10 21:35:12'),
(24, 'استشوار سونر 8892', '', '300.00', '250.00', 5, 1, 3, 1, 1, '2025-03-20 14:33:01', '2025-03-20 14:33:01'),
(25, 'استشوار سوكانى  3618', '', '350.00', '300.00', 5, 1, 3, 1, 1, '2025-03-20 14:34:52', '2025-03-20 14:34:52'),
(26, 'مكوه سوكانى 6505', '', '350.00', '300.00', 5, 1, 3, 1, 1, '2025-03-20 14:36:07', '2025-03-20 14:36:07'),
(27, 'مكوه كيمي 531', '', '300.00', '250.00', 5, 1, 3, 1, 1, '2025-03-20 14:37:25', '2025-03-20 14:37:25'),
(28, 'استشوار اطفال', '', '50.00', '50.00', 5, 1, 3, 1, 1, '2025-03-20 14:38:22', '2025-05-09 19:51:02'),
(29, 'جهاز شمع', '', '350.00', '300.00', 5, 1, 2, 1, 1, '2025-03-20 14:40:15', '2025-03-20 14:40:15'),
(30, 'استشوار تورنيدو', '', '1100.00', '1100.00', 5, 1, 1, 1, 1, '2025-03-20 14:47:18', '2025-03-20 14:47:18'),
(31, 'كرتونة شفرات خاصه', '', '120.00', '120.00', 6, 0, 2, 1, 1, '2025-03-20 14:47:50', '2025-03-20 14:47:50'),
(32, 'مناديل رقبه', '', '60.00', '60.00', 7, 0, 10, 1, 1, '2025-03-20 14:48:16', '2025-03-20 14:48:16'),
(33, 'اسبر اى شعر', '', '190.00', '190.00', 1, 0, 3, 1, 1, '2025-03-20 14:48:47', '2025-03-20 14:48:47'),
(35, 'ملمع', '', '100.00', '100.00', 2, 0, 2, 1, 1, '2025-03-20 14:50:45', '2025-03-20 14:50:45'),
(36, 'معالج شعر', '', '150.00', '150.00', 2, 0, 2, 1, 1, '2025-03-20 14:51:39', '2025-03-20 14:51:39'),
(37, 'صبغه هير كلور', '', '175.00', '175.00', 4, 0, 2, 1, 1, '2025-03-20 14:52:41', '2025-03-20 14:52:41'),
(38, 'حمام كريم', '', '40.00', '40.00', 2, 0, 3, 1, 1, '2025-03-20 14:53:51', '2025-03-20 14:53:51'),
(39, 'فوم كرلي للشعر', '', '75.00', '75.00', 1, 0, 2, 1, 1, '2025-03-20 14:54:17', '2025-03-20 14:54:17'),
(40, 'زيت سيرم', '', '150.00', '100.00', 2, 1, 3, 1, 1, '2025-03-20 14:54:56', '2025-03-20 14:54:56'),
(41, 'ماكين حلاقه', '', '750.00', '750.00', 5, 0, 1, 1, 1, '2025-03-20 14:57:00', '2025-03-20 14:57:00'),
(42, 'شمع', '', '100.00', '100.00', 3, 0, 2, 1, 1, '2025-03-20 14:57:53', '2025-03-20 14:57:53'),
(43, 'حنه فاتيكا', '', '160.00', '160.00', 4, 0, 2, 1, 1, '2025-03-20 14:58:27', '2025-03-20 14:58:27'),
(44, 'فوة قص', '', '100.00', '100.00', 7, 0, 2, 1, 1, '2025-03-20 15:00:25', '2025-03-20 15:00:25'),
(45, 'فوط تنشيف', '', '150.00', '150.00', 7, 0, 5, 1, 1, '2025-03-20 15:01:06', '2025-03-20 15:01:06'),
(46, 'بلسم', '', '50.00', '50.00', 2, 0, 3, 1, 1, '2025-03-20 15:02:00', '2025-03-20 15:02:00'),
(47, 'امواس', '', '200.00', '200.00', 6, 0, 5, 1, 1, '2025-03-20 15:02:53', '2025-03-20 15:02:53'),
(48, 'فرشه شعر زيرو', '', '30.00', '30.00', 7, 0, 3, 1, 1, '2025-03-20 15:03:59', '2025-03-20 15:03:59'),
(49, 'غسول', '', '25.00', '25.00', 3, 0, 3, 1, 1, '2025-03-20 15:04:14', '2025-03-20 15:04:14'),
(50, 'فوم للدقن', '', '50.00', '50.00', 6, 0, 2, 1, 1, '2025-03-20 15:05:00', '2025-03-20 15:05:00'),
(51, 'بودرة تفتيح', '', '150.00', '150.00', 4, 0, 2, 1, 1, '2025-03-20 15:05:27', '2025-03-20 15:05:27'),
(52, 'واكس', '', '30.00', '30.00', 1, 0, 2, 1, 1, '2025-03-20 15:07:18', '2025-03-20 15:07:18'),
(53, 'ماسك صنفره  خيار', '', '22.50', '22.50', 3, 0, 3, 1, 1, '2025-03-20 15:10:00', '2025-03-20 15:10:00'),
(54, 'ماسك سنور', '', '30.00', '30.00', 3, 0, 3, 1, 1, '2025-03-20 15:10:42', '2025-03-20 15:10:42'),
(55, 'ملح صنفرة', '', '110.00', '110.00', 3, 0, 2, 1, 1, '2025-03-20 15:12:26', '2025-03-20 15:12:26'),
(56, 'مشط', '', '15.00', '15.00', 7, 0, 5, 1, 1, '2025-03-20 15:19:37', '2025-03-20 15:19:37'),
(57, 'فزلين هيرفود', '', '70.00', '100.00', 3, 1, 2, 1, 1, '2025-04-13 16:28:48', NULL),
(58, 'مسك لوزان', '', '35.00', '15.00', NULL, 1, 3, 1, 1, '2025-04-13 16:31:48', NULL),
(59, 'جل فيورا', '', '60.00', '40.00', NULL, 1, 1, 1, 1, '2025-04-13 16:34:57', NULL),
(60, 'كريم مرطب فيورا', '', '70.00', '50.00', NULL, 1, 5, 1, 1, '2025-04-13 16:36:05', NULL),
(61, 'منديل مبلله', '', '50.00', '40.00', NULL, 1, 1, 1, 1, '2025-04-13 16:37:51', NULL),
(62, 'سيرم uc', '', '95.00', '75.00', NULL, 1, 1, 1, 1, '2025-04-13 16:39:13', NULL),
(63, 'جونديات', '', '220.00', '200.00', 6, 1, 1, 1, 1, '2025-04-13 16:43:16', NULL),
(64, 'جوندي بلاستك', '', '30.00', '20.00', 4, 1, 2, 1, 1, '2025-04-13 16:44:29', NULL),
(65, 'شمع  خرز', '', '145.00', '125.00', 3, 1, 2, 1, 1, '2025-04-13 16:46:29', NULL),
(66, 'شفت سوفت', '', '140.00', '120.00', 5, 1, 1, 1, 1, '2025-04-13 16:52:37', NULL),
(67, 'منديل رقبه', '', '80.00', '60.00', 6, 1, 5, 1, 1, '2025-04-13 16:54:15', NULL),
(68, 'صبغه', '', '170.00', '150.00', 4, 1, 1, 1, 1, '2025-04-13 16:55:46', NULL),
(69, 'بودره تفتيح', '', '100.00', '80.00', 4, 1, 5, 1, 1, '2025-04-13 16:58:05', NULL),
(70, 'حمامك فيورا', '', '100.00', '85.00', NULL, 1, 1, 1, 1, '2025-04-13 16:59:12', NULL),
(71, 'ملمع oliu', '', '100.00', '75.00', NULL, 1, 5, 1, 1, '2025-04-13 17:01:32', NULL),
(72, 'حمام ويللي', '', '50.00', '30.00', NULL, 1, 1, 1, 1, '2025-04-13 17:02:45', NULL),
(73, 'امواس لورد', '', '55.00', '55.00', NULL, 1, 1, 1, 1, '2025-04-13 17:03:36', NULL),
(74, 'فوط صبغه', '', '50.00', '50.00', NULL, 1, 1, 1, 1, '2025-04-13 17:04:28', NULL),
(75, 'فرشه', '', '25.00', '25.00', NULL, 1, 3, 1, 1, '2025-04-13 17:06:58', NULL),
(76, 'حامل استشوار', '', '50.00', '50.00', NULL, 1, 5, 1, 1, '2025-04-13 17:07:39', NULL),
(77, 'فوط بلاستك', '', '100.00', '100.00', NULL, 1, 1, 1, 1, '2025-04-13 17:08:37', NULL),
(78, 'فوط تنشيف غسيل', '', '60.00', '60.00', 6, 1, 1, 1, 1, '2025-04-13 17:10:07', NULL),
(79, 'صبغه شعر مصري', '', '100.00', '60.00', NULL, 1, 3, 1, 1, '2025-04-13 17:18:42', NULL),
(80, 'مشط شوكه معدن كبير', 'مشط مستخدم للصالون', '25.00', '25.00', NULL, 0, 1, 1, 1, '2025-04-13 17:25:52', NULL),
(81, 'امشاط مشكله واسعه', 'امشاط لاستخدام الصالون', '8.50', '8.50', NULL, 1, 10, 1, 1, '2025-04-13 17:29:16', NULL),
(82, 'مشط مشكل', '', '85.00', '85.00', 7, 1, 1, 1, 1, '2025-04-14 19:10:08', NULL),
(83, 'بخاخ مياه', '', '125.00', '125.00', 7, 1, 1, 1, 1, '2025-04-14 19:16:25', NULL),
(84, 'حامل استند', '', '50.00', '50.00', 7, 1, 1, 1, 1, '2025-04-14 19:17:33', NULL),
(85, 'فوط اطفل', '', '50.00', '50.00', NULL, 1, 2, 1, 1, '2025-04-14 19:18:21', NULL),
(86, 'جل حلاقه', '', '30.00', '30.00', NULL, 1, 4, 1, 1, '2025-04-14 19:19:45', NULL),
(87, 'مسبت تركي', '', '175.00', '175.00', NULL, 1, 2, 1, 1, '2025-04-14 19:20:38', NULL),
(88, 'فرشه اسود', '', '75.00', '75.00', NULL, 1, 4, 1, 1, '2025-04-14 19:21:40', NULL),
(89, 'مقصط', '', '450.00', '450.00', NULL, 1, 3, 1, 1, '2025-04-14 19:22:42', NULL),
(90, 'بخاخه', '', '60.00', '60.00', 7, 1, 6, 1, 1, '2025-04-14 19:23:35', NULL),
(91, 'فزلين', '', '40.00', '40.00', 6, 1, 3, 1, 1, '2025-04-14 19:24:26', NULL),
(92, 'استيك', '', '15.00', '15.00', 7, 1, 7, 1, 1, '2025-04-14 19:25:35', NULL),
(93, 'اكبسان', '', '35.00', '35.00', 7, 1, 1, 1, 1, '2025-04-14 19:26:24', NULL),
(94, 'مشط فتت', '', '50.00', '50.00', 5, 1, 2, 1, 1, '2025-04-14 19:28:45', NULL),
(96, 'بروتين', '', '200.00', '200.00', NULL, 1, 1, 1, 1, '2025-05-08 14:19:52', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `product_categories`
--

CREATE TABLE `product_categories` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `product_categories`
--

INSERT INTO `product_categories` (`id`, `name`, `description`) VALUES
(1, 'منتجات تصفيف الشعر', 'جل، واكس، سبراي، ومنتجات أخرى لتصفيف وترتيب الشعر'),
(2, 'منتجات العناية بالشعر', 'شامبو، بلسم، معالجات الشعر، كريمات ترطيب'),
(3, 'منتجات العناية بالبشرة', 'ماسكات، مقشرات، كريمات ترطيب ومنتجات العناية بالبشرة'),
(4, 'منتجات الصبغة والتلوين', 'صبغات الشعر، الحناء، بودرة التفتيح'),
(5, 'أجهزة ومعدات', 'أجهزة التجفيف، المكواة، ماكينات الحلاقة'),
(6, 'منتجات الحلاقة', 'شفرات، رغوة حلاقة، جل الحلاقة'),
(7, 'مستلزمات الصالون', 'فوط، مناديل، أمشاط، فرش، ومستلزمات أخرى');

-- --------------------------------------------------------

--
-- Table structure for table `promotions`
--

CREATE TABLE `promotions` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL COMMENT 'اسم العرض',
  `description` text DEFAULT NULL COMMENT 'وصف العرض',
  `condition_type` enum('total_amount','items_count','specific_product','specific_service') NOT NULL COMMENT 'نوع الشرط',
  `condition_value` decimal(10,2) NOT NULL COMMENT 'قيمة الشرط',
  `condition_max_value` decimal(10,2) DEFAULT NULL COMMENT 'الحد الأقصى لقيمة الشرط (اختياري)',
  `discount_type` enum('percentage','fixed') NOT NULL COMMENT 'نوع الخصم',
  `discount_value` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT 'قيمة الخصم',
  `start_date` date DEFAULT NULL COMMENT 'تاريخ بداية العرض',
  `end_date` date DEFAULT NULL COMMENT 'تاريخ نهاية العرض',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'هل العرض نشط',
  `branch_id` int(11) DEFAULT NULL COMMENT 'معرف الفرع (NULL لجميع الفروع)',
  `specific_item_id` int(11) DEFAULT NULL COMMENT 'معرف المنتج أو الخدمة المحددة (إذا كان نوع الشرط specific_product أو specific_service)',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `promotion_usage`
--

CREATE TABLE `promotion_usage` (
  `id` int(11) NOT NULL,
  `promotion_id` int(11) NOT NULL COMMENT 'معرف العرض',
  `invoice_id` int(11) NOT NULL COMMENT 'معرف الفاتورة',
  `customer_id` int(11) DEFAULT NULL COMMENT 'معرف العميل',
  `discount_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT 'قيمة الخصم المطبق',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `promo_codes`
--

CREATE TABLE `promo_codes` (
  `id` int(11) NOT NULL,
  `code` varchar(50) NOT NULL COMMENT 'كود الخصم',
  `name` varchar(255) NOT NULL COMMENT 'اسم كود الخصم',
  `description` text DEFAULT NULL COMMENT 'وصف كود الخصم',
  `discount_type` enum('percentage','fixed') NOT NULL COMMENT 'نوع الخصم',
  `discount_value` decimal(10,2) NOT NULL COMMENT 'قيمة الخصم',
  `start_date` date DEFAULT NULL COMMENT 'تاريخ بداية الكود',
  `end_date` date DEFAULT NULL COMMENT 'تاريخ نهاية الكود',
  `min_invoice_amount` decimal(10,2) DEFAULT NULL COMMENT 'الحد الأدنى لقيمة الفاتورة',
  `max_invoice_amount` decimal(10,2) DEFAULT NULL COMMENT 'الحد الأقصى لقيمة الفاتورة',
  `max_uses` int(11) DEFAULT NULL COMMENT 'الحد الأقصى لعدد مرات الاستخدام',
  `one_use_per_customer` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'استخدام مرة واحدة لكل عميل (1 = نعم، 0 = لا)',
  `current_uses` int(11) NOT NULL DEFAULT 0 COMMENT 'عدد مرات الاستخدام الحالية',
  `is_for_specific_customers` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'هل الكود مخصص لعملاء محددين',
  `required_loyalty_points` int(11) DEFAULT NULL COMMENT 'عدد نقاط الولاء المطلوبة للاستخدام',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'هل الكود نشط',
  `branch_id` int(11) DEFAULT NULL COMMENT 'معرف الفرع (NULL لجميع الفروع)',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `promo_codes`
--

INSERT INTO `promo_codes` (`id`, `code`, `name`, `description`, `discount_type`, `discount_value`, `start_date`, `end_date`, `min_invoice_amount`, `max_invoice_amount`, `max_uses`, `one_use_per_customer`, `current_uses`, `is_for_specific_customers`, `required_loyalty_points`, `is_active`, `branch_id`, `created_at`, `updated_at`) VALUES
(1, 'aaxxx', 'خصم 10%', '', 'percentage', '10.00', NULL, NULL, NULL, NULL, 50, 1, 0, 0, NULL, 1, NULL, '2025-04-20 13:32:39', '2025-04-20 13:33:40');

-- --------------------------------------------------------

--
-- Table structure for table `promo_code_customers`
--

CREATE TABLE `promo_code_customers` (
  `id` int(11) NOT NULL,
  `promo_code_id` int(11) NOT NULL COMMENT 'معرف كود الخصم',
  `customer_id` int(11) NOT NULL COMMENT 'معرف العميل',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `promo_code_usage`
--

CREATE TABLE `promo_code_usage` (
  `id` int(11) NOT NULL,
  `promo_code_id` int(11) NOT NULL COMMENT 'معرف كود الخصم',
  `invoice_id` int(11) NOT NULL COMMENT 'معرف الفاتورة',
  `customer_id` int(11) DEFAULT NULL COMMENT 'معرف العميل',
  `discount_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT 'قيمة الخصم المطبق',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `services`
--

CREATE TABLE `services` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `duration` int(11) DEFAULT 30 COMMENT 'في الدقائق',
  `category_id` int(11) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `branch_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `services`
--

INSERT INTO `services` (`id`, `name`, `description`, `price`, `duration`, `category_id`, `is_active`, `branch_id`, `created_at`, `updated_at`) VALUES
(43, 'شعر', 'قص الشعر العادي', '75.00', 30, 1, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:21:18'),
(44, 'حلاقة شعر', 'حلاقة شعر عادية', '75.00', 30, 1, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:18:45'),
(45, 'حلاقة شعر موس/ماكينة', 'حلاقة شعر باستخدام الموس أو الماكينة', '55.00', 30, 1, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:19:56'),
(46, 'حلاقة شعر دبر فيد/أسلو فيد', 'تصفيف الشعر بأسلوب دبر فيد أو أسلو فيد', '65.00', 30, 1, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:19:40'),
(47, 'حلاقة شعر اسكين فيد', 'قص الشعر بأسلوب الاسكين فيد', '75.00', 30, 1, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:19:07'),
(48, 'حلاقة شعر الأطفال مع استايل', 'حلاقة شعر الأطفال مع تصفيف مميز', '45.00', 30, 1, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:19:26'),
(49, 'حلاقة الشعر مع اللحية', 'حلاقة الشعر مع تحديد أو حلاقة اللحية', '100.00', 30, 1, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:17:56'),
(50, 'تحديد اللحية مع التدريج', 'تحديد اللحية مع تدريج احترافي', '50.00', 30, 4, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:16:34'),
(51, 'حلاقة اللحية موس', 'حلاقة اللحية باستخدام الموس', '40.00', 30, 4, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:18:31'),
(52, 'حلاقة اللحية ماكينة زيرو', 'حلاقة اللحية باستخدام ماكينة زيرو', '30.00', 30, 4, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:18:10'),
(53, 'شمع كامل للوجه', 'إزالة الشعر بالشمع لكامل الوجه', '55.00', 30, 2, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:21:48'),
(54, 'شمع أنف وأذن', 'إزالة الشعر بالشمع من الأنف والأذن', '25.00', 30, 2, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:21:31'),
(55, 'استشوار شعر قصير', 'تسريح الشعر القصير بالاستشوار', '50.00', 30, 1, 1, 1, '2025-03-15 23:03:57', '2025-04-07 20:28:38'),
(56, 'استشوار شعر طويل', 'تسريح الشعر الطويل بالاستشوار', '85.00', 30, 1, 1, 1, '2025-03-15 23:03:57', '2025-04-07 20:28:24'),
(57, 'كيرلي مكواة', 'تمويج الشعر بالمكواة', '95.00', 30, 1, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:22:56'),
(58, 'ويفي', 'تصفيف الشعر بأسلوب ويفي', '35.00', 30, 1, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:28:53'),
(59, 'بروتين معالج للشعر', 'بروتين معالج للشعر متوسط الطول', '545.00', 30, 1, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:16:22'),
(60, 'تريت مينت', 'علاج تريت مينت للشعر متوسط الطول', '740.00', 30, 1, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:16:50'),
(61, 'بروتين معالج + تريت مينت', 'بروتين معالج مع تريت مينت للشعر الطويل', '970.00', 30, 1, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:15:52'),
(62, 'معالج الشعر العادي', 'معالج شعر للشعر العادي', '95.00', 30, 1, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:28:42'),
(63, 'صبغة الشعر بدون تفتيح', 'صبغة الشعر بدون تفتيح اللون', '80.00', 30, 3, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:22:12'),
(64, 'صبغة اللحية بدون تفتيح', 'صبغة اللحية بدون تفتيح اللون', '40.00', 30, 3, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:22:27'),
(65, 'تفتيح لون الشعر مع الصبغة', 'تفتيح لون الشعر مع الصبغة العادية', '150.00', 30, 3, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:17:05'),
(66, 'تفتيح لون الشعر مع صبغة سيلفر', 'تفتيح لون الشعر مع صبغة فضية (سيلفر)', '250.00', 30, 3, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:17:21'),
(67, 'حنة للشعر', 'تلوين الشعر بالحناء', '50.00', 30, 3, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:20:39'),
(68, 'حنة للحية', 'تلوين اللحية بالحناء', '30.00', 30, 3, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:20:59'),
(69, 'تنظيف عميق للبشرة', 'تنظيف عميق للبشرة باستخدام أحدث التقنيات', '250.00', 30, 2, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:17:36'),
(70, 'حمام كريم للشعر بالزيوت الطبيعية مع بخار', 'حمام كريم مغذي للشعر مع استخدام البخار', '70.00', 30, 2, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:20:19'),
(71, 'فوطة ساخنة مع ماسك', 'عناية بالوجه باستخدام فوطة ساخنة مع ماسك مغذي', '30.00', 30, 2, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:22:41'),
(72, 'باكدج عريس كامل يوم واحد', 'باكدج عريس يشمل جميع الخدمات ليوم واحد', '1150.00', 30, 5, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:15:17'),
(73, 'باكدج عريس كامل يومين', 'باكدج عريس يشمل جميع الخدمات ليومين', '1550.00', 30, 5, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:15:29'),
(74, 'باكدج عريس كامل 3 أيام', 'باكدج عريس يشمل جميع الخدمات لـ 3 أيام', '1850.00', 30, 5, 1, 1, '2025-03-15 23:03:57', '2025-04-08 20:14:58');

-- --------------------------------------------------------

--
-- Table structure for table `service_categories`
--

CREATE TABLE `service_categories` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `service_categories`
--

INSERT INTO `service_categories` (`id`, `name`, `description`) VALUES
(1, 'قص الشعر', 'جميع خدمات قص وتصفيف الشعر'),
(2, 'العناية بالبشرة والوجه', 'خدمات العناية بالبشرة والوجه'),
(3, 'صبغ الشعر', 'خدمات صبغ وتلوين الشعر'),
(4, 'حلاقة اللحية والذقن', 'خدمات حلاقة وتهذيب اللحية والذقن'),
(5, 'VIP', 'عرسان ');

-- --------------------------------------------------------

--
-- Table structure for table `service_employees`
--

CREATE TABLE `service_employees` (
  `service_id` int(11) NOT NULL,
  `employee_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(50) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `setting_key`, `setting_value`, `description`) VALUES
(1, 'system_default_language', 'ar', 'اللغة الافتراضية للنظام'),
(2, 'system_timezone', 'Africa/Cairo', 'المنطقة الزمنية'),
(3, 'system_currency', 'جنيه مصري', 'العملة الافتراضية'),
(4, 'system_currency_symbol', 'ج', 'رمز العملة'),
(5, 'system_backup_interval', '7', 'الفترة بين النسخ الاحتياطية (بالأيام)'),
(6, 'invoice_company_name', 'Eszy salon', 'اسم الشركة في الفاتورة'),
(7, 'invoice_header_text', '', 'نص رأس الفاتورة'),
(8, 'invoice_footer_text', '', 'نص تذييل الفاتورة'),
(9, 'invoice_tax_rate', '0', ''),
(10, 'invoice_receipt_width', '80', ''),
(11, 'invoice_include_tax', '1', ''),
(12, 'system_auto_backup', '1', ''),
(13, 'appointment_start_time', '09:00', ''),
(14, 'appointment_end_time', '21:00', ''),
(15, 'appointment_interval', '30', ''),
(16, 'appointment_days_in_advance', '30', ''),
(17, 'appointment_allow_online_booking', '1', ''),
(18, 'salary_day_of_month', '28', ''),
(19, 'salary_reminder_days', '3', ''),
(20, 'salary_payment_reminder', '1', ''),
(21, 'notification_reminder_hours', '24', ''),
(22, 'notification_enable_email', '1', ''),
(23, 'notification_enable_sms', '1', ''),
(24, 'notification_appointment_reminder', '1', ''),
(26, 'invoice_copies', '2', ''),
(27, 'invoice_company_logo', '', ''),
(28, 'invoice_logo_width', '200', ''),
(29, 'invoice_logo_height', '100', ''),
(30, 'invoice_logo_print_quality', 'normal', ''),
(31, 'invoice_print_without_preview', '1', ''),
(32, 'whatsapp_client_side', '1', ''),
(33, 'notification_invoice_notification', '1', ''),
(34, 'notification_invoice_message_template', 'شكراً لزيارتكم {company_name}\r\n\r\nتفاصيل الفاتورة:\r\nرقم الفاتورة: {invoice_number}\r\nالتاريخ: {invoice_date}\r\nالمبلغ الإجمالي: {invoice_total}\r\n\r\nنتطلع لرؤيتكم مرة أخرى!', ''),
(35, 'notification_reminder_minutes', '30', ''),
(36, 'notification_reminder_method', 'js', ''),
(37, 'notification_check_interval', '5', ''),
(38, 'notification_admin_notifications', '1', ''),
(39, 'notification_admin_phone_numbers', '201022429488\r\n201009036186\r\n201032648474', ''),
(40, 'notification_notify_workday_open', '1', ''),
(41, 'notification_notify_workday_close', '1', ''),
(42, 'notification_notify_daily_report', '1', ''),
(43, 'notification_admin_message_template', 'تقرير يومي {date}\r\n\r\nإجمالي المبيعات: {total_sales}\r\nعدد الفواتير: {invoices_count}\r\nإجمالي المصروفات: {total_expenses}\r\nصافي الربح: {net_profit}\r\n\r\nأفضل المنتجات مبيعاً:\r\n{top_products}\r\n\r\nأفضل الخدمات مبيعاً:\r\n{top_services}', ''),
(44, 'notification_nvoice_notification', 'on', ''),
(45, 'whatsapp_enabled', '1', ''),
(46, 'whatsapp_account_sid', '', ''),
(47, 'whatsapp_auth_token', '', ''),
(48, 'whatsapp_from_number', '', ''),
(49, 'notification_enable_whatsapp', '1', ''),
(50, 'notification_debug_mode', 'on', ''),
(51, 'notification_notify_new_invoice', '1', ''),
(52, 'enable_background_tasks', '0', ''),
(53, 'notification_enable_minutes_reminder', '0', '');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `role` enum('admin','manager','cashier','employee') NOT NULL,
  `branch_id` int(11) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `last_login` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `username`, `password`, `name`, `email`, `role`, `branch_id`, `is_active`, `last_login`, `created_at`, `updated_at`) VALUES
(1, 'admin', '$2y$10$9pCaA8p.Q2gxKijzy2zbp.5jUNfxP6FFdm4noIldzffMm7Ar66Ht.', 'بشمهندس محمود', '<EMAIL>', 'admin', 1, 1, '2025-05-31 10:57:49', '2025-04-04 06:06:39', '2025-05-31 17:57:49'),
(8, 'ahmedx', '$2y$10$vPfB6P77ufi997Hu9SkUvuviQj4TWs1Tod2w/.kjy0Sz6Nolzsrui', 'احمد البدراوي', '', 'manager', 1, 1, '2025-05-31 13:59:39', '2025-04-06 06:33:07', '2025-05-31 20:59:39'),
(10, 'cashier2', '$2y$10$uqaHavUqOvreMb/t6EPsFeCpEK9Q6lvOLKNffxpoJ.FXXXZi8cnMK', 'البدرواي صالون', '<EMAIL>', 'cashier', 1, 1, '2025-05-31 08:44:54', '2025-04-07 08:30:12', '2025-05-31 15:44:54'),
(11, 'elbdarwy', '$2y$10$n0uIxgJvbCe5NGuq96NDdeU9VQlSNDxG2M1QWm9niX4DhXlHhrx8e', 'محمد البدراوي', '<EMAIL>', 'admin', 1, 1, '2025-05-31 13:48:38', '2025-04-07 23:09:20', '2025-05-31 20:48:38');

-- --------------------------------------------------------

--
-- Table structure for table `user_permissions`
--

CREATE TABLE `user_permissions` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `permission` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_permissions`
--

INSERT INTO `user_permissions` (`id`, `user_id`, `permission`) VALUES
(749, 1, 'appointments_create'),
(750, 1, 'appointments_edit'),
(748, 1, 'appointments_view'),
(731, 1, 'branches_add'),
(733, 1, 'branches_delete'),
(732, 1, 'branches_edit'),
(730, 1, 'branches_view'),
(735, 1, 'customers_add'),
(737, 1, 'customers_delete'),
(736, 1, 'customers_edit'),
(734, 1, 'customers_view'),
(721, 1, 'dashboard_view'),
(726, 1, 'employees_add'),
(728, 1, 'employees_delete'),
(727, 1, 'employees_edit'),
(729, 1, 'employees_salaries'),
(725, 1, 'employees_view'),
(769, 1, 'endday_manage'),
(757, 1, 'expenses_add'),
(759, 1, 'expenses_delete'),
(758, 1, 'expenses_edit'),
(756, 1, 'expenses_view'),
(747, 1, 'inventory_adjust'),
(746, 1, 'inventory_view'),
(752, 1, 'invoices_create'),
(754, 1, 'invoices_delete'),
(753, 1, 'invoices_edit'),
(755, 1, 'invoices_print'),
(751, 1, 'invoices_view'),
(743, 1, 'products_add'),
(745, 1, 'products_delete'),
(744, 1, 'products_edit'),
(742, 1, 'products_view'),
(761, 1, 'promotions_create'),
(762, 1, 'promotions_edit'),
(760, 1, 'promotions_view'),
(768, 1, 'reports_appointments'),
(766, 1, 'reports_employees'),
(767, 1, 'reports_expenses'),
(764, 1, 'reports_sales'),
(765, 1, 'reports_services'),
(763, 1, 'reports_view'),
(739, 1, 'services_add'),
(741, 1, 'services_delete'),
(740, 1, 'services_edit'),
(738, 1, 'services_view'),
(724, 1, 'settings_edit'),
(723, 1, 'settings_view'),
(722, 1, 'users_manage'),
(834, 8, 'appointments_create'),
(835, 8, 'appointments_edit'),
(833, 8, 'appointments_view'),
(823, 8, 'customers_add'),
(825, 8, 'customers_delete'),
(824, 8, 'customers_edit'),
(822, 8, 'customers_view'),
(816, 8, 'dashboard_view'),
(818, 8, 'employees_add'),
(820, 8, 'employees_delete'),
(819, 8, 'employees_edit'),
(821, 8, 'employees_salaries'),
(817, 8, 'employees_view'),
(851, 8, 'endday_manage'),
(842, 8, 'expenses_add'),
(844, 8, 'expenses_delete'),
(843, 8, 'expenses_edit'),
(841, 8, 'expenses_view'),
(832, 8, 'inventory_adjust'),
(831, 8, 'inventory_view'),
(837, 8, 'invoices_create'),
(839, 8, 'invoices_delete'),
(838, 8, 'invoices_edit'),
(840, 8, 'invoices_print'),
(836, 8, 'invoices_view'),
(828, 8, 'products_add'),
(830, 8, 'products_delete'),
(829, 8, 'products_edit'),
(827, 8, 'products_view'),
(850, 8, 'reports_appointments'),
(848, 8, 'reports_employees'),
(849, 8, 'reports_expenses'),
(846, 8, 'reports_sales'),
(847, 8, 'reports_services'),
(845, 8, 'reports_view'),
(826, 8, 'services_view'),
(544, 10, 'appointments_create'),
(545, 10, 'appointments_edit'),
(543, 10, 'appointments_view'),
(531, 10, 'customers_add'),
(532, 10, 'customers_edit'),
(530, 10, 'customers_view'),
(528, 10, 'dashboard_view'),
(529, 10, 'employees_view'),
(555, 10, 'endday_manage'),
(552, 10, 'expenses_add'),
(554, 10, 'expenses_delete'),
(553, 10, 'expenses_edit'),
(551, 10, 'expenses_view'),
(542, 10, 'inventory_adjust'),
(541, 10, 'inventory_view'),
(547, 10, 'invoices_create'),
(549, 10, 'invoices_delete'),
(548, 10, 'invoices_edit'),
(550, 10, 'invoices_print'),
(546, 10, 'invoices_view'),
(538, 10, 'products_add'),
(540, 10, 'products_delete'),
(539, 10, 'products_edit'),
(537, 10, 'products_view'),
(534, 10, 'services_add'),
(536, 10, 'services_delete'),
(535, 10, 'services_edit'),
(533, 10, 'services_view'),
(798, 11, 'appointments_create'),
(799, 11, 'appointments_edit'),
(797, 11, 'appointments_view'),
(780, 11, 'branches_add'),
(782, 11, 'branches_delete'),
(781, 11, 'branches_edit'),
(779, 11, 'branches_view'),
(784, 11, 'customers_add'),
(786, 11, 'customers_delete'),
(785, 11, 'customers_edit'),
(783, 11, 'customers_view'),
(770, 11, 'dashboard_view'),
(775, 11, 'employees_add'),
(777, 11, 'employees_delete'),
(776, 11, 'employees_edit'),
(778, 11, 'employees_salaries'),
(774, 11, 'employees_view'),
(815, 11, 'endday_manage'),
(806, 11, 'expenses_add'),
(808, 11, 'expenses_delete'),
(807, 11, 'expenses_edit'),
(805, 11, 'expenses_view'),
(796, 11, 'inventory_adjust'),
(795, 11, 'inventory_view'),
(801, 11, 'invoices_create'),
(803, 11, 'invoices_delete'),
(802, 11, 'invoices_edit'),
(804, 11, 'invoices_print'),
(800, 11, 'invoices_view'),
(792, 11, 'products_add'),
(794, 11, 'products_delete'),
(793, 11, 'products_edit'),
(791, 11, 'products_view'),
(814, 11, 'reports_appointments'),
(812, 11, 'reports_employees'),
(813, 11, 'reports_expenses'),
(810, 11, 'reports_sales'),
(811, 11, 'reports_services'),
(809, 11, 'reports_view'),
(788, 11, 'services_add'),
(790, 11, 'services_delete'),
(789, 11, 'services_edit'),
(787, 11, 'services_view'),
(773, 11, 'settings_edit'),
(772, 11, 'settings_view'),
(771, 11, 'users_manage');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `appointments`
--
ALTER TABLE `appointments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `customer_id` (`customer_id`),
  ADD KEY `employee_id` (`employee_id`),
  ADD KEY `service_id` (`service_id`),
  ADD KEY `branch_id` (`branch_id`);

--
-- Indexes for table `branches`
--
ALTER TABLE `branches`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `customers`
--
ALTER TABLE `customers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `phone` (`phone`),
  ADD KEY `branch_id` (`branch_id`);

--
-- Indexes for table `customer_visits`
--
ALTER TABLE `customer_visits`
  ADD PRIMARY KEY (`id`),
  ADD KEY `customer_id` (`customer_id`),
  ADD KEY `branch_id` (`branch_id`);

--
-- Indexes for table `employees`
--
ALTER TABLE `employees`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `branch_id` (`branch_id`);

--
-- Indexes for table `employee_attendance`
--
ALTER TABLE `employee_attendance`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `employee_date` (`employee_id`,`date`);

--
-- Indexes for table `employee_salaries`
--
ALTER TABLE `employee_salaries`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `employee_month_year` (`employee_id`,`month`,`year`);

--
-- Indexes for table `end_days`
--
ALTER TABLE `end_days`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `branch_date` (`branch_id`,`date`),
  ADD KEY `closed_by` (`closed_by`);

--
-- Indexes for table `expenses`
--
ALTER TABLE `expenses`
  ADD PRIMARY KEY (`id`),
  ADD KEY `category_id` (`category_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `end_day_id` (`end_day_id`);

--
-- Indexes for table `expense_categories`
--
ALTER TABLE `expense_categories`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `inventory`
--
ALTER TABLE `inventory`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `product_branch` (`product_id`,`branch_id`),
  ADD KEY `branch_id` (`branch_id`);

--
-- Indexes for table `inventory_transactions`
--
ALTER TABLE `inventory_transactions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `branch_id` (`branch_id`);

--
-- Indexes for table `invoices`
--
ALTER TABLE `invoices`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `invoice_number` (`invoice_number`),
  ADD KEY `customer_id` (`customer_id`),
  ADD KEY `cashier_id` (`cashier_id`),
  ADD KEY `employee_id` (`employee_id`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `end_day_id` (`end_day_id`);

--
-- Indexes for table `invoice_items`
--
ALTER TABLE `invoice_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `invoice_id` (`invoice_id`),
  ADD KEY `employee_id` (`employee_id`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `recipient_id` (`recipient_id`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `related_id` (`related_id`);

--
-- Indexes for table `products`
--
ALTER TABLE `products`
  ADD PRIMARY KEY (`id`),
  ADD KEY `category_id` (`category_id`),
  ADD KEY `branch_id` (`branch_id`);

--
-- Indexes for table `product_categories`
--
ALTER TABLE `product_categories`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `promotions`
--
ALTER TABLE `promotions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `branch_id` (`branch_id`);

--
-- Indexes for table `promotion_usage`
--
ALTER TABLE `promotion_usage`
  ADD PRIMARY KEY (`id`),
  ADD KEY `promotion_id` (`promotion_id`),
  ADD KEY `invoice_id` (`invoice_id`),
  ADD KEY `customer_id` (`customer_id`);

--
-- Indexes for table `promo_codes`
--
ALTER TABLE `promo_codes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`),
  ADD KEY `branch_id` (`branch_id`);

--
-- Indexes for table `promo_code_customers`
--
ALTER TABLE `promo_code_customers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `promo_code_customer` (`promo_code_id`,`customer_id`),
  ADD KEY `customer_id` (`customer_id`);

--
-- Indexes for table `promo_code_usage`
--
ALTER TABLE `promo_code_usage`
  ADD PRIMARY KEY (`id`),
  ADD KEY `promo_code_id` (`promo_code_id`),
  ADD KEY `invoice_id` (`invoice_id`),
  ADD KEY `customer_id` (`customer_id`);

--
-- Indexes for table `services`
--
ALTER TABLE `services`
  ADD PRIMARY KEY (`id`),
  ADD KEY `category_id` (`category_id`),
  ADD KEY `branch_id` (`branch_id`);

--
-- Indexes for table `service_categories`
--
ALTER TABLE `service_categories`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `service_employees`
--
ALTER TABLE `service_employees`
  ADD PRIMARY KEY (`service_id`,`employee_id`),
  ADD KEY `employee_id` (`employee_id`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD KEY `branch_id` (`branch_id`);

--
-- Indexes for table `user_permissions`
--
ALTER TABLE `user_permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_permission` (`user_id`,`permission`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `appointments`
--
ALTER TABLE `appointments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `branches`
--
ALTER TABLE `branches`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `customers`
--
ALTER TABLE `customers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3311;

--
-- AUTO_INCREMENT for table `customer_visits`
--
ALTER TABLE `customer_visits`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `employees`
--
ALTER TABLE `employees`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `employee_attendance`
--
ALTER TABLE `employee_attendance`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `employee_salaries`
--
ALTER TABLE `employee_salaries`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=198;

--
-- AUTO_INCREMENT for table `end_days`
--
ALTER TABLE `end_days`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20250477;

--
-- AUTO_INCREMENT for table `expenses`
--
ALTER TABLE `expenses`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3186;

--
-- AUTO_INCREMENT for table `expense_categories`
--
ALTER TABLE `expense_categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `inventory`
--
ALTER TABLE `inventory`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=175;

--
-- AUTO_INCREMENT for table `inventory_transactions`
--
ALTER TABLE `inventory_transactions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=147;

--
-- AUTO_INCREMENT for table `invoices`
--
ALTER TABLE `invoices`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6120;

--
-- AUTO_INCREMENT for table `invoice_items`
--
ALTER TABLE `invoice_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6545;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=589;

--
-- AUTO_INCREMENT for table `products`
--
ALTER TABLE `products`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=97;

--
-- AUTO_INCREMENT for table `product_categories`
--
ALTER TABLE `product_categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `promotions`
--
ALTER TABLE `promotions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `promotion_usage`
--
ALTER TABLE `promotion_usage`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `promo_codes`
--
ALTER TABLE `promo_codes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `promo_code_customers`
--
ALTER TABLE `promo_code_customers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `promo_code_usage`
--
ALTER TABLE `promo_code_usage`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `services`
--
ALTER TABLE `services`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=75;

--
-- AUTO_INCREMENT for table `service_categories`
--
ALTER TABLE `service_categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=54;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `user_permissions`
--
ALTER TABLE `user_permissions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=852;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `appointments`
--
ALTER TABLE `appointments`
  ADD CONSTRAINT `appointments_branch_fk` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `appointments_customer_fk` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `appointments_employee_fk` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `appointments_service_fk` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `customers`
--
ALTER TABLE `customers`
  ADD CONSTRAINT `customers_branch_fk` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `notifications_branch_id_fk` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

--
-- Constraints for table `promotions`
--
ALTER TABLE `promotions`
  ADD CONSTRAINT `promotions_branch_id_fk` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

--
-- Constraints for table `promotion_usage`
--
ALTER TABLE `promotion_usage`
  ADD CONSTRAINT `promotion_usage_customer_id_fk` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `promotion_usage_invoice_id_fk` FOREIGN KEY (`invoice_id`) REFERENCES `invoices` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `promotion_usage_promotion_id_fk` FOREIGN KEY (`promotion_id`) REFERENCES `promotions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `promo_codes`
--
ALTER TABLE `promo_codes`
  ADD CONSTRAINT `promo_codes_branch_id_fk` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

--
-- Constraints for table `promo_code_customers`
--
ALTER TABLE `promo_code_customers`
  ADD CONSTRAINT `promo_code_customers_customer_id_fk` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `promo_code_customers_promo_code_id_fk` FOREIGN KEY (`promo_code_id`) REFERENCES `promo_codes` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `promo_code_usage`
--
ALTER TABLE `promo_code_usage`
  ADD CONSTRAINT `promo_code_usage_customer_id_fk` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `promo_code_usage_invoice_id_fk` FOREIGN KEY (`invoice_id`) REFERENCES `invoices` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `promo_code_usage_promo_code_id_fk` FOREIGN KEY (`promo_code_id`) REFERENCES `promo_codes` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
