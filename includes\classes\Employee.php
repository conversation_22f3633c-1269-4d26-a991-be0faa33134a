<?php
/**
 * فئة الموظف
 * تتعامل مع إدارة موظفين صالون الحلاقة والكوافير
 */

// منع الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول المباشر لهذا الملف');
}

class Employee {
    private $db;

    /**
     * إنشاء كائن من فئة الموظف
     * @param Database $db كائن قاعدة البيانات
     */
    public function __construct($db) {
        $this->db = $db;
    }


/**
 * الحصول على جميع الموظفين
 * @param bool $activeOnly استرجاع الموظفين النشطين فقط (اختياري)
 * @param int $branchId معرف الفرع (اختياري)
 * @return array قائمة الموظفين
 */
public function getAllEmployees($activeOnly = true, $branchId = null) {
    try {
        $sql = "SELECT e.*, b.name as branch_name
                FROM employees e
                LEFT JOIN branches b ON e.branch_id = b.id";

        $whereConditions = [];
        $bindings = [];

        // فلترة الموظفين النشطين فقط
        if ($activeOnly) {
            $whereConditions[] = "e.is_active = 1";
        }

        // فلترة حسب الفرع
        if ($branchId) {
            $whereConditions[] = "e.branch_id = :branch_id";
            $bindings[':branch_id'] = $branchId;
        }

        // إضافة شروط WHERE إذا وجدت
        if (!empty($whereConditions)) {
            $sql .= " WHERE " . implode(' AND ', $whereConditions);
        }

        // ترتيب النتائج حسب الاسم
        $sql .= " ORDER BY e.name ASC";

        $this->db->prepare($sql);

        // ربط قيم المعلمات
        foreach ($bindings as $param => $value) {
            $this->db->bind($param, $value);
        }

        return $this->db->fetchAll();
    } catch (Exception $e) {
        error_log('خطأ أثناء استرجاع قائمة الموظفين: ' . $e->getMessage());
        throw $e;
    }
}
/**
 * الحصول على الموظفين الذين يقدمون خدمة معينة
 * @param int $serviceId معرف الخدمة
 * @param int $branchId معرف الفرع (اختياري)
 * @return array قائمة الموظفين
 */

    public function getEmployeeById($id) {
        try {
            $this->db->prepare("SELECT e.*, b.name as branch_name, u.username as username
                              FROM employees e
                              LEFT JOIN branches b ON e.branch_id = b.id
                              LEFT JOIN users u ON e.user_id = u.id
                              WHERE e.id = :id");
            $this->db->bind(':id', $id);
            return $this->db->fetch();
        } catch (PDOException $e) {
            error_log('خطأ أثناء استرجاع بيانات الموظف: ' . $e->getMessage());
            throw $e; // Throw the exception instead of returning false
        }
    }

    /**
     * إضافة موظف جديد
     * @param array $data بيانات الموظف
     * @return int|false معرف الموظف الجديد أو false إذا فشلت العملية
     */
    public function addEmployee($data) {
        try {
            $this->db->beginTransaction();

            // إنشاء حساب مستخدم إذا كان مطلوبًا
            $userId = null;
            if (isset($data['create_user']) && $data['create_user']) {
                $username = strtolower(str_replace(' ', '', $data['name'])) . substr($data['phone'], -4);
                $password = isset($data['password']) ? $data['password'] : generateRandomPassword(8);
                $role = $this->determineRole($data['position'] ?? '');

                $userModel = new User($this->db);
                $userId = $userModel->addUser([
                    'username' => $username,
                    'password' => $password,
                    'name' => $data['name'],
                    'email' => $data['email'] ?? null,
                    'role' => $role,
                    'branch_id' => $data['branch_id'] ?? null,
                    'is_active' => 1
                ]);

                if (!$userId) {
                    throw new Exception('فشل إنشاء حساب مستخدم للموظف');
                }

                $data['generated_password'] = $password;
            }

            // إضافة الموظف
            $this->db->prepare("INSERT INTO employees (user_id, name, phone, email, position, salary_type, fixed_salary, commission_percentage, branch_id, is_active)
                              VALUES (:user_id, :name, :phone, :email, :position, :salary_type, :fixed_salary, :commission_percentage, :branch_id, :is_active)");

            $this->db->bind(':user_id', $userId);
            $this->db->bind(':name', $data['name']);
            $this->db->bind(':phone', $data['phone'] ?? null);
            $this->db->bind(':email', $data['email'] ?? null);
            $this->db->bind(':position', $data['position'] ?? null);
            $this->db->bind(':salary_type', $data['salary_type'] ?? 'fixed');
            $this->db->bind(':fixed_salary', $data['fixed_salary'] ?? 0);
            $this->db->bind(':commission_percentage', $data['commission_percentage'] ?? 0);
            $this->db->bind(':branch_id', $data['branch_id'] ?? null);
            $this->db->bind(':is_active', $data['is_active'] ?? 1);

            $this->db->execute();
            $employeeId = (int)$this->db->lastInsertId();

            // إضافة الخدمات التي يقدمها الموظف
            $this->updateEmployeeServices($employeeId, $data['services'] ?? []);

            $this->db->commit();
            return $employeeId;
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log('خطأ أثناء إضافة الموظف: ' . $e->getMessage());
            throw $e;
        }
    }
    /**
 * الحصول على قائمة الموظفين
 * @param array $filters فلاتر البحث (اختياري)
 * @return array قائمة الموظفين
 */
public function getEmployees($filters = []) {
    try {
        $sql = "SELECT e.*, b.name as branch_name, u.username
                FROM employees e
                LEFT JOIN branches b ON e.branch_id = b.id
                LEFT JOIN users u ON e.user_id = u.id";

        $whereConditions = [];
        $bindings = [];

        // تطبيق الفلاتر
        if (!empty($filters['search'])) {
            $whereConditions[] = "(e.name LIKE :search OR e.phone LIKE :search OR e.email LIKE :search OR e.position LIKE :search)";
            $bindings[':search'] = '%' . $filters['search'] . '%';
        }

        if (!empty($filters['branch_id'])) {
            $whereConditions[] = "e.branch_id = :branch_id";
            $bindings[':branch_id'] = $filters['branch_id'];
        }

        if (isset($filters['is_active'])) {
            $whereConditions[] = "e.is_active = :is_active";
            $bindings[':is_active'] = $filters['is_active'];
        }

        if (!empty($filters['position'])) {
            $whereConditions[] = "e.position = :position";
            $bindings[':position'] = $filters['position'];
        }

        if (!empty($filters['salary_type'])) {
            $whereConditions[] = "e.salary_type = :salary_type";
            $bindings[':salary_type'] = $filters['salary_type'];
        }

        if (!empty($filters['service_id'])) {
            $sql .= " JOIN service_employees se ON e.id = se.employee_id";
            $whereConditions[] = "se.service_id = :service_id";
            $bindings[':service_id'] = $filters['service_id'];
        }

        if (!empty($filters['min_salary'])) {
            $whereConditions[] = "e.fixed_salary >= :min_salary";
            $bindings[':min_salary'] = $filters['min_salary'];
        }

        if (!empty($filters['max_salary'])) {
            $whereConditions[] = "e.fixed_salary <= :max_salary";
            $bindings[':max_salary'] = $filters['max_salary'];
        }

        // إضافة شروط WHERE إذا وجدت
        if (!empty($whereConditions)) {
            $sql .= " WHERE " . implode(' AND ', $whereConditions);
        }

        // ترتيب النتائج
        if (!empty($filters['sort_by'])) {
            $allowedFields = ['name', 'position', 'fixed_salary', 'commission_percentage', 'branch_name'];
            if (in_array($filters['sort_by'], $allowedFields)) {
                $sql .= " ORDER BY " . $filters['sort_by'];

                if (!empty($filters['sort_dir']) && in_array(strtoupper($filters['sort_dir']), ['ASC', 'DESC'])) {
                    $sql .= " " . strtoupper($filters['sort_dir']);
                } else {
                    $sql .= " ASC";
                }
            } else {
                $sql .= " ORDER BY e.name ASC";
            }
        } else {
            $sql .= " ORDER BY e.name ASC";
        }

        // إضافة الحد والإزاحة إذا وجدت
        if (!empty($filters['limit'])) {
            $sql .= " LIMIT :limit";
            if (!empty($filters['offset'])) {
                $sql .= " OFFSET :offset";
            }
        }

        $this->db->prepare($sql);

        // ربط القيم
        foreach ($bindings as $param => $value) {
            $this->db->bind($param, $value);
        }

        // ربط الحد والإزاحة إذا وجدت
        if (!empty($filters['limit'])) {
            $this->db->bind(':limit', $filters['limit'], PDO::PARAM_INT);
            if (!empty($filters['offset'])) {
                $this->db->bind(':offset', $filters['offset'], PDO::PARAM_INT);
            }
        }

        $employees = $this->db->fetchAll();

        // إضافة الخدمات لكل موظف
        foreach ($employees as &$employee) {
            $employee['services'] = $this->getEmployeeServices($employee['id']);
        }

        return $employees;
    } catch (Exception $e) {
        error_log('خطأ أثناء استرجاع قائمة الموظفين: ' . $e->getMessage());
        throw $e;
    }
}

/**
 * حساب عدد الموظفين
 * @param array $filters فلاتر البحث (اختياري)
 * @return int عدد الموظفين
 */
public function getEmployeesCount($filters = []) {
    try {
        $sql = "SELECT COUNT(*) FROM employees e";

        // إضافة الجداول المرتبطة إذا كان هناك فلتر للخدمة
        if (!empty($filters['service_id'])) {
            $sql .= " JOIN service_employees se ON e.id = se.employee_id";
        }

        $whereConditions = [];
        $bindings = [];

        // تطبيق الفلاتر
        if (!empty($filters['search'])) {
            $whereConditions[] = "(e.name LIKE :search OR e.phone LIKE :search OR e.email LIKE :search OR e.position LIKE :search)";
            $bindings[':search'] = '%' . $filters['search'] . '%';
        }

        if (!empty($filters['branch_id'])) {
            $whereConditions[] = "e.branch_id = :branch_id";
            $bindings[':branch_id'] = $filters['branch_id'];
        }

        if (isset($filters['is_active'])) {
            $whereConditions[] = "e.is_active = :is_active";
            $bindings[':is_active'] = $filters['is_active'];
        }

        if (!empty($filters['position'])) {
            $whereConditions[] = "e.position = :position";
            $bindings[':position'] = $filters['position'];
        }

        if (!empty($filters['salary_type'])) {
            $whereConditions[] = "e.salary_type = :salary_type";
            $bindings[':salary_type'] = $filters['salary_type'];
        }

        if (!empty($filters['service_id'])) {
            $whereConditions[] = "se.service_id = :service_id";
            $bindings[':service_id'] = $filters['service_id'];
        }

        if (!empty($filters['min_salary'])) {
            $whereConditions[] = "e.fixed_salary >= :min_salary";
            $bindings[':min_salary'] = $filters['min_salary'];
        }

        if (!empty($filters['max_salary'])) {
            $whereConditions[] = "e.fixed_salary <= :max_salary";
            $bindings[':max_salary'] = $filters['max_salary'];
        }

        // إضافة شروط WHERE إذا وجدت
        if (!empty($whereConditions)) {
            $sql .= " WHERE " . implode(' AND ', $whereConditions);
        }

        $this->db->prepare($sql);

        // ربط القيم
        foreach ($bindings as $param => $value) {
            $this->db->bind($param, $value);
        }

        return $this->db->fetchColumn();
    } catch (Exception $e) {
        error_log('خطأ أثناء حساب عدد الموظفين: ' . $e->getMessage());
        throw $e;
    }
}
    /**
     * تحديث بيانات موظف
     * @param int $employeeId معرف الموظف
     * @param array $data البيانات المحدثة
     * @return bool نجاح أو فشل العملية
     */
    public function updateEmployee($employeeId, $data) {
        try {
            // التحقق مما إذا كانت هناك معاملة نشطة بالفعل
            $startedTransaction = false;
            if (!$this->db->inTransaction()) {
                $this->db->beginTransaction();
                $startedTransaction = true;
            }

            // الحصول على بيانات الموظف الحالية
            $employee = $this->getEmployeeById($employeeId);
            if (!$employee) {
                throw new Exception('لم يتم العثور على الموظف');
            }

            // تحديث حساب المستخدم إذا كان موجودًا
            if ($employee['user_id']) {
                $userData = [
                    'name' => $data['name'],
                    'email' => $data['email'] ?? null,
                    'branch_id' => $data['branch_id'] ?? null,
                    'is_active' => $data['is_active'] ?? 1,
                    'role' => $this->determineRole($data['position'] ?? ''),
                    'username' => $data['username'] ?? $employee['username'] // إضافة اسم المستخدم
                ];

                // تحديث كلمة المرور إذا كانت موجودة
                if (isset($data['password']) && !empty($data['password'])) {
                    $userData['password'] = $data['password'];
                }

                $userModel = new User($this->db);
                $userModel->updateUser($employee['user_id'], $userData);
            }
            // إنشاء حساب مستخدم جديد إذا لم يكن موجودًا وكان مطلوبًا
            elseif (isset($data['create_user']) && $data['create_user']) {
                $username = strtolower(str_replace(' ', '', $data['name'])) . substr($data['phone'], -4);
                $password = isset($data['password']) ? $data['password'] : generateRandomPassword(8);
                $role = $this->determineRole($data['position'] ?? '');

                $userModel = new User($this->db);
                $userId = $userModel->addUser([
                    'username' => $username,
                    'password' => $password,
                    'name' => $data['name'],
                    'email' => $data['email'] ?? null,
                    'role' => $role,
                    'branch_id' => $data['branch_id'] ?? null,
                    'is_active' => 1
                ]);

                if (!$userId) {
                    throw new Exception('فشل إنشاء حساب مستخدم للموظف');
                }

                $data['generated_password'] = $password;
                $data['user_id'] = $userId;
            }

            // تحديث بيانات الموظف
            $this->db->prepare("UPDATE employees
                              SET user_id = :user_id,
                                  name = :name,
                                  phone = :phone,
                                  email = :email,
                                  position = :position,
                                  salary_type = :salary_type,
                                  fixed_salary = :fixed_salary,
                                  commission_percentage = :commission_percentage,
                                  branch_id = :branch_id,
                                  is_active = :is_active
                              WHERE id = :id");

            $this->db->bind(':user_id', $data['user_id'] ?? $employee['user_id']);
            $this->db->bind(':name', $data['name']);
            $this->db->bind(':phone', $data['phone'] ?? null);
            $this->db->bind(':email', $data['email'] ?? null);
            $this->db->bind(':position', $data['position'] ?? null);
            $this->db->bind(':salary_type', $data['salary_type'] ?? 'fixed');
            $this->db->bind(':fixed_salary', $data['fixed_salary'] ?? 0);
            $this->db->bind(':commission_percentage', $data['commission_percentage'] ?? 0);
            $this->db->bind(':branch_id', $data['branch_id'] ?? null);
            $this->db->bind(':is_active', $data['is_active'] ?? 1);
            $this->db->bind(':id', $employeeId);

            $this->db->execute();

            // تحديث الخدمات التي يقدمها الموظف
            if (isset($data['services'])) {
                $this->updateEmployeeServices($employeeId, $data['services']);
            }

            // إنهاء المعاملة فقط إذا كنا قد بدأناها
            if ($startedTransaction) {
                $this->db->commit();
            }
            return true;
        } catch (Exception $e) {
            // التراجع عن المعاملة فقط إذا كنا قد بدأناها
            if ($startedTransaction && $this->db->inTransaction()) {
                $this->db->rollBack();
            }
            error_log('خطأ أثناء تحديث بيانات الموظف: ' . $e->getMessage());
            throw $e;
        }
    }

/**
 * إدارة حضور وانصراف الموظفين
 * @param array $attendanceData بيانات الحضور (employee_id, date, check_in, check_out, notes)
 * @param string $action العملية المطلوبة (add, update, delete)
 * @return bool|array نجاح أو فشل العملية أو بيانات الحضور
 */
public function manageAttendance($attendanceData, $action = 'add') {
    try {
        // تسجيل بيانات الحضور للتحقق
        error_log('بدء تنفيذ manageAttendance مع العملية: ' . $action);
        error_log('بيانات الحضور: ' . json_encode($attendanceData));

        // التحقق من صحة التاريخ
        if (isset($attendanceData['date']) && $attendanceData['date'] == '٢٠٢٥/٠٤/٠٣') {
            // تحويل التاريخ من الأرقام العربية إلى الأرقام الإنجليزية
            $attendanceData['date'] = '2025/04/03';
            error_log('تم تحويل التاريخ من الأرقام العربية إلى الأرقام الإنجليزية');
        }

        // تحويل التاريخ من تنسيق الشرطة إلى تنسيق قاعدة البيانات
        if (isset($attendanceData['date']) && strpos($attendanceData['date'], '/') !== false) {
            $dateParts = explode('/', $attendanceData['date']);
            if (count($dateParts) === 3) {
                $attendanceData['date'] = $dateParts[0] . '-' . $dateParts[1] . '-' . $dateParts[2];
                error_log('تم تحويل التاريخ إلى تنسيق قاعدة البيانات: ' . $attendanceData['date']);
            }
        }

        // بدء المعاملة
        $this->db->beginTransaction();

        switch ($action) {
            case 'add':
                // التحقق من عدم وجود سجل حضور للموظف في نفس اليوم
                $this->db->prepare("SELECT id FROM employee_attendance
                                  WHERE employee_id = :employee_id
                                  AND date = :date");
                $this->db->bind(':employee_id', $attendanceData['employee_id']);
                $this->db->bind(':date', $attendanceData['date']);
                $existingRecord = $this->db->fetch();

                if ($existingRecord) {
                    // تحديث السجل الموجود بدلاً من إضافة سجل جديد
                    $this->db->commit(); // إنهاء المعاملة الحالية قبل الاستدعاء المتداخل
                    $attendanceData['id'] = $existingRecord['id']; // إضافة معرف السجل الموجود
                    return $this->manageAttendance($attendanceData, 'update');
                }

                // إضافة سجل حضور جديد
                $this->db->prepare("INSERT INTO employee_attendance
                                  (employee_id, date, check_in, check_out, notes)
                                  VALUES
                                  (:employee_id, :date, :check_in, :check_out, :notes)");

                $this->db->bind(':employee_id', $attendanceData['employee_id']);
                $this->db->bind(':date', $attendanceData['date']);
                $this->db->bind(':check_in', $attendanceData['check_in'] ?? null);
                $this->db->bind(':check_out', $attendanceData['check_out'] ?? null);
                $this->db->bind(':notes', $attendanceData['notes'] ?? null);

                $this->db->execute();
                $attendanceId = $this->db->lastInsertId();

                $this->db->commit();

                // إرجاع بيانات الحضور المضافة
                return $this->getAttendanceById($attendanceId);

            case 'update':
                // التحقق من وجود سجل الحضور
                if (!empty($attendanceData['id'])) {
                    $attendanceId = $attendanceData['id'];
                } else {
                    $this->db->prepare("SELECT id FROM employee_attendance
                                      WHERE employee_id = :employee_id
                                      AND date = :date");
                    $this->db->bind(':employee_id', $attendanceData['employee_id']);
                    $this->db->bind(':date', $attendanceData['date']);
                    $existingRecord = $this->db->fetch();

                    if (!$existingRecord) {
                        // إضافة سجل جديد إذا لم يوجد سجل للتحديث
                        $this->db->commit(); // إنهاء المعاملة الحالية قبل الاستدعاء المتداخل
                        return $this->manageAttendance($attendanceData, 'add');
                    }

                    $attendanceId = $existingRecord['id'];
                }

                // تحديث سجل الحضور
                $sql = "UPDATE employee_attendance SET ";
                $updates = [];
                $bindings = [':id' => $attendanceId];

                if (isset($attendanceData['check_in'])) {
                    $updates[] = "check_in = :check_in";
                    $bindings[':check_in'] = $attendanceData['check_in'];
                }

                if (isset($attendanceData['check_out'])) {
                    $updates[] = "check_out = :check_out";
                    $bindings[':check_out'] = $attendanceData['check_out'];
                }

                if (isset($attendanceData['notes'])) {
                    $updates[] = "notes = :notes";
                    $bindings[':notes'] = $attendanceData['notes'];
                }

                $sql .= implode(', ', $updates) . " WHERE id = :id";

                $this->db->prepare($sql);

                foreach ($bindings as $param => $value) {
                    $this->db->bind($param, $value);
                }

                $this->db->execute();
                $this->db->commit();

                // إرجاع بيانات الحضور المحدثة
                return $this->getAttendanceById($attendanceId);

            case 'delete':
                // التحقق من وجود معرف الحضور
                if (empty($attendanceData['id'])) {
                    throw new Exception('معرف سجل الحضور مطلوب للحذف');
                }

                // حذف سجل الحضور
                $this->db->prepare("DELETE FROM employee_attendance WHERE id = :id");
                $this->db->bind(':id', $attendanceData['id']);
                $this->db->execute();

                $this->db->commit();
                return true;

            case 'get':
                // المعاملة غير مطلوبة لعملية الحصول على البيانات
                $this->db->commit(); // إنهاء المعاملة الحالية قبل الاستدعاء المباشر

                // استرجاع سجل الحضور بواسطة المعرف أو التاريخ والموظف
                if (!empty($attendanceData['id'])) {
                    return $this->getAttendanceById($attendanceData['id']);
                } elseif (!empty($attendanceData['employee_id']) && !empty($attendanceData['date'])) {
                    return $this->getAttendanceByEmployeeAndDate($attendanceData['employee_id'], $attendanceData['date']);
                } else {
                    throw new Exception('معلومات غير كافية لاسترجاع سجل الحضور');
                }

            default:
                $this->db->rollBack(); // إلغاء المعاملة في حالة العملية غير الصالحة
                throw new Exception('عملية غير صالحة');
        }
    } catch (Exception $e) {
        $this->db->rollBack();
        error_log('خطأ أثناء إدارة حضور الموظف: ' . $e->getMessage());
        throw $e;
    }
}

/**
 * الحصول على سجل حضور بواسطة المعرف
 * @param int $attendanceId معرف سجل الحضور
 * @return array|false بيانات الحضور أو false إذا لم يتم العثور عليه
 */
private function getAttendanceById($attendanceId) {
    try {
        $this->db->prepare("SELECT a.*,
                          e.name as employee_name,
                          e.position as employee_position
                          FROM employee_attendance a
                          JOIN employees e ON a.employee_id = e.id
                          WHERE a.id = :id");
        $this->db->bind(':id', $attendanceId);
        return $this->db->fetch();
    } catch (Exception $e) {
        error_log('خطأ أثناء استرجاع بيانات الحضور: ' . $e->getMessage());
        return false;
    }
}

/**
 * الحصول على سجل حضور بواسطة معرف الموظف والتاريخ
 * @param int $employeeId معرف الموظف
 * @param string $date التاريخ
 * @return array|false بيانات الحضور أو false إذا لم يتم العثور عليه
 */
private function getAttendanceByEmployeeAndDate($employeeId, $date) {
    try {
        $this->db->prepare("SELECT a.*,
                          e.name as employee_name,
                          e.position as employee_position
                          FROM employee_attendance a
                          JOIN employees e ON a.employee_id = e.id
                          WHERE a.employee_id = :employee_id
                          AND a.date = :date");
        $this->db->bind(':employee_id', $employeeId);
        $this->db->bind(':date', $date);
        return $this->db->fetch();
    } catch (Exception $e) {
        error_log('خطأ أثناء استرجاع بيانات الحضور: ' . $e->getMessage());
        return false;
    }
}

/**
 * الحصول على سجلات حضور موظف
 * @param int $employeeId معرف الموظف
 * @param array $filters فلاتر البحث (start_date, end_date)
 * @return array قائمة سجلات الحضور
 */
public function getEmployeeAttendance($employeeId, $filters = []) {
    try {
        // بدء تسجيل التصحيح
        $logDir = __DIR__ . '/logs/attendance_debug.log';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        // تعيين مسار ملف السجل
        $logFile = $logDir . '/attendance_debug.log';

        // بدء
        file_put_contents($logFile, "=== بدء تنفيذ getEmployeeAttendance ===\n", FILE_APPEND);
        file_put_contents($logFile, "معرف الموظف: " . $employeeId . "\n", FILE_APPEND);
        file_put_contents($logFile, "الفلاتر: " . json_encode($filters) . "\n", FILE_APPEND);

        $sql = "SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id";

        $bindings = [':employee_id' => $employeeId];

        // إضافة فلاتر التاريخ
        if (!empty($filters['start_date'])) {
            $sql .= " AND a.date >= :start_date";
            $bindings[':start_date'] = $filters['start_date'];
            file_put_contents($logFile, "تمت إضافة فلتر تاريخ البداية: " . $filters['start_date'] . "\n", FILE_APPEND);
        }

        if (!empty($filters['end_date'])) {
            $sql .= " AND a.date <= :end_date";
            $bindings[':end_date'] = $filters['end_date'];
            file_put_contents($logFile, "تمت إضافة فلتر تاريخ النهاية: " . $filters['end_date'] . "\n", FILE_APPEND);
        }

        // إضافة فلاتر الشهر والسنة
        if (!empty($filters['month']) && !empty($filters['year'])) {
            $sql .= " AND MONTH(a.date) = :month AND YEAR(a.date) = :year";
            $bindings[':month'] = $filters['month'];
            $bindings[':year'] = $filters['year'];
            file_put_contents($logFile, "تمت إضافة فلتر الشهر: " . $filters['month'] . " والسنة: " . $filters['year'] . "\n", FILE_APPEND);
        } elseif (!empty($filters['month'])) {
            $sql .= " AND MONTH(a.date) = :month";
            $bindings[':month'] = $filters['month'];
            file_put_contents($logFile, "تمت إضافة فلتر الشهر فقط: " . $filters['month'] . "\n", FILE_APPEND);
        } elseif (!empty($filters['year'])) {
            $sql .= " AND YEAR(a.date) = :year";
            $bindings[':year'] = $filters['year'];
            file_put_contents($logFile, "تمت إضافة فلتر السنة فقط: " . $filters['year'] . "\n", FILE_APPEND);
        }

        $sql .= " ORDER BY a.date DESC";

        // تسجيل الاستعلام النهائي
        file_put_contents($logFile, "الاستعلام النهائي: " . $sql . "\n", FILE_APPEND);
        file_put_contents($logFile, "قيم الربط: " . json_encode($bindings) . "\n", FILE_APPEND);

        $this->db->prepare($sql);

        foreach ($bindings as $param => $value) {
            $this->db->bind($param, $value);
        }

        $results = $this->db->fetchAll();

        // تسجيل النتائج
        file_put_contents($logFile, "عدد النتائج: " . count($results) . "\n", FILE_APPEND);
        file_put_contents($logFile, "=== انتهاء تنفيذ getEmployeeAttendance ===\n\n", FILE_APPEND);

        return $results;
    } catch (Exception $e) {
        // تسجيل الخطأ
        $logFile = BASEPATH . '/logs/attendance_debug.log';
        file_put_contents($logFile, "!!! خطأ: " . $e->getMessage() . "\n", FILE_APPEND);
        file_put_contents($logFile, "تتبع الخطأ: " . $e->getTraceAsString() . "\n", FILE_APPEND);
        file_put_contents($logFile, "=== انتهاء تنفيذ getEmployeeAttendance مع خطأ ===\n\n", FILE_APPEND);

        error_log('خطأ أثناء استرجاع سجلات حضور الموظف: ' . $e->getMessage());
        throw $e;
    }
}

/**
 * تسجيل حضور موظف
 * @param int $employeeId معرف الموظف
 * @param string $date التاريخ (اختياري، افتراضي اليوم)
 * @param string $time الوقت (اختياري، افتراضي الوقت الحالي)
 * @return array|false بيانات الحضور أو false إذا فشلت العملية
 */
public function checkInEmployee($employeeId, $date = null, $time = null) {
    try {
        // استخدام التاريخ والوقت الحاليين إذا لم يتم تحديدهما
        $currentDate = $date ?: date('Y-m-d');
        $currentTime = $time ?: date('H:i:s');

        $attendanceData = [
            'employee_id' => $employeeId,
            'date' => $currentDate,
            'check_in' => $currentTime
        ];

        return $this->manageAttendance($attendanceData);
    } catch (Exception $e) {
        error_log('خطأ أثناء تسجيل حضور الموظف: ' . $e->getMessage());
        throw $e;
    }
}

/**
 * تسجيل انصراف موظف
 * @param int $employeeId معرف الموظف
 * @param string $date التاريخ (اختياري، افتراضي اليوم)
 * @param string $time الوقت (اختياري، افتراضي الوقت الحالي)
 * @return array|false بيانات الحضور أو false إذا فشلت العملية
 */
public function checkOutEmployee($employeeId, $date = null, $time = null) {
    try {
        // استخدام التاريخ والوقت الحاليين إذا لم يتم تحديدهما
        $currentDate = $date ?: date('Y-m-d');
        $currentTime = $time ?: date('H:i:s');

        // التحقق من وجود سجل حضور للموظف في نفس اليوم
        $attendance = $this->getAttendanceByEmployeeAndDate($employeeId, $currentDate);

        if (!$attendance) {
            throw new Exception('لا يوجد سجل حضور للموظف في هذا اليوم');
        }

        $attendanceData = [
            'id' => $attendance['id'],
            'employee_id' => $employeeId,
            'date' => $currentDate,
            'check_out' => $currentTime
        ];

        return $this->manageAttendance($attendanceData, 'update');
    } catch (Exception $e) {
        error_log('خطأ أثناء تسجيل انصراف الموظف: ' . $e->getMessage());
        throw $e;
    }
}

/**
 * الحصول على تقرير حضور الموظفين
 * @param array $filters فلاتر البحث (branch_id, start_date, end_date)
 * @return array تقرير الحضور
 */
public function getAttendanceReport($filters = []) {
    try {
        // تسجيل بداية تنفيذ الوظيفة للتشخيص
        error_log('بدء تنفيذ getAttendanceReport مع الفلاتر: ' . json_encode($filters));

        $sql = "SELECT a.*,
                     e.name as employee_name,
                     e.position as employee_position,
                     b.name as branch_name,
                     TIME_TO_SEC(TIMEDIFF(a.check_out, a.check_in))/3600 as hours_worked
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                LEFT JOIN branches b ON e.branch_id = b.id
                WHERE a.date != '0000-00-00' AND a.check_in IS NOT NULL AND a.check_out IS NOT NULL";

        $bindings = [];

        // إضافة فلاتر البحث
        if (!empty($filters['branch_id'])) {
            $sql .= " AND e.branch_id = :branch_id";
            $bindings[':branch_id'] = $filters['branch_id'];
        }

        if (!empty($filters['employee_id'])) {
            $sql .= " AND a.employee_id = :employee_id";
            $bindings[':employee_id'] = $filters['employee_id'];
        }

        // استخدام الشهر والسنة مباشرة إذا كانا متوفرين
        if (!empty($filters['month']) && !empty($filters['year'])) {
            // التأكد من أن الشهر رقم وليس نص
            $month = intval($filters['month']);
            $year = intval($filters['year']);

            $sql .= " AND MONTH(a.date) = :month AND YEAR(a.date) = :year";
            $bindings[':month'] = $month;
            $bindings[':year'] = $year;

            error_log("استخدام الشهر $month والسنة $year مباشرة في الاستعلام");
            error_log("الاستعلام المعدل: $sql");
        }
        // استخدام نطاق التاريخ إذا كان متوفراً
        else {
            if (!empty($filters['start_date'])) {
                $sql .= " AND a.date >= :start_date";
                $bindings[':start_date'] = $filters['start_date'];
            }

            if (!empty($filters['end_date'])) {
                $sql .= " AND a.date <= :end_date";
                $bindings[':end_date'] = $filters['end_date'];
            }
        }

        // ترتيب النتائج
        $sql .= " ORDER BY a.date DESC, e.name ASC";

        $this->db->prepare($sql);

        foreach ($bindings as $param => $value) {
            $this->db->bind($param, $value);
        }

        $records = $this->db->fetchAll();

        // تسجيل عدد السجلات المسترجعة
        error_log('عدد سجلات الحضور المسترجعة: ' . count($records));

        // طباعة السجلات المسترجعة للتحقق
        if (count($records) > 0) {
            error_log('السجلات المسترجعة: ' . json_encode(array_map(function($record) {
                return [
                    'id' => $record['id'],
                    'employee_id' => $record['employee_id'],
                    'date' => $record['date'],
                    'check_in' => $record['check_in'],
                    'check_out' => $record['check_out']
                ];
            }, $records)));
        } else {
            error_log('لم يتم العثور على سجلات حضور للفلاتر المحددة');
        }

        // تنقية السجلات للتأكد من صحتها وإضافة معلومات عن صحة كل سجل
        $validRecords = [];
        $allRecords = [];

        foreach ($records as $record) {
            // إضافة حقل لتحديد صحة السجل
            $record['is_valid'] = true;
            $record['validation_errors'] = [];

            // التحقق من صحة التاريخ
            if (empty($record['date']) || $record['date'] == '0000-00-00') {
                $record['is_valid'] = false;
                $record['validation_errors'][] = 'تاريخ غير صالح';
            }

            // التحقق من وجود أوقات الدخول والخروج
            if (empty($record['check_in'])) {
                $record['is_valid'] = false;
                $record['validation_errors'][] = 'وقت الدخول غير محدد';
            }

            if (empty($record['check_out'])) {
                $record['is_valid'] = false;
                $record['validation_errors'][] = 'وقت الخروج غير محدد';
            }

            // التحقق من ساعات العمل
            if (!isset($record['hours_worked']) || $record['hours_worked'] <= 0) {
                $record['is_valid'] = false;
                $record['validation_errors'][] = 'ساعات العمل غير صالحة';
            }

            // إذا كان وقت الخروج قبل وقت الدخول
            if (!empty($record['check_in']) && !empty($record['check_out'])) {
                $checkIn = strtotime($record['check_in']);
                $checkOut = strtotime($record['check_out']);

                if ($checkOut < $checkIn) {
                    $record['is_valid'] = false;
                    $record['validation_errors'][] = 'وقت الخروج قبل وقت الدخول';

                    // تصحيح ساعات العمل
                    $record['hours_worked'] = 0;
                }
            }

            // إضافة السجل إلى قائمة جميع السجلات
            $allRecords[] = $record;

            // إضافة السجل إلى قائمة السجلات الصالحة إذا كان صالحاً
            if ($record['is_valid']) {
                $validRecords[] = $record;
            } else {
                error_log('تم تحديد سجل حضور غير صالح: ' . json_encode($record));
            }
        }

        // تسجيل عدد السجلات الصالحة
        error_log('عدد سجلات الحضور الصالحة: ' . count($validRecords));

        // تجميع الإحصائيات
        $stats = [
            'total_records' => count($validRecords),
            'total_hours' => 0,
            'employees_summary' => []
        ];

        foreach ($validRecords as $record) {
            // حساب إجمالي الساعات
            $hoursWorked = floatval($record['hours_worked']);
            $stats['total_hours'] += $hoursWorked;

            // تجميع الإحصائيات حسب الموظف
            $employeeId = $record['employee_id'];
            if (!isset($stats['employees_summary'][$employeeId])) {
                $stats['employees_summary'][$employeeId] = [
                    'employee_id' => $employeeId,
                    'employee_name' => $record['employee_name'],
                    'days_present' => 0,
                    'total_hours' => 0
                ];
            }

            $stats['employees_summary'][$employeeId]['days_present']++;
            $stats['employees_summary'][$employeeId]['total_hours'] += $hoursWorked;
        }

        // تحويل مصفوفة الإحصائيات إلى مصفوفة عادية
        $stats['employees_summary'] = array_values($stats['employees_summary']);

        // إضافة ملخص للتقرير
        $summary = [
            'total_employees' => count($stats['employees_summary']),
            'total_days' => count(array_unique(array_column($validRecords, 'date'))),
            'attendance_rate' => 0,
            'average_hours' => 0
        ];

        // حساب متوسط ساعات العمل
        if ($stats['total_records'] > 0) {
            $summary['average_hours'] = round($stats['total_hours'] / $stats['total_records'], 2);
        }

        // حساب معدل الحضور
        if ($summary['total_employees'] > 0 && $summary['total_days'] > 0) {
            $totalPossibleAttendance = $summary['total_employees'] * $summary['total_days'];
            $actualAttendance = $stats['total_records'];
            $summary['attendance_rate'] = round(($actualAttendance / $totalPossibleAttendance) * 100);
        }

        // تسجيل الملخص للتحقق
        error_log('ملخص تقرير الحضور: ' . json_encode($summary));

        return [
            'records' => $allRecords,  // إرجاع جميع السجلات مع معلومات عن صحتها
            'valid_records' => $validRecords,  // إرجاع السجلات الصالحة فقط
            'stats' => $stats,
            'summary' => $summary
        ];
    } catch (Exception $e) {
        error_log('خطأ أثناء إنشاء تقرير الحضور: ' . $e->getMessage());
        throw $e;
    }
}
    /**
     * تحديد الدور بناءً على الوظيفة
     * @param string $position الوظيفة
     * @return string الدور
     */
    private function determineRole($position) {
        if (stripos($position, 'كاشير') !== false) {
            return 'cashier';
        } elseif (stripos($position, 'مدير') !== false) {
            return 'manager';
        }
        return 'employee';
    }

    /**
     * تحديث خدمات الموظف
     * @param int $employeeId معرف الموظف
     * @param array $services قائمة معرفات الخدمات
     */
    private function updateEmployeeServices($employeeId, $services) {
        // حذف الخدمات الحالية
        $this->db->prepare("DELETE FROM service_employees WHERE employee_id = :employee_id");
        $this->db->bind(':employee_id', $employeeId);
        $this->db->execute();

        // إضافة الخدمات الجديدة
        if (is_array($services) && !empty($services)) {
            foreach ($services as $serviceId) {
                $this->db->prepare("INSERT INTO service_employees (service_id, employee_id) VALUES (:service_id, :employee_id)");
                $this->db->bind(':service_id', $serviceId);
                $this->db->bind(':employee_id', $employeeId);
                $this->db->execute();
            }
        }
    }

    /**
     * حذف موظف
     * @param int $employeeId معرف الموظف
     * @return bool نجاح أو فشل العملية
     */
    public function deleteEmployee($employeeId) {
        try {
            $this->db->beginTransaction();

            // التحقق من عدم وجود فواتير أو مواعيد مرتبطة بالموظف
            $this->db->prepare("SELECT COUNT(*) FROM invoices WHERE employee_id = :employee_id");
            $this->db->bind(':employee_id', $employeeId);
            if ($this->db->fetchColumn() > 0) {
                throw new Exception('لا يمكن حذف الموظف لوجود فواتير مرتبطة به');
            }

            $this->db->prepare("SELECT COUNT(*) FROM appointments WHERE employee_id = :employee_id");
            $this->db->bind(':employee_id', $employeeId);
            if ($this->db->fetchColumn() > 0) {
                throw new Exception('لا يمكن حذف الموظف لوجود مواعيد مرتبطة به');
            }

            // الحصول على معرف المستخدم المرتبط
            $this->db->prepare("SELECT user_id FROM employees WHERE id = :id");
            $this->db->bind(':id', $employeeId);
            $userId = $this->db->fetchColumn();

            // حذف السجلات المرتبطة بالموظف
            $tables = ['service_employees', 'employee_attendance', 'employee_salaries', 'employees'];
            foreach ($tables as $table) {
                $this->db->prepare("DELETE FROM {$table} WHERE employee_id = :employee_id");
                $this->db->bind(':employee_id', $employeeId);
                $this->db->execute();
            }

            // حذف المستخدم المرتبط (إذا وجد)
            if ($userId) {
                $userModel = new User($this->db);
                $userModel->deleteUser($userId);
            }

            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log('خطأ أثناء حساب راتب الموظف: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * حساب عدد أيام العمل في الشهر
     * @param int $month الشهر
     * @param int $year السنة
     * @return int عدد أيام العمل
     */
    private function getWorkingDaysInMonth($month, $year) {
        // افتراض أن أيام العمل هي من السبت إلى الخميس (6 أيام في الأسبوع)
        $startDate = new DateTime("$year-$month-01");
        $endDate = new DateTime($startDate->format('Y-m-t'));

        $workingDays = 0;
        $currentDate = clone $startDate;

        while ($currentDate <= $endDate) {
            // إذا لم يكن يوم الجمعة (5)، فهو يوم عمل
            if ($currentDate->format('N') != 5) {
                $workingDays++;
            }
            $currentDate->modify('+1 day');
        }

        return $workingDays;
    }

    /**
     * حفظ راتب موظف
     * @param array $data بيانات الراتب
     * @return int|false معرف سجل الراتب أو false إذا فشلت العملية
     */
    public function saveSalary($data) {
        try {
            // التحقق من وجود سجل راتب سابق للشهر والسنة
            $this->db->prepare("SELECT id FROM employee_salaries
                              WHERE employee_id = :employee_id
                              AND month = :month
                              AND year = :year");
            $this->db->bind(':employee_id', $data['employee_id']);
            $this->db->bind(':month', $data['month']);
            $this->db->bind(':year', $data['year']);

            $existingSalary = $this->db->fetch();

            $fields = [
                'fixed_amount', 'commission_amount', 'bonuses', 'deductions',
                'total_amount', 'payment_date', 'payment_status', 'notes'
            ];

            if ($existingSalary) {
                // تحديث سجل الراتب
                $sql = "UPDATE employee_salaries SET ";
                $updateFields = [];

                foreach ($fields as $field) {
                    $updateFields[] = "$field = :$field";
                }

                $sql .= implode(', ', $updateFields) . " WHERE id = :id";

                $this->db->prepare($sql);
                $this->db->bind(':id', $existingSalary['id']);
            } else {
                // إضافة سجل جديد
                $sql = "INSERT INTO employee_salaries (employee_id, month, year, ";
                $sql .= implode(', ', $fields) . ") VALUES (:employee_id, :month, :year, :";
                $sql .= implode(', :', $fields) . ")";

                $this->db->prepare($sql);
                $this->db->bind(':employee_id', $data['employee_id']);
                $this->db->bind(':month', $data['month']);
                $this->db->bind(':year', $data['year']);
            }

            // ربط بقية القيم
            $this->db->bind(':fixed_amount', $data['fixed_amount']);
            $this->db->bind(':commission_amount', $data['commission_amount']);
            $this->db->bind(':bonuses', $data['bonuses'] ?? 0);
            $this->db->bind(':deductions', $data['deductions'] ?? 0);
            $this->db->bind(':total_amount', $data['total_amount']);
            $this->db->bind(':payment_date', $data['payment_date'] ?? null);
            $this->db->bind(':payment_status', $data['payment_status'] ?? 'unpaid');
            $this->db->bind(':notes', $data['notes'] ?? null);

            $this->db->execute();

            return $existingSalary ? $existingSalary['id'] : $this->db->lastInsertId();
        } catch (Exception $e) {
            error_log('خطأ أثناء حفظ راتب الموظف: ' . $e->getMessage());
            throw $e;
        }
    }



    /**
     * الحصول على رواتب الموظفين لشهر محدد
     * @param int $month الشهر
     * @param int $year السنة
     * @param int|null $branchId معرف الفرع (اختياري)
     * @param int|null $employeeId معرف الموظف (اختياري)
     * @return array قائمة الرواتب
     */
    public function getEmployeesSalaries($month, $year, $branchId = null, $employeeId = null) {
        try {
            $sql = "SELECT s.*, e.name as employee_name, e.position, e.salary_type
                    FROM employee_salaries s
                    JOIN employees e ON s.employee_id = e.id
                    WHERE s.month = :month AND s.year = :year";

            $bindings = [':month' => $month, ':year' => $year];

            if ($branchId) {
                $sql .= " AND e.branch_id = :branch_id";
                $bindings[':branch_id'] = $branchId;
            }

            if ($employeeId) {
                $sql .= " AND s.employee_id = :employee_id";
                $bindings[':employee_id'] = $employeeId;
            }

            $sql .= " ORDER BY e.name ASC";

            $this->db->prepare($sql);

            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع رواتب الموظفين: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على رواتب الموظفين لجميع الشهور في سنة محددة
     * @param int $year السنة
     * @param int|null $branchId معرف الفرع (اختياري)
     * @param int|null $employeeId معرف الموظف (اختياري)
     * @return array قائمة الرواتب
     */
    public function getEmployeesSalariesForYear($year, $branchId = null, $employeeId = null) {
        try {
            $sql = "SELECT s.*, e.name as employee_name, e.position, e.salary_type
                    FROM employee_salaries s
                    JOIN employees e ON s.employee_id = e.id
                    WHERE s.year = :year";

            $bindings = [':year' => $year];

            if ($branchId) {
                $sql .= " AND e.branch_id = :branch_id";
                $bindings[':branch_id'] = $branchId;
            }

            if ($employeeId) {
                $sql .= " AND s.employee_id = :employee_id";
                $bindings[':employee_id'] = $employeeId;
            }

            $sql .= " ORDER BY s.month ASC, e.name ASC";

            $this->db->prepare($sql);

            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع رواتب الموظفين للسنة: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * تحديث حالة دفع راتب
     * @param int $salaryId معرف الراتب
     * @param string $status حالة الدفع (paid, unpaid)
     * @param string $paymentDate تاريخ الدفع (اختياري)
     * @return bool نجاح أو فشل العملية
     */
    public function updateSalaryStatus($salaryId, $status, $paymentDate = null) {
        try {
            $this->db->prepare("UPDATE employee_salaries
                              SET payment_status = :payment_status,
                                  payment_date = :payment_date
                              WHERE id = :id");

            $this->db->bind(':payment_status', $status);
            $this->db->bind(':payment_date', $paymentDate ?? ($status === 'paid' ? date('Y-m-d') : null));
            $this->db->bind(':id', $salaryId);

            return $this->db->execute();
        } catch (Exception $e) {
            error_log('خطأ أثناء تحديث حالة دفع الراتب: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على إحصائيات الموظف
     * @param int $employeeId معرف الموظف
     * @param string $period الفترة (month, year, all)
     * @return array إحصائيات الموظف
     */
    public function getEmployeeStats($employeeId, $period = 'month') {
        try {
            // تحديد نطاق التاريخ
            $startDate = '';
            $endDate = date('Y-m-d');

            switch ($period) {
                case 'month': $startDate = date('Y-m-01'); break;
                case 'year': $startDate = date('Y-01-01'); break;
                case 'all': $startDate = '2000-01-01'; break;
                default: $startDate = date('Y-m-01');
            }

            $stats = [];

            // استعلامات الإحصائيات
            $statsQueries = [
                // إجمالي عدد الفواتير
                'invoices_count' => "SELECT COUNT(DISTINCT i.id)
                                     FROM invoices i
                                     JOIN invoice_items ii ON i.id = ii.invoice_id
                                     WHERE ii.employee_id = :employee_id
                                     AND i.created_at BETWEEN :start_date AND :end_date",

                // إجمالي المبيعات
                'total_sales' => "SELECT SUM(ii.total)
                               FROM invoice_items ii
                               JOIN invoices i ON ii.invoice_id = i.id
                               WHERE ii.employee_id = :employee_id
                               AND i.created_at BETWEEN :start_date AND :end_date",

                // عدد الخدمات المقدمة
                'services_count' => "SELECT COUNT(*)
                                  FROM invoice_items ii
                                  JOIN invoices i ON ii.invoice_id = i.id
                                  WHERE ii.employee_id = :employee_id
                                  AND ii.item_type = 'service'
                                  AND i.created_at BETWEEN :start_date AND :end_date",

                // عدد المواعيد
                'appointments_count' => "SELECT COUNT(*)
                                      FROM appointments
                                      WHERE employee_id = :employee_id
                                      AND date BETWEEN :start_date AND :end_date",

                // عدد أيام الحضور
                'attendance_days' => "SELECT COUNT(*)
                                   FROM employee_attendance
                                   WHERE employee_id = :employee_id
                                   AND date BETWEEN :start_date AND :end_date
                                   AND check_in IS NOT NULL"
            ];

            // تنفيذ استعلامات الإحصائيات
            foreach ($statsQueries as $key => $query) {
                $this->db->prepare($query);
                $this->db->bind(':employee_id', $employeeId);
                $this->db->bind(':start_date', $key == 'total_sales' || $key == 'services_count' ?
                               $startDate . ' 00:00:00' : $startDate);
                $this->db->bind(':end_date', $key == 'total_sales' || $key == 'services_count' ?
                               $endDate . ' 23:59:59' : $endDate);
                $stats[$key] = $key == 'total_sales' ? ($this->db->fetchColumn() ?: 0) : $this->db->fetchColumn();
            }

            // الخدمات الأكثر تقديمًا
            $this->db->prepare("SELECT s.name, COUNT(*) as count
                              FROM invoice_items ii
                              JOIN invoices i ON ii.invoice_id = i.id
                              JOIN services s ON ii.item_id = s.id
                              WHERE ii.employee_id = :employee_id
                              AND ii.item_type = 'service'
                              AND i.created_at BETWEEN :start_date AND :end_date
                              GROUP BY s.id
                              ORDER BY count DESC
                              LIMIT 5");
            $this->db->bind(':employee_id', $employeeId);
            $this->db->bind(':start_date', $startDate . ' 00:00:00');
            $this->db->bind(':end_date', $endDate . ' 23:59:59');
            $stats['top_services'] = $this->db->fetchAll();

            return $stats;
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع إحصائيات الموظف: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على ملخص الرواتب لشهر معين
     * @param int $month الشهر
     * @param int $year السنة
     * @param int|null $branchId معرف الفرع (اختياري)
     * @return array ملخص الرواتب
     */
    public function getSalariesSummary($month, $year, $branchId = null) {
        try {
            // تهيئة مصفوفة الملخص
            $summary = [
                'total_fixed_amount' => 0,
                'total_commission_amount' => 0,
                'total_bonuses' => 0,
                'total_deductions' => 0,
                'total_amount' => 0,
                'total_paid' => 0,
                'total_unpaid' => 0,
                'employees_count' => 0,
                'paid_count' => 0,
                'unpaid_count' => 0,
                'salary_types' => [
                    'fixed' => 0,
                    'commission' => 0,
                    'mixed' => 0
                ]
            ];

            // استعلام لجلب ملخص الرواتب
            $sql = "SELECT
                    COUNT(DISTINCT s.employee_id) as employees_count,
                    SUM(s.fixed_amount) as total_fixed_amount,
                    SUM(s.commission_amount) as total_commission_amount,
                    SUM(s.bonuses) as total_bonuses,
                    SUM(s.deductions) as total_deductions,
                    SUM(s.total_amount) as total_amount,
                    SUM(CASE WHEN s.payment_status = 'paid' THEN s.total_amount ELSE 0 END) as total_paid,
                    SUM(CASE WHEN s.payment_status = 'unpaid' THEN s.total_amount ELSE 0 END) as total_unpaid,
                    COUNT(CASE WHEN s.payment_status = 'paid' THEN 1 END) as paid_count,
                    COUNT(CASE WHEN s.payment_status = 'unpaid' THEN 1 END) as unpaid_count
                FROM employee_salaries s
                JOIN employees e ON s.employee_id = e.id
                WHERE s.month = :month AND s.year = :year";

            $bindings = [':month' => $month, ':year' => $year];

            if ($branchId) {
                $sql .= " AND e.branch_id = :branch_id";
                $bindings[':branch_id'] = $branchId;
            }

            $this->db->prepare($sql);

            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            $result = $this->db->fetch();

            if ($result) {
                $summary['employees_count'] = (int)$result['employees_count'];
                $summary['total_fixed_amount'] = (float)$result['total_fixed_amount'];
                $summary['total_commission_amount'] = (float)$result['total_commission_amount'];
                $summary['total_bonuses'] = (float)$result['total_bonuses'];
                $summary['total_deductions'] = (float)$result['total_deductions'];
                $summary['total_amount'] = (float)$result['total_amount'];
                $summary['total_paid'] = (float)$result['total_paid'];
                $summary['total_unpaid'] = (float)$result['total_unpaid'];
                $summary['paid_count'] = (int)$result['paid_count'];
                $summary['unpaid_count'] = (int)$result['unpaid_count'];
            }

            // استعلام لحساب أنواع الرواتب
            $sql = "SELECT
                    COUNT(CASE WHEN s.fixed_amount > 0 AND s.commission_amount = 0 THEN 1 END) as fixed_count,
                    COUNT(CASE WHEN s.fixed_amount = 0 AND s.commission_amount > 0 THEN 1 END) as commission_count,
                    COUNT(CASE WHEN s.fixed_amount > 0 AND s.commission_amount > 0 THEN 1 END) as mixed_count
                FROM employee_salaries s
                JOIN employees e ON s.employee_id = e.id
                WHERE s.month = :month AND s.year = :year";

            if ($branchId) {
                $sql .= " AND e.branch_id = :branch_id";
            }

            $this->db->prepare($sql);

            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            $result = $this->db->fetch();

            if ($result) {
                $summary['salary_types']['fixed'] = (int)$result['fixed_count'];
                $summary['salary_types']['commission'] = (int)$result['commission_count'];
                $summary['salary_types']['mixed'] = (int)$result['mixed_count'];
            }

            return $summary;
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع ملخص الرواتب: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على أفضل الموظفين أداءً
     * @param int $limit عدد الموظفين
     * @param string $period الفترة (month, year, all)
     * @param int $branchId معرف الفرع (اختياري)
     * @return array قائمة الموظفين
     */
    /**
 * الحصول على الخدمات التي يقدمها الموظف
 * @param int $employeeId معرف الموظف
 * @return array قائمة الخدمات
 */
public function getEmployeeServices($employeeId) {
    try {
        // التحقق من وجود الموظف
        if (!$this->getEmployeeById($employeeId)) {
            throw new Exception('الموظف غير موجود');
        }

        $this->db->prepare("SELECT s.*,
                           sc.name as category_name
                           FROM services s
                           JOIN service_employees se ON s.id = se.service_id
                           LEFT JOIN service_categories sc ON s.category_id = sc.id
                           WHERE se.employee_id = :employee_id
                           AND s.is_active = 1
                           ORDER BY s.name ASC");

        $this->db->bind(':employee_id', $employeeId);
        return $this->db->fetchAll();
    } catch (Exception $e) {
        error_log('خطأ أثناء استرجاع خدمات الموظف: ' . $e->getMessage());
        throw $e;
    }
}



/**
 * التحقق مما إذا كان الموظف يقدم خدمة معينة
 * @param int $employeeId معرف الموظف
 * @param int $serviceId معرف الخدمة
 * @return bool نتيجة التحقق
 */
public function isEmployeeProvidingService($employeeId, $serviceId) {
    try {
        $this->db->prepare("SELECT COUNT(*) FROM service_employees
                          WHERE employee_id = :employee_id
                          AND service_id = :service_id");
        $this->db->bind(':employee_id', $employeeId);
        $this->db->bind(':service_id', $serviceId);

        return $this->db->fetchColumn() > 0;
    } catch (Exception $e) {
        error_log('خطأ أثناء التحقق من تقديم الموظف للخدمة: ' . $e->getMessage());
        return false;
    }
}

/**
 * إضافة خدمة للموظف
 * @param int $employeeId معرف الموظف
 * @param int $serviceId معرف الخدمة
 * @return bool نجاح أو فشل العملية
 */
public function addServiceToEmployee($employeeId, $serviceId) {
    try {
        // التحقق مما إذا كان الموظف يقدم الخدمة بالفعل
        if ($this->isEmployeeProvidingService($employeeId, $serviceId)) {
            return true; // الخدمة مضافة بالفعل
        }

        $this->db->prepare("INSERT INTO service_employees (service_id, employee_id)
                          VALUES (:service_id, :employee_id)");
        $this->db->bind(':service_id', $serviceId);
        $this->db->bind(':employee_id', $employeeId);

        return $this->db->execute();
    } catch (Exception $e) {
        error_log('خطأ أثناء إضافة خدمة للموظف: ' . $e->getMessage());
        throw $e;
    }
}

/**
 * إزالة خدمة من الموظف
 * @param int $employeeId معرف الموظف
 * @param int $serviceId معرف الخدمة
 * @return bool نجاح أو فشل العملية
 */
public function removeServiceFromEmployee($employeeId, $serviceId) {
    try {
        $this->db->prepare("DELETE FROM service_employees
                          WHERE employee_id = :employee_id
                          AND service_id = :service_id");
        $this->db->bind(':employee_id', $employeeId);
        $this->db->bind(':service_id', $serviceId);

        return $this->db->execute();
    } catch (Exception $e) {
        error_log('خطأ أثناء إزالة خدمة من الموظف: ' . $e->getMessage());
        throw $e;
    }
}

/**
 * الحصول على قائمة الموظفين حسب الخدمة
 * @param int $serviceId معرف الخدمة
 * @param int $branchId معرف الفرع (اختياري)
 * @return array قائمة الموظفين
 */
public function getEmployeesByService($serviceId, $branchId = null) {
    try {
        $sql = "SELECT e.*
                FROM employees e
                JOIN service_employees se ON e.id = se.employee_id
                WHERE se.service_id = :service_id
                AND e.is_active = 1";

        $bindings = [':service_id' => $serviceId];

        if ($branchId) {
            $sql .= " AND e.branch_id = :branch_id";
            $bindings[':branch_id'] = $branchId;
        }

        $sql .= " ORDER BY e.name ASC";

        $this->db->prepare($sql);

        foreach ($bindings as $param => $value) {
            $this->db->bind($param, $value);
        }

        return $this->db->fetchAll();
    } catch (Exception $e) {
        error_log('خطأ أثناء استرجاع الموظفين حسب الخدمة: ' . $e->getMessage());
        throw $e;
    }
}
    public function getTopEmployees($limit = 5, $period = 'month', $branchId = null) {
        try {
            // تحديد نطاق التاريخ
            $startDate = '';
            $endDate = date('Y-m-d');

            switch ($period) {
                case 'month': $startDate = date('Y-m-01'); break;
                case 'year': $startDate = date('Y-01-01'); break;
                case 'all': $startDate = '2000-01-01'; break;
                default: $startDate = date('Y-m-01');
            }

            $sql = "SELECT e.id, e.name, e.position, COUNT(ii.id) as services_count, SUM(ii.total) as total_sales
                    FROM employees e
                    LEFT JOIN invoice_items ii ON e.id = ii.employee_id
                    LEFT JOIN invoices i ON ii.invoice_id = i.id
                    WHERE e.is_active = 1
                    AND i.created_at BETWEEN :start_date AND :end_date";

            $bindings = [
                ':start_date' => $startDate . ' 00:00:00',
                ':end_date' => $endDate . ' 23:59:59'
            ];

            if ($branchId) {
                $sql .= " AND e.branch_id = :branch_id";
                $bindings[':branch_id'] = $branchId;
            }

            $sql .= " GROUP BY e.id
                      ORDER BY total_sales DESC
                      LIMIT :limit";

            $this->db->prepare($sql);

            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            $this->db->bind(':limit', $limit, PDO::PARAM_INT);

            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع أفضل الموظفين: ' . $e->getMessage());
            throw $e;
        }
    }
    /**
     * الحصول على راتب بواسطة المعرف
     * @param int $salaryId معرف الراتب
     * @return array|false بيانات الراتب أو false إذا لم يتم العثور عليه
     */
    public function getSalaryById($salaryId) {
        try {
            $this->db->prepare("SELECT s.*, e.name as employee_name, e.position, e.salary_type
                              FROM employee_salaries s
                              JOIN employees e ON s.employee_id = e.id
                              WHERE s.id = :salary_id");
            $this->db->bind(':salary_id', $salaryId);

            return $this->db->fetch();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع بيانات الراتب: ' . $e->getMessage());
            throw $e;
        }
    }
    /**
     * تحديث بيانات راتب موظف
     * @param array $data بيانات الراتب المحدثة
     * @return bool نجاح أو فشل العملية
     */
    public function updateSalary($data) {
        try {
            // التحقق من وجود الراتب
            $this->db->prepare("SELECT id FROM employee_salaries WHERE id = :id");
            $this->db->bind(':id', $data['id']);

            if (!$this->db->fetch()) {
                throw new Exception('الراتب غير موجود');
            }

            // تحديث بيانات الراتب
            $sql = "UPDATE employee_salaries SET
                    fixed_amount = :fixed_amount,
                    commission_amount = :commission_amount,
                    bonuses = :bonuses,
                    deductions = :deductions,
                    total_amount = :total_amount,
                    payment_status = :payment_status,
                    notes = :notes";

            // إضافة تاريخ الدفع إذا كان موجودًا
            if (isset($data['payment_date'])) {
                $sql .= ", payment_date = :payment_date";
            }

            $sql .= " WHERE id = :id";

            $this->db->prepare($sql);

            // ربط القيم
            $this->db->bind(':fixed_amount', $data['fixed_amount']);
            $this->db->bind(':commission_amount', $data['commission_amount']);
            $this->db->bind(':bonuses', $data['bonuses'] ?? 0);
            $this->db->bind(':deductions', $data['deductions'] ?? 0);
            $this->db->bind(':total_amount', $data['total_amount']);
            $this->db->bind(':payment_status', $data['payment_status'] ?? 'unpaid');
            $this->db->bind(':notes', $data['notes'] ?? null);

            // ربط تاريخ الدفع إذا كان موجودًا
            if (isset($data['payment_date'])) {
                $this->db->bind(':payment_date', $data['payment_date']);
            }

            $this->db->bind(':id', $data['id']);

            return $this->db->execute();
        } catch (Exception $e) {
            error_log('خطأ أثناء تحديث بيانات الراتب: ' . $e->getMessage());
            throw $e;
        }
    }
    /**
     * حساب عدد أيام الشهر
     * @param int $month الشهر
     * @param int $year السنة
     * @return int عدد أيام الشهر
     */
    private function getDaysInMonth($month, $year) {
        return cal_days_in_month(CAL_GREGORIAN, $month, $year);
    }

    /**
     * حساب مستحقات الموظف لشهر وسنة محددة
     * @param int $employeeId معرف الموظف
     * @param int $month الشهر
     * @param int $year السنة
     * @param int $currentDay اليوم الحالي من الشهر (0 لحساب الشهر كاملاً)
     * @return array بيانات مستحقات الموظف
     */
    public function calculateEmployeeSalary($employeeId, $month, $year, $currentDay = 0) {
        try {

            // الحصول على بيانات الموظف
            $employee = $this->getEmployeeById($employeeId);
            if (!$employee) {
                throw new Exception('الموظف غير موجود');
            }

            // التحقق من وجود راتب محسوب مسبق
            $existingSalary = $this->getEmployeeSalary($employeeId, $month, $year);
            if ($existingSalary) {
                return $existingSalary;
            }

            // تهيئة بيانات الراتب
            $salaryData = [
                'employee_id' => $employeeId,
                'month' => $month,
                'year' => $year,
                'fixed_amount' => 0,
                'commission_amount' => 0,
                'bonuses' => 0,
                'deductions' => 0,
                'total_amount' => 0,
                'payment_status' => 'unpaid'
            ];

            // حساب الراتب الثابت بناءً على عدد أيام الشهر
            if (in_array($employee['salary_type'], ['fixed', 'both'])) {
                // حساب عدد أيام الشهر
                $daysInMonth = $this->getDaysInMonth($month, $year);

                // حساب قيمة اليوم الواحد
                $dailyRate = floatval($employee['fixed_salary']) / $daysInMonth; // استخدام عدد أيام الشهر الفعلي

                // تحديد عدد الأيام التي سيتم حساب الراتب عليها
                $daysToCalculate = $currentDay > 0 ? $currentDay : $daysInMonth;

                // حساب الراتب الثابت بناءً على عدد الأيام المحددة
                $salaryData['fixed_amount'] = $dailyRate * $daysToCalculate;

                // تقريب الراتب لأقرب رقمين عشريين
                $salaryData['fixed_amount'] = round($salaryData['fixed_amount'], 2);

                // تخزين عدد الأيام المحسوبة في الملاحظات
                $salaryData['calculated_days'] = $daysToCalculate;
                $salaryData['notes'] = json_encode(['calculated_days' => $daysToCalculate]);
            }

            // حساب العمولة إذا كان نوع الراتب نسبة أو ثابت ونسبة
            if (in_array($employee['salary_type'], ['percentage', 'both'])) {
                // تحديد تاريخ بداية ونهاية الشهر
                $startDate = sprintf('%04d-%02d-01', $year, $month);
                $endDate = date('Y-m-t', strtotime($startDate));

                // استعلام لحساب إجمالي مبيعات الموظف خلال الشهر (الخدمات فقط)
                $this->db->prepare("SELECT SUM(ii.total) as total_sales
                                  FROM invoice_items ii
                                  JOIN invoices i ON ii.invoice_id = i.id
                                  JOIN end_days ed ON i.end_day_id = ed.id
                                  WHERE ii.employee_id = :employee_id
                                  AND ii.item_type = 'service'
                                  AND ed.date BETWEEN :start_date AND :end_date
                                  AND i.payment_status = 'paid'");

                $this->db->bind(':employee_id', $employeeId);
                $this->db->bind(':start_date', $startDate);
                $this->db->bind(':end_date', $endDate);
                $salesResult = $this->db->fetch();
                $totalSales = $salesResult ? floatval($salesResult['total_sales']) : 0;

                // حساب العمولة بناءً على النسبة المئوية
                $commissionPercentage = floatval($employee['commission_percentage']);
                $salaryData['commission_amount'] = ($totalSales * $commissionPercentage) / 100;

                // تقريب العمولة لأقرب رقمين عشريين
                $salaryData['commission_amount'] = round($salaryData['commission_amount'], 2);
            }

            // حساب إجمالي الراتب
            $salaryData['total_amount'] = $salaryData['fixed_amount'] + $salaryData['commission_amount'] + $salaryData['bonuses'] - $salaryData['deductions'];
            $salaryData['total_amount'] = round($salaryData['total_amount'], 2);

            // حفظ بيانات الراتب في قاعدة البيانات
            $this->db->prepare("INSERT INTO employee_salaries
                              (employee_id, month, year, fixed_amount, commission_amount, bonuses, deductions, total_amount, payment_status, notes)
                              VALUES
                              (:employee_id, :month, :year, :fixed_amount, :commission_amount, :bonuses, :deductions, :total_amount, :payment_status, :notes)");

            $this->db->bind(':employee_id', $salaryData['employee_id']);
            $this->db->bind(':month', $salaryData['month']);
            $this->db->bind(':year', $salaryData['year']);
            $this->db->bind(':fixed_amount', $salaryData['fixed_amount']);
            $this->db->bind(':commission_amount', $salaryData['commission_amount']);
            $this->db->bind(':bonuses', $salaryData['bonuses']);
            $this->db->bind(':deductions', $salaryData['deductions']);
            $this->db->bind(':total_amount', $salaryData['total_amount']);
            $this->db->bind(':payment_status', $salaryData['payment_status']);

            // تخزين عدد الأيام المحسوبة في حقل الملاحظات
            if (isset($salaryData['calculated_days'])) {
                $notesData = ['calculated_days' => $salaryData['calculated_days']];
                $this->db->bind(':notes', json_encode($notesData));
            } else {
                $this->db->bind(':notes', null);
            }

            $this->db->execute();
            $salaryData['id'] = $this->db->lastInsertId();

            // إضافة اسم الموظف والمسمى الوظيفي للبيانات المرجعة
            $salaryData['employee_name'] = $employee['name'];
            $salaryData['position'] = $employee['position'];
            $salaryData['days_in_month'] = $this->getDaysInMonth($month, $year);

            return $salaryData;
        } catch (Exception $e) {
            error_log('خطأ أثناء حساب راتب الموظف: ' . $e->getMessage());
            throw $e;
        }
    }
    /**
     * حذف راتب موظف
     * @param int $salaryId معرف الراتب
     * @return bool نتيجة العملية
     */
    public function deleteSalary($salaryId) {
        try {
            $this->db->prepare("DELETE FROM employee_salaries WHERE id = :id");
            $this->db->bind(':id', $salaryId);
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('خطأ أثناء حذف راتب الموظف: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على راتب موظف لشهر وسنة محددة
     * @param int $employeeId معرف الموظف
     * @param int $month الشهر
     * @param int $year السنة
     * @return array|false بيانات الراتب أو false إذا لم يتم العثور عليه
     */
    public function getEmployeeSalary($employeeId, $month, $year) {
        try {
            // تأكد من أن المتغيرات من النوع الصحيح
            $employeeId = (int)$employeeId;
            $month = (int)$month;
            $year = (int)$year;

            // استعلام مباشر بدون JOIN أولاً للتأكد من وجود السجل
            $checkSql = "SELECT * FROM employee_salaries
                         WHERE employee_id = :employee_id AND month = :month AND year = :year";

            $this->db->prepare($checkSql);
            $this->db->bind(':employee_id', $employeeId);
            $this->db->bind(':month', $month);
            $this->db->bind(':year', $year);
            $check = $this->db->fetch();

            if ($check) {
                // السجل موجود في جدول الرواتب، الآن نحصل على معلومات الموظف
                $empSql = "SELECT name, position, salary_type, commission_percentage FROM employees WHERE id = :employee_id";
                $this->db->prepare($empSql);
                $this->db->bind(':employee_id', $employeeId);
                $emp = $this->db->fetch();

                // الحصول على تفاصيل الفواتير والعمولات للموظف
                $startDate = sprintf('%04d-%02d-01', $year, $month);
                $endDate = date('Y-m-t', strtotime($startDate));

                // استعلام لجلب تفاصيل الفواتير (الخدمات فقط)
                $invoicesSql = "SELECT
                                i.id as invoice_id,
                                i.invoice_number,
                                DATE_FORMAT(i.created_at, '%Y-%m-%d') as invoice_created_at,
                                ed.date as end_day_date,
                                s.name as service_name,
                                ii.price as service_price,
                                ii.quantity as service_quantity,
                                ii.total as service_total,
                                (ii.total * :commission_percentage / 100) as commission_amount
                                FROM invoice_items ii
                                JOIN invoices i ON ii.invoice_id = i.id
                                JOIN end_days ed ON i.end_day_id = ed.id
                                LEFT JOIN services s ON ii.item_id = s.id AND ii.item_type = 'service'
                                WHERE ii.employee_id = :employee_id
                                AND ii.item_type = 'service'
                                AND ed.date BETWEEN :start_date AND :end_date
                                AND i.payment_status = 'paid'
                                ORDER BY ed.date DESC, i.created_at DESC";

                $this->db->prepare($invoicesSql);
                $this->db->bind(':employee_id', $employeeId);
                $this->db->bind(':start_date', $startDate);
                $this->db->bind(':end_date', $endDate);
                $this->db->bind(':commission_percentage', $emp['commission_percentage'] ?? 0);
                $invoices = $this->db->fetchAll();

                if ($emp) {
                    $result = array_merge($check, [
                        'employee_name' => $emp['name'],
                        'position' => $emp['position'],
                        'salary_type' => $emp['salary_type'],
                        'commission_percentage' => $emp['commission_percentage'],
                        'invoices_details' => $invoices,
                        'days_in_month' => $this->getDaysInMonth($month, $year)
                    ]);

                    // استخراج عدد الأيام المحسوبة من حقل الملاحظات إذا كان موجوداً
                    if (!empty($check['notes'])) {
                        $notesData = json_decode($check['notes'], true);
                        if (is_array($notesData) && isset($notesData['calculated_days'])) {
                            $result['calculated_days'] = $notesData['calculated_days'];
                        }
                    }

                    return $result;
                }

                return $check;
            }

            return false;
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع راتب الموظف: ' . $e->getMessage());
            return false;
        }
    }
}
