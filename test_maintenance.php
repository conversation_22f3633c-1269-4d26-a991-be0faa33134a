<?php
/**
 * صفحة اختبار نظام الصيانة
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once 'config/init.php';

echo "<h1>صفحة اختبار نظام الصيانة</h1>";

// معلومات المستخدم الحالي
echo "<h2>معلومات المستخدم:</h2>";
echo "<p><strong>دور المستخدم:</strong> " . ($_SESSION['user_role'] ?? 'غير محدد') . "</p>";
echo "<p><strong>معرف المستخدم:</strong> " . ($_SESSION['user_id'] ?? 'غير محدد') . "</p>";

// حالة ملف الصيانة
$maintenanceFile = 'config/maintenance_mode.txt';
echo "<h2>حالة ملف الصيانة:</h2>";
echo "<p><strong>مسار الملف:</strong> " . realpath($maintenanceFile) . "</p>";
echo "<p><strong>الملف موجود:</strong> " . (file_exists($maintenanceFile) ? 'نعم' : 'لا') . "</p>";

if (file_exists($maintenanceFile)) {
    echo "<p><strong>محتوى الملف:</strong> " . file_get_contents($maintenanceFile) . "</p>";
    echo "<p><strong>وقت إنشاء الملف:</strong> " . date('Y-m-d H:i:s', filemtime($maintenanceFile)) . "</p>";
}

// اختبار دوال الصيانة
echo "<h2>اختبار دوال الصيانة:</h2>";

// استدعاء الدوال مباشرة
require_once 'includes/maintenance_check.php';

echo "<p><strong>isMaintenanceMode():</strong> " . (isMaintenanceMode() ? 'true' : 'false') . "</p>";
echo "<p><strong>isMaintenanceException():</strong> " . (isMaintenanceException() ? 'true' : 'false') . "</p>";

// معلومات الصفحة الحالية
echo "<h2>معلومات الصفحة:</h2>";
echo "<p><strong>اسم الملف:</strong> " . basename($_SERVER['SCRIPT_NAME']) . "</p>";
echo "<p><strong>المسار الكامل:</strong> " . $_SERVER['SCRIPT_NAME'] . "</p>";
echo "<p><strong>المجلد الحالي:</strong> " . dirname($_SERVER['SCRIPT_NAME']) . "</p>";

// اختبار صلاحيات المجلد
echo "<h2>صلاحيات المجلد:</h2>";
$configDir = 'config/';
echo "<p><strong>مجلد config موجود:</strong> " . (is_dir($configDir) ? 'نعم' : 'لا') . "</p>";
echo "<p><strong>يمكن الكتابة في config:</strong> " . (is_writable($configDir) ? 'نعم' : 'لا') . "</p>";

// اختبار إنشاء ملف
echo "<h2>اختبار إنشاء ملف:</h2>";
$testFile = 'config/test_write.txt';
$writeResult = file_put_contents($testFile, 'test content');
echo "<p><strong>نتيجة الكتابة:</strong> " . ($writeResult !== false ? 'نجح' : 'فشل') . "</p>";

if ($writeResult !== false) {
    echo "<p><strong>محتوى الملف المكتوب:</strong> " . file_get_contents($testFile) . "</p>";
    unlink($testFile); // حذف الملف التجريبي
    echo "<p><em>تم حذف الملف التجريبي</em></p>";
}

// روابط مفيدة
echo "<h2>روابط مفيدة:</h2>";
echo "<p><a href='admin/maintenance_control.php'>لوحة التحكم في الصيانة</a></p>";
echo "<p><a href='maintenance.php'>صفحة الصيانة</a></p>";
echo "<p><a href='pages/dashboard.php'>لوحة التحكم</a></p>";

// عرض آخر سجلات الأخطاء
echo "<h2>آخر سجلات الأخطاء:</h2>";
$errorLog = ini_get('error_log');
if ($errorLog && file_exists($errorLog)) {
    $lines = file($errorLog);
    $lastLines = array_slice($lines, -10); // آخر 10 أسطر
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
    foreach ($lastLines as $line) {
        if (strpos($line, 'Maintenance') !== false) {
            echo htmlspecialchars($line);
        }
    }
    echo "</pre>";
} else {
    echo "<p>لا يمكن العثور على ملف سجل الأخطاء</p>";
}

?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    direction: rtl;
}
h1, h2 {
    color: #333;
}
p {
    margin: 5px 0;
}
strong {
    color: #666;
}
a {
    color: #007bff;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
</style>
