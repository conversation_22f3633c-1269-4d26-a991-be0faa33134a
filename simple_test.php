<?php
/**
 * صفحة اختبار بسيطة لنظام الصيانة
 * بدون تسجيل دخول لاختبار إعادة التوجيه
 */

// بدء الجلسة
session_start();

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head><meta charset='UTF-8'><title>صفحة اختبار بسيطة</title></head>";
echo "<body style='font-family: Arial, sans-serif; margin: 20px;'>";

echo "<h1>🧪 صفحة اختبار بسيطة</h1>";
echo "<p>هذه صفحة اختبار لنظام الصيانة</p>";

// عرض معلومات الجلسة
echo "<h2>معلومات الجلسة:</h2>";
echo "<ul>";
echo "<li><strong>دور المستخدم:</strong> " . ($_SESSION['user_role'] ?? 'غير مسجل') . "</li>";
echo "<li><strong>معرف المستخدم:</strong> " . ($_SESSION['user_id'] ?? 'غير مسجل') . "</li>";
echo "</ul>";

// التحقق من ملف الصيانة
$maintenanceFile = 'config/maintenance_mode.txt';
echo "<h2>حالة الصيانة:</h2>";
echo "<p><strong>ملف الصيانة موجود:</strong> " . (file_exists($maintenanceFile) ? 'نعم ✅' : 'لا ❌') . "</p>";

if (file_exists($maintenanceFile)) {
    echo "<p><strong>محتوى الملف:</strong> " . file_get_contents($maintenanceFile) . "</p>";
}

// تضمين ملف التحقق من الصيانة
echo "<h2>تضمين ملف التحقق:</h2>";
echo "<p>سيتم الآن تضمين ملف maintenance_check.php...</p>";

// تضمين الملف
require_once 'includes/maintenance_check.php';

echo "<p>✅ تم تضمين الملف بنجاح!</p>";

// إذا وصلنا هنا، فهذا يعني أن إعادة التوجيه لم تحدث
echo "<h2>🎉 النتيجة:</h2>";
echo "<p>إذا كنت ترى هذه الرسالة، فهذا يعني:</p>";
echo "<ul>";
echo "<li>إما أن الصيانة غير مفعلة</li>";
echo "<li>أو أنك مستثنى من الصيانة</li>";
echo "<li>أو أن هناك مشكلة في نظام الصيانة</li>";
echo "</ul>";

echo "<h2>روابط مفيدة:</h2>";
echo "<ul>";
echo "<li><a href='debug_maintenance.php'>صفحة التشخيص المتقدمة</a></li>";
echo "<li><a href='maintenance.php'>صفحة الصيانة</a></li>";
echo "<li><a href='admin/maintenance_control.php'>لوحة التحكم في الصيانة</a></li>";
echo "</ul>";

echo "</body></html>";
?>
