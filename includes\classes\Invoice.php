<?php
/**
 * فئة الفاتورة
 * تتعامل مع إدارة فواتير صالون الحلاقة والكوافير
 */

// منع الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول المباشر لهذا الملف');
}

class Invoice {
    private $db;

    /**
     * إنشاء كائن من فئة الفاتورة
     * @param Database $db كائن قاعدة البيانات
     */
    public function __construct($db) {
        $this->db = $db;
    }

    /**
     * إنشاء فاتورة جديدة
     * @param array $invoiceData بيانات الفاتورة
     * @param array $items العناصر (خدمات/منتجات)
     * @return int|false معرف الفاتورة الجديدة أو false إذا فشلت العملية
     */
    public function createInvoice($invoiceData, $items) {
        try {
            $this->db->beginTransaction();

            // توليد رقم فاتورة فريد
            $invoiceNumber = $this->generateInvoiceNumber($invoiceData['branch_id'] ?? null);

            // إضافة رقم الفاتورة إلى البيانات
            $invoiceData['invoice_number'] = $invoiceNumber;

            // التحقق من وجود نهاية يوم مفتوحة للفرع إذا لم يتم توفير معرف نهاية اليوم
            if (empty($invoiceData['end_day_id'])) {
                $branchId = $invoiceData['branch_id'] ?? $_SESSION['user_branch_id'];
                $this->db->prepare("SELECT id FROM end_days
                              WHERE branch_id = :branch_id
                              AND closed_at IS NULL");
                $this->db->bind(':branch_id', $branchId);
                $openEndDay = $this->db->fetch();

                if (!$openEndDay) {
                    throw new Exception('لا يمكن إنشاء فاتورة بدون بدء يوم العمل للفرع', 400);
                }

                // إضافة معرف نهاية اليوم إلى بيانات الفاتورة للفواتير المدفوعة والمدفوعة جزئيًا
                $paymentStatus = $invoiceData['payment_status'] ?? 'paid';
                if ($paymentStatus === 'paid' || $paymentStatus === 'partial') {
                    $invoiceData['end_day_id'] = $openEndDay['id'];
                } else {
                    // لا تربط الفواتير غير المدفوعة بيوم العمل
                    $invoiceData['end_day_id'] = null;
                }
            }

            // إعداد بيانات الفاتورة
            $this->db->prepare("INSERT INTO invoices (
                invoice_number, customer_id, cashier_id, employee_id,
                total_amount, discount_amount, discount_type,
                tax_amount, final_amount, payment_method,
                payment_status, paid_amount, notes, branch_id, end_day_id
            ) VALUES (
                :invoice_number, :customer_id, :cashier_id, :employee_id,
                :total_amount, :discount_amount, :discount_type,
                :tax_amount, :final_amount, :payment_method,
                :payment_status, :paid_amount, :notes, :branch_id, :end_day_id
            )");

            // تحديد المبلغ المدفوع بناءً على حالة الدفع
            $paymentStatus = $invoiceData['payment_status'] ?? 'paid';
            if ($paymentStatus === 'paid') {
                $paidAmount = $invoiceData['final_amount'] ?? $invoiceData['total_amount'];
            } elseif ($paymentStatus === 'partial') {
                $paidAmount = isset($invoiceData['paid_amount']) ? floatval($invoiceData['paid_amount']) : 0;
                // التحقق من أن المبلغ المدفوع أقل من المبلغ الإجمالي وأكبر من صفر
                if ($paidAmount <= 0) {
                    throw new Exception('يجب أن يكون المبلغ المدفوع أكبر من صفر للدفع الجزئي');
                }
                if ($paidAmount >= ($invoiceData['final_amount'] ?? $invoiceData['total_amount'])) {
                    throw new Exception('المبلغ المدفوع يجب أن يكون أقل من المبلغ الإجمالي للدفع الجزئي');
                }
            } else {
                $paidAmount = 0;
            }

            // ربط القيم للفاتورة
            $this->db->bind(':invoice_number', $invoiceNumber);
            $this->db->bind(':customer_id', $invoiceData['customer_id'] ?? null);
            $this->db->bind(':cashier_id', $invoiceData['cashier_id'] ?? $_SESSION['user_id']);
            $this->db->bind(':employee_id', $invoiceData['employee_id'] ?? null);
            $this->db->bind(':total_amount', $invoiceData['total_amount'] ?? 0);
            $this->db->bind(':discount_amount', $invoiceData['discount_amount'] ?? 0);
            $this->db->bind(':discount_type', $invoiceData['discount_type'] ?? null);
            $this->db->bind(':tax_amount', $invoiceData['tax_amount'] ?? 0);
            $this->db->bind(':final_amount', $invoiceData['final_amount'] ?? $invoiceData['total_amount']);
            $this->db->bind(':payment_method', $invoiceData['payment_method'] ?? 'cash');
            $this->db->bind(':payment_status', $paymentStatus);
            $this->db->bind(':paid_amount', $paidAmount);
            $this->db->bind(':notes', $invoiceData['notes'] ?? null);
            $this->db->bind(':branch_id', $invoiceData['branch_id'] ?? $_SESSION['user_branch_id']);
            $this->db->bind(':end_day_id', $invoiceData['end_day_id'] ?? null);

            $this->db->execute();
            $invoiceId = (int)$this->db->lastInsertId();

            // إضافة عناصر الفاتورة
            $this->addInvoiceItems($invoiceId, $items);

            // تحديث المخزون
            $this->updateInventoryAfterSale($items, $invoiceData['branch_id'] ?? $_SESSION['user_branch_id']);

            $this->db->commit();
            return $invoiceId;
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log('خطأ أثناء إنشاء الفاتورة: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * إضافة عناصر الفاتورة
     * @param int $invoiceId معرف الفاتورة
     * @param array $items العناصر (خدمات/منتجات)
     */
    private function addInvoiceItems($invoiceId, $items) {
        // التحقق من وجود عناصر للإضافة
        if (empty($items)) {
            return; // لا توجد عناصر للإضافة
        }

        foreach ($items as $item) {
            $this->db->prepare("INSERT INTO invoice_items (
                invoice_id, item_type, item_id, quantity,
                price, discount, total, employee_id
            ) VALUES (
                :invoice_id, :item_type, :item_id, :quantity,
                :price, :discount, :total, :employee_id
            )");

            $this->db->bind(':invoice_id', $invoiceId);
            $this->db->bind(':item_type', $item['item_type']);
            $this->db->bind(':item_id', $item['item_id']);
            $this->db->bind(':quantity', $item['quantity'] ?? 1);
            $this->db->bind(':price', $item['price']);
            $this->db->bind(':discount', $item['discount'] ?? 0);
            $this->db->bind(':total', $item['total']);
            $this->db->bind(':employee_id', $item['employee_id'] ?? null);

            $this->db->execute();
        }
    }
    /**
 * إنشاء فاتورة مع إدارة المعاملة خارجيًا
 *
 * @param array $invoiceData بيانات الفاتورة
 * @param array $items عناصر الفاتورة
 * @return int معرف الفاتورة الجديدة
 */
public function createInvoiceWithTransaction($invoiceData, $items) {
    // التحقق من وجود نهاية يوم مفتوحة للفرع إذا لم يتم توفير معرف نهاية اليوم
    if (empty($invoiceData['end_day_id'])) {
        $branchId = $invoiceData['branch_id'] ?? $_SESSION['user_branch_id'];
        $this->db->prepare("SELECT id FROM end_days
                      WHERE branch_id = :branch_id
                      AND closed_at IS NULL");
        $this->db->bind(':branch_id', $branchId);
        $openEndDay = $this->db->fetch();

        if (!$openEndDay) {
            throw new Exception('لا يمكن إنشاء فاتورة بدون بدء يوم العمل للفرع', 400);
        }

        // إضافة معرف نهاية اليوم إلى بيانات الفاتورة للفواتير المدفوعة والمدفوعة جزئيًا
        $paymentStatus = $invoiceData['payment_status'] ?? 'paid';
        if ($paymentStatus === 'paid' || $paymentStatus === 'partial') {
            $invoiceData['end_day_id'] = $openEndDay['id'];
        } else {
            // لا تربط الفواتير غير المدفوعة بيوم العمل
            $invoiceData['end_day_id'] = null;
        }
    }

    // إدراج بيانات الفاتورة الرئيسية
    $currentDateTime = date('Y-m-d H:i:s'); // الحصول على التاريخ والوقت الحالي من PHP

    $sql = "INSERT INTO invoices (
        invoice_number, customer_id, cashier_id, employee_id,
        total_amount, discount_amount, discount_type, tax_amount,
        final_amount, payment_method, payment_status, paid_amount, notes, branch_id, end_day_id, created_at
    ) VALUES (
        :invoice_number, :customer_id, :cashier_id, :employee_id,
        :total_amount, :discount_amount, :discount_type, :tax_amount,
        :final_amount, :payment_method, :payment_status, :paid_amount, :notes, :branch_id, :end_day_id, :created_at
    )";

    // Determinar el monto pagado según el estado de pago
    if ($invoiceData['payment_status'] === 'paid') {
        $paidAmount = $invoiceData['final_amount'];
    } elseif ($invoiceData['payment_status'] === 'partial') {
        $paidAmount = isset($invoiceData['paid_amount']) ? floatval($invoiceData['paid_amount']) : 0;
        // Validar que el monto pagado sea válido para pago parcial
        if ($paidAmount <= 0) {
            throw new Exception('يجب أن يكون المبلغ المدفوع أكبر من صفر للدفع الجزئي');
        }
        if ($paidAmount >= $invoiceData['final_amount']) {
            throw new Exception('المبلغ المدفوع يجب أن يكون أقل من المبلغ الإجمالي للدفع الجزئي');
        }
    } else {
        $paidAmount = 0;
    }

    $this->db->prepare($sql);
    $this->db->bind(':invoice_number', $invoiceData['invoice_number']);
    $this->db->bind(':customer_id', $invoiceData['customer_id']);
    $this->db->bind(':cashier_id', $invoiceData['cashier_id']);
    $this->db->bind(':employee_id', $invoiceData['employee_id']);
    $this->db->bind(':total_amount', $invoiceData['total_amount']);
    $this->db->bind(':discount_amount', $invoiceData['discount_amount']);
    $this->db->bind(':discount_type', $invoiceData['discount_type']);
    $this->db->bind(':tax_amount', $invoiceData['tax_amount']);
    $this->db->bind(':final_amount', $invoiceData['final_amount']);
    $this->db->bind(':payment_method', $invoiceData['payment_method']);
    $this->db->bind(':payment_status', $invoiceData['payment_status']);
    $this->db->bind(':paid_amount', $paidAmount);
    $this->db->bind(':notes', $invoiceData['notes']);
    $this->db->bind(':branch_id', $invoiceData['branch_id']);
    $this->db->bind(':end_day_id', $invoiceData['end_day_id']);
    $this->db->bind(':created_at', $currentDateTime);
    $this->db->execute();

    // الحصول على معرف الفاتورة المدرجة
    $invoiceId = (int)$this->db->lastInsertId();

    // إدراج عناصر الفاتورة
    foreach ($items as $item) {
        $sql = "INSERT INTO invoice_items (
            invoice_id, item_id, item_type, quantity, price, discount, total, employee_id
        ) VALUES (
            :invoice_id, :item_id, :item_type, :quantity, :price, :discount, :total, :employee_id
        )";

        $this->db->prepare($sql);
        $this->db->bind(':invoice_id', $invoiceId);
        $this->db->bind(':item_id', $item['item_id']);
        $this->db->bind(':item_type', $item['item_type']);
        $this->db->bind(':quantity', $item['quantity']);
        $discount = isset($item['discount']) ? $item['discount'] : 0;
        $total = isset($item['total']) ? $item['total'] : ($item['price'] * $item['quantity'] - $discount);
        $this->db->bind(':price', $item['price']);
        $this->db->bind(':discount', $discount);
        $this->db->bind(':total', $total);
        $employeeId = $item['employee_id'] ?? null;
        $this->db->bind(':employee_id', $employeeId);
        $this->db->execute();

        // تحديث المخزون إذا كان العنصر منتجًا
        if ($item['item_type'] === 'product') {
            $inventoryModel = new Inventory($this->db);
            $inventoryModel->decreaseStock(
                $item['item_id'],
                $item['quantity'],
                $invoiceData['branch_id']
            );
        }
    }

    // تسجيل استخدام كود الترويج إذا كان موجودًا
    if (isset($invoiceData['promo_code_id']) && !empty($invoiceData['promo_code_id'])) {
        $promoCodeId = $invoiceData['promo_code_id'];
        $customerId = $invoiceData['customer_id'] ?? null;
        $discountAmount = $invoiceData['discount_amount'] ?? 0;

        // تسجيل استخدام كود الترويج
        $promoCodeModel = new PromoCode($this->db);
        $promoCodeModel->recordPromoCodeUsage($promoCodeId, $invoiceId, $customerId, $discountAmount);
        error_log('Recorded promo code usage: promoCodeId=' . $promoCodeId . ', invoiceId=' . $invoiceId . ', customerId=' . $customerId . ', discountAmount=' . $discountAmount);
    }

    return $invoiceId;
}
    /**
     * تحديث المخزون بعد البيع
     * @param array $items العناصر (خدمات/منتجات)
     * @param int $branchId معرف الفرع
     */
    private function updateInventoryAfterSale($items, $branchId) {
        // التحقق من وجود عناصر لتحديث المخزون
        if (empty($items)) {
            return; // لا توجد عناصر لتحديث المخزون
        }

        $inventoryModel = new Inventory($this->db);

        foreach ($items as $item) {
            if ($item['item_type'] === 'product') {
                $inventoryModel->decreaseStock(
                    $item['item_id'],
                    $item['quantity'] ?? 1,
                    $branchId
                );
            }
        }
    }

    /**
     * توليد رقم فاتورة فريد
     * @param int $branchId معرف الفرع (اختياري)
     * @return string رقم الفاتورة
     */
    private function generateInvoiceNumber($branchId = null) {
        // يمكن استخدام الدالة generateInvoiceNumber من ملف helpers.php
        return generateInvoiceNumber($branchId);
    }
    public function setProductStock($productId, $quantity, $branchId, $notes = '') {
        try {
            // Check if there's already an active transaction
            $transactionStartedHere = false;
            if (!$this->db->inTransaction()) {
                $this->db->beginTransaction();
                $transactionStartedHere = true;
            }

            // Your existing code here...

            // Only commit if we started the transaction
            if ($transactionStartedHere && $this->db->inTransaction()) {
                $this->db->commit();
            }

            return true;
        } catch (Exception $e) {
            // Only rollback if we started the transaction
            if ($transactionStartedHere && $this->db->inTransaction()) {
                $this->db->rollBack();
            }
            throw $e;
        }
    }
    /**
     * تحديث بيانات فاتورة
     * @param int $invoiceId معرف الفاتورة
     * @param array $invoiceData البيانات المحدثة
     * @return bool نجاح أو فشل العملية
     */
    public function updateInvoice($invoiceId, $invoiceData) {
        try {
            $this->db->beginTransaction();

            // تحديث بيانات الفاتورة الرئيسية
            $sql = "UPDATE invoices SET
                    customer_id = :customer_id,
                    employee_id = :employee_id,
                    total_amount = :total_amount,
                    discount_amount = :discount_amount,
                    tax_amount = :tax_amount,
                    final_amount = :final_amount,
                    payment_method = :payment_method,
                    payment_status = :payment_status,
                    paid_amount = :paid_amount,
                    notes = :notes";

            $sql .= " WHERE id = :id";

            // تحديد المبلغ المدفوع بناءً على حالة الدفع
            $paymentStatus = $invoiceData['payment_status'] ?? 'paid';
            if ($paymentStatus === 'paid') {
                $paidAmount = $invoiceData['final_amount'] ?? $invoiceData['total_amount'];
            } elseif ($paymentStatus === 'partial') {
                $paidAmount = isset($invoiceData['paid_amount']) ? floatval($invoiceData['paid_amount']) : 0;
                // التحقق من أن المبلغ المدفوع أقل من المبلغ الإجمالي وأكبر من صفر
                if ($paidAmount <= 0) {
                    throw new Exception('يجب أن يكون المبلغ المدفوع أكبر من صفر للدفع الجزئي');
                }
                if ($paidAmount >= ($invoiceData['final_amount'] ?? $invoiceData['total_amount'])) {
                    throw new Exception('المبلغ المدفوع يجب أن يكون أقل من المبلغ الإجمالي للدفع الجزئي');
                }
            } else {
                $paidAmount = 0;
            }

            $this->db->prepare($sql);

            $this->db->bind(':id', $invoiceId);
            $this->db->bind(':customer_id', $invoiceData['customer_id'] ?? null);
            $this->db->bind(':employee_id', $invoiceData['employee_id'] ?? null);
            $this->db->bind(':total_amount', $invoiceData['total_amount'] ?? 0);
            $this->db->bind(':discount_amount', $invoiceData['discount_amount'] ?? 0);
            $this->db->bind(':tax_amount', $invoiceData['tax_amount'] ?? 0);
            $this->db->bind(':final_amount', $invoiceData['final_amount'] ?? $invoiceData['total_amount']);
            $this->db->bind(':payment_method', $invoiceData['payment_method'] ?? 'cash');
            $this->db->bind(':payment_status', $paymentStatus);
            $this->db->bind(':paid_amount', $paidAmount);
            $this->db->bind(':notes', $invoiceData['notes'] ?? null);

            $this->db->execute();

            // إعادة تسوية المخزون فقط إذا تم توفير عناصر جديدة
            if (isset($invoiceData['items'])) {
                $this->revertAndUpdateInventory($invoiceId, $invoiceData['items']);
            }

            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log('خطأ أثناء تحديث الفاتورة: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * إعادة تسوية المخزون عند تحديث الفاتورة
     * @param int $invoiceId معرف الفاتورة
     * @param array $newItems العناصر الجديدة
     */
    private function revertAndUpdateInventory($invoiceId, $newItems) {
        $inventoryModel = new Inventory($this->db);

        // استرجاع العناصر القديمة
        $oldItems = $this->getInvoiceItems($invoiceId);

        // إعادة المخزون للعناصر القديمة
        foreach ($oldItems as $oldItem) {
            if ($oldItem['item_type'] === 'product') {
                $inventoryModel->updateStockAfterCancelSale(
                    $oldItem['item_id'],
                    $oldItem['quantity'],
                    $oldItem['branch_id']
                );
            }
        }

        // حذف العناصر القديمة
        $this->db->prepare("DELETE FROM invoice_items WHERE invoice_id = :invoice_id");
        $this->db->bind(':invoice_id', $invoiceId);
        $this->db->execute();

        // إضافة العناصر الجديدة وتحديث المخزون فقط إذا كانت هناك عناصر جديدة
        if (!empty($newItems)) {
            // تحديث المخزون بالعناصر الجديدة
            $this->updateInventoryAfterSale($newItems, $this->getCurrentInvoiceBranchId($invoiceId));

            // إضافة العناصر الجديدة
            $this->addInvoiceItems($invoiceId, $newItems);
        }
    }

    /**
     * الحصول على معرف الفرع للفاتورة الحالية
     * @param int $invoiceId معرف الفاتورة
     * @return int معرف الفرع
     */
    private function getCurrentInvoiceBranchId($invoiceId) {
        $this->db->prepare("SELECT branch_id FROM invoices WHERE id = :id");
        $this->db->bind(':id', $invoiceId);
        $invoice = $this->db->fetch();
        return $invoice ? $invoice['branch_id'] : $_SESSION['user_branch_id'];
    }

    /**
     * تحديث حالة دفع الفاتورة
     * @param int $invoiceId معرف الفاتورة
     * @param string $paymentStatus حالة الدفع الجديدة (paid, partial, unpaid)
     * @param int $userId معرف المستخدم الذي يحاول التحديث (اختياري)
     * @param float $paidAmount المبلغ المدفوع (اختياري، يستخدم فقط مع حالة الدفع الجزئي)
     * @return bool نجاح أو فشل العملية
     */
    public function updatePaymentStatus($invoiceId, $paymentStatus, $userId = null, $paidAmount = 0) {
        try {
            // التحقق من وجود الفاتورة
            $this->db->prepare("SELECT id, cashier_id, end_day_id, payment_status FROM invoices WHERE id = :id");
            $this->db->bind(':id', $invoiceId);
            $invoice = $this->db->fetch();

            if (!$invoice) {
                throw new Exception('الفاتورة غير موجودة');
            }

            // التحقق من أن اليوم لم يتم إغلاقه
            if ($invoice['end_day_id']) {
                $this->db->prepare("SELECT closed_at FROM end_days WHERE id = :end_day_id");
                $this->db->bind(':end_day_id', $invoice['end_day_id']);
                $endDay = $this->db->fetch();

                if ($endDay && $endDay['closed_at']) {
                    throw new Exception('لا يمكن تغيير حالة الفاتورة لأن اليوم مغلق');
                }
            }

            // إذا تم توفير معرف المستخدم، تحقق مما إذا كان هو منشئ الفاتورة
            if ($userId && !hasPermission('invoices_edit')) {
                if ($invoice['cashier_id'] != $userId) {
                    throw new Exception('ليس لديك صلاحية تغيير حالة هذه الفاتورة');
                }
            }

            // التحقق من أن حالة الدفع صحيحة
            if (!in_array($paymentStatus, ['paid', 'partial', 'unpaid'])) {
                throw new Exception('حالة الدفع غير صحيحة');
            }

            // تحديث حالة الدفع
            try {
                // بدء معاملة قاعدة البيانات
                $this->db->beginTransaction();

                // التحقق من أن حالة الدفع هي واحدة من القيم المسموح بها
                if (!in_array($paymentStatus, ['paid', 'partial', 'unpaid'])) {
                    error_log("Invalid payment status: " . $paymentStatus);
                    throw new Exception('حالة الدفع غير صحيحة');
                }

                $currentDateTime = date('Y-m-d H:i:s'); // الحصول على التاريخ والوقت الحالي من PHP

                // الحصول على المبلغ الإجمالي للفاتورة
                $this->db->prepare("SELECT final_amount FROM invoices WHERE id = :id");
                $this->db->bind(':id', $invoiceId);
                $invoiceAmount = $this->db->fetch();
                $finalAmount = $invoiceAmount ? floatval($invoiceAmount['final_amount']) : 0;

                // تحديد المبلغ المدفوع بناءً على حالة الدفع
                if ($paymentStatus === 'paid') {
                    // إذا كانت مدفوعة بالكامل، المبلغ المدفوع = المبلغ الإجمالي
                    $paidAmount = $finalAmount;
                } else if ($paymentStatus === 'unpaid') {
                    // إذا كانت غير مدفوعة، المبلغ المدفوع = 0
                    $paidAmount = 0;
                }
                // إذا كانت مدفوعة جزئياً، نستخدم المبلغ المدفوع المرسل
                // والتحقق من أنه أقل من المبلغ الإجمالي
                else if ($paymentStatus === 'partial') {
                    if ($paidAmount <= 0) {
                        throw new Exception('يجب أن يكون المبلغ المدفوع أكبر من صفر للدفع الجزئي');
                    }
                    if ($paidAmount >= $finalAmount) {
                        throw new Exception('المبلغ المدفوع يجب أن يكون أقل من المبلغ الإجمالي للدفع الجزئي');
                    }
                }

                error_log("Payment Status: " . $paymentStatus);
                error_log("Paid Amount: " . $paidAmount);
                error_log("Final Amount: " . $finalAmount);

                // إذا كانت الفاتورة ستصبح مدفوعة أو مدفوعة جزئيًا، تحقق من ربطها بيوم العمل المفتوح
                if (($paymentStatus === 'paid' || $paymentStatus === 'partial') && $invoice['payment_status'] === 'unpaid') {
                    // التحقق من وجود يوم عمل مفتوح
                    $branchId = $_SESSION['user_branch_id'];
                    $this->db->prepare("SELECT id FROM end_days
                                  WHERE branch_id = :branch_id
                                  AND closed_at IS NULL");
                    $this->db->bind(':branch_id', $branchId);
                    $openEndDay = $this->db->fetch();

                    if (!$openEndDay) {
                        throw new Exception('لا يمكن تغيير حالة الفاتورة إلى مدفوعة بدون بدء يوم العمل للفرع', 400);
                    }

                    // تحديث حالة الدفع وربط الفاتورة بيوم العمل المفتوح
                    $sql = "UPDATE invoices SET payment_status = :payment_status, paid_amount = :paid_amount, end_day_id = :end_day_id, updated_at = :updated_at WHERE id = :id";
                    error_log("SQL Query: " . $sql);
                    error_log("Payment Status (Before): " . $paymentStatus);
                    error_log("Invoice ID: " . $invoiceId);
                    error_log("End Day ID: " . $openEndDay['id']);

                    $this->db->prepare($sql);
                    $this->db->bind(':payment_status', $paymentStatus);
                    $this->db->bind(':paid_amount', $paidAmount);
                    $this->db->bind(':end_day_id', $openEndDay['id']);
                    $this->db->bind(':updated_at', $currentDateTime);
                    $this->db->bind(':id', $invoiceId);
                } else {
                    // تحديث حالة الدفع والمبلغ المدفوع
                    $sql = "UPDATE invoices SET payment_status = :payment_status, paid_amount = :paid_amount, updated_at = :updated_at WHERE id = :id";
                    error_log("SQL Query: " . $sql);
                    error_log("Payment Status (Before): " . $paymentStatus);
                    error_log("Invoice ID: " . $invoiceId);

                    $this->db->prepare($sql);
                    $this->db->bind(':payment_status', $paymentStatus);
                    $this->db->bind(':paid_amount', $paidAmount);
                    $this->db->bind(':updated_at', $currentDateTime);
                    $this->db->bind(':id', $invoiceId);
                }

                $result = $this->db->execute();

                error_log("Update Result: " . ($result ? 'true' : 'false'));

                // للتحقق من التحديث
                $this->db->prepare("SELECT payment_status FROM invoices WHERE id = :id");
                $this->db->bind(':id', $invoiceId);
                $updatedInvoice = $this->db->fetch();
                error_log("Updated Payment Status: " . ($updatedInvoice ? $updatedInvoice['payment_status'] : 'not found'));

                // تأكيد المعاملة
                $this->db->commit();
            } catch (Exception $e) {
                // التراجع عن المعاملة في حالة حدوث خطأ
                $this->db->rollBack();
                error_log("Error updating payment status: " . $e->getMessage());
                throw $e;
            }

            return $result;
        } catch (Exception $e) {
            error_log('خطأ أثناء تحديث حالة دفع الفاتورة: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * حذف فاتورة
     * @param int $invoiceId معرف الفاتورة
     * @param int $userId معرف المستخدم الذي يحاول الحذف (اختياري)
     * @return bool نجاح أو فشل العملية
     */
    public function deleteInvoice($invoiceId, $userId = null) {
        try {
            $this->db->beginTransaction();

            // التحقق من عدم وجود عمليات مرتبطة بالفاتورة
            $this->db->prepare("SELECT id, cashier_id, end_day_id, payment_status FROM invoices WHERE id = :id");
            $this->db->bind(':id', $invoiceId);
            $invoice = $this->db->fetch();

            if (!$invoice) {
                throw new Exception('الفاتورة غير موجودة');
            }

            // التحقق من أن اليوم لم يتم إغلاقه
            if ($invoice['end_day_id']) {
                $this->db->prepare("SELECT closed_at FROM end_days WHERE id = :end_day_id");
                $this->db->bind(':end_day_id', $invoice['end_day_id']);
                $endDay = $this->db->fetch();

                if ($endDay && $endDay['closed_at']) {
                    throw new Exception('لا يمكن حذف الفاتورة لأن اليوم مغلق');
                }
            }

            // إذا تم توفير معرف المستخدم، تحقق مما إذا كان هو منشئ الفاتورة
            if ($userId && !hasPermission('invoices_delete')) {
                if ($invoice['cashier_id'] != $userId) {
                    throw new Exception('ليس لديك صلاحية حذف هذه الفاتورة');
                }
            }

            // استرجاع العناصر للتسوية
            $items = $this->getInvoiceItems($invoiceId);

            // إعادة المخزون
            $inventoryModel = new Inventory($this->db);
            $branchId = $this->getCurrentInvoiceBranchId($invoiceId);

            foreach ($items as $item) {
                if ($item['item_type'] === 'product') {
                    $inventoryModel->updateStockAfterCancelSale(
                        $item['item_id'],
                        $item['quantity'],
                        $branchId
                    );
                }
            }

            // حذف عناصر الفاتورة أولاً
            $this->db->prepare("DELETE FROM invoice_items WHERE invoice_id = :invoice_id");
            $this->db->bind(':invoice_id', $invoiceId);
            $this->db->execute();

            // ثم حذف الفاتورة نفسها
            $this->db->prepare("DELETE FROM invoices WHERE id = :id");
            $this->db->bind(':id', $invoiceId);
            $this->db->execute();

            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log('خطأ أثناء حذف الفاتورة: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على بيانات فاتورة
     * @param int $invoiceId معرف الفاتورة
     * @return array|false بيانات الفاتورة أو false إذا لم يتم العثور عليها
     */
    public function getInvoiceById($invoiceId) {
        try {
            $this->db->prepare("SELECT i.*,
                                c.name as customer_name,
                                c.phone as customer_phone,
                                u.name as cashier_name,
                                e.name as employee_name,
                                b.name as branch_name,
                                b.address as branch_address,
                                b.phone as branch_phone,
                                ed.closed_at as end_day_closed_at,
                                ed.date as end_day_date,
                                COALESCE(i.paid_amount, 0) as paid_amount,
                                i.updated_at
                              FROM invoices i
                              LEFT JOIN customers c ON i.customer_id = c.id
                              LEFT JOIN users u ON i.cashier_id = u.id
                              LEFT JOIN employees e ON i.employee_id = e.id
                              LEFT JOIN branches b ON i.branch_id = b.id
                              LEFT JOIN end_days ed ON i.end_day_id = ed.id
                              WHERE i.id = :id");
            $this->db->bind(':id', $invoiceId);
            $invoice = $this->db->fetch();

            if ($invoice) {
                // إضافة عناصر الفاتورة
                $invoice['items'] = $this->getInvoiceItems($invoiceId);

                // إضافة معلومات عن حالة نهاية اليوم
                $invoice['is_end_day_closed'] = !empty($invoice['end_day_closed_at']);

                // تسجيل للتحقق
                error_log('Invoice ID: ' . $invoiceId . ' - end_day_id: ' . ($invoice['end_day_id'] ?? 'null') . ' - end_day_closed_at: ' . ($invoice['end_day_closed_at'] ?? 'null') . ' - is_end_day_closed: ' . ($invoice['is_end_day_closed'] ? 'true' : 'false'));
            }

            return $invoice;
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع بيانات الفاتورة: ' . $e->getMessage());
            throw $e;
        }
    }

/**
     * الحصول على عناصر الفاتورة
     * @param int $invoiceId معرف الفاتورة
     * @return array عناصر الفاتورة
     */
    public function getInvoiceItems($invoiceId) {
        try {
            $this->db->prepare("SELECT ii.*,
                                s.name as service_name,
                                p.name as product_name,
                                e.name as employee_name,
                                i.branch_id
                              FROM invoice_items ii
                              LEFT JOIN services s ON ii.item_type = 'service' AND ii.item_id = s.id
                              LEFT JOIN products p ON ii.item_type = 'product' AND ii.item_id = p.id
                              LEFT JOIN employees e ON ii.employee_id = e.id
                              LEFT JOIN invoices i ON ii.invoice_id = i.id
                              WHERE ii.invoice_id = :invoice_id");
            $this->db->bind(':invoice_id', $invoiceId);
            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع عناصر الفاتورة: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على الفواتير في نطاق تاريخي محدد
     * @param string $startDate تاريخ البداية (YYYY-MM-DD)
     * @param string $endDate تاريخ النهاية (YYYY-MM-DD)
     * @param array $filters فلاتر إضافية (اختياري)
     * @return array قائمة الفواتير
     */
    public function getInvoicesByDateRange($startDate, $endDate, $filters = []) {
        try {
            error_log("Getting invoices from $startDate to $endDate");

            // التحقق من وجود فواتير في قاعدة البيانات
            $checkSql = "SELECT COUNT(*) FROM invoices";
            $this->db->prepare($checkSql);
            $totalInvoices = $this->db->fetchColumn();
            error_log("Total invoices in database: $totalInvoices");

            // استخدام LEFT JOIN مع جدول end_days للحصول على تاريخ إقفال اليوم
            $sql = "SELECT i.*,
                           COALESCE(ed.date, DATE(i.created_at)) as date,
                           c.name as customer_name,
                           u.name as cashier_name,
                           e.name as employee_name,
                           b.name as branch_name,
                           ed.date as end_day_date
                    FROM invoices i
                    LEFT JOIN customers c ON i.customer_id = c.id
                    LEFT JOIN users u ON i.cashier_id = u.id
                    LEFT JOIN employees e ON i.employee_id = e.id
                    LEFT JOIN branches b ON i.branch_id = b.id
                    LEFT JOIN end_days ed ON i.end_day_id = ed.id
                    WHERE (
                        (i.end_day_id IS NOT NULL AND ed.date BETWEEN :range_start_date AND :range_end_date) OR
                        (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :range_start_date AND :range_end_date)
                    )";

            $bindings = [
                ':range_start_date' => $startDate,
                ':range_end_date' => $endDate
            ];

            // تطبيق فلاتر إضافية
            if (!empty($filters['branch_id'])) {
                $sql .= " AND i.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (!empty($filters['employee_id'])) {
                $sql .= " AND i.employee_id = :employee_id";
                $bindings[':employee_id'] = $filters['employee_id'];
            }

            if (!empty($filters['payment_method'])) {
                $sql .= " AND i.payment_method = :payment_method";
                $bindings[':payment_method'] = $filters['payment_method'];
            }

            // ترتيب النتائج حسب التاريخ (استخدام end_day.date أو created_at)
            $sql .= " ORDER BY COALESCE(ed.date, DATE(i.created_at)) ASC, i.created_at ASC";

            // تسجيل الاستعلام للتحقق
            error_log('SQL Query for getInvoicesByDateRange: ' . $sql);
            foreach ($bindings as $param => $value) {
                error_log('Param ' . $param . ' = ' . $value);
            }

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            $invoices = $this->db->fetchAll();
            error_log('Found ' . count($invoices) . ' invoices in date range');

            // التحقق من البيانات المسترجعة
            if (!empty($invoices)) {
                error_log('First invoice: ' . json_encode($invoices[0]));
            }

            return $invoices;
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع الفواتير حسب التاريخ: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على قائمة الفواتير
     * @param array $filters فلاتر البحث (اختياري)
     * @return array قائمة الفواتير
     */
    public function getInvoices($filters = []) {
        try {
            $sql = "SELECT i.*,
                           c.name as customer_name,
                           u.name as cashier_name,
                           e.name as employee_name,
                           b.name as branch_name,
                           ed.closed_at as end_day_closed_at,
                           COALESCE(i.paid_amount, 0) as paid_amount
                    FROM invoices i
                    LEFT JOIN customers c ON i.customer_id = c.id
                    LEFT JOIN users u ON i.cashier_id = u.id
                    LEFT JOIN employees e ON i.employee_id = e.id
                    LEFT JOIN branches b ON i.branch_id = b.id
                    LEFT JOIN end_days ed ON i.end_day_id = ed.id";

            $whereConditions = [];
            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['search'])) {
                $whereConditions[] = "(i.invoice_number LIKE :search OR c.name LIKE :search OR c.phone LIKE :search)";
                $bindings[':search'] = '%' . $filters['search'] . '%';
            }

            if (!empty($filters['customer_id'])) {
                $whereConditions[] = "i.customer_id = :customer_id";
                $bindings[':customer_id'] = $filters['customer_id'];
            }

            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "i.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (!empty($filters['employee_id'])) {
                $whereConditions[] = "i.employee_id = :employee_id";
                $bindings[':employee_id'] = $filters['employee_id'];
            }

            if (!empty($filters['payment_method'])) {
                $whereConditions[] = "i.payment_method = :payment_method";
                $bindings[':payment_method'] = $filters['payment_method'];
            }

            if (!empty($filters['payment_status'])) {
                $whereConditions[] = "i.payment_status = :payment_status";
                $bindings[':payment_status'] = $filters['payment_status'];
            }

            // فلتر بواسطة معرف نهاية اليوم
            if (isset($filters['end_day_id'])) {
                $whereConditions[] = "i.end_day_id = :end_day_id";
                $bindings[':end_day_id'] = $filters['end_day_id'];
            }

            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                $whereConditions[] = "i.created_at BETWEEN :start_date AND :end_date";
                $bindings[':start_date'] = $filters['start_date'] . ' 00:00:00';
                $bindings[':end_date'] = $filters['end_date'] . ' 23:59:59';
            } elseif (!empty($filters['date'])) {
                $whereConditions[] = "DATE(i.created_at) = :date";
                $bindings[':date'] = $filters['date'];
            } elseif (!empty($filters['month']) && !empty($filters['year'])) {
                $whereConditions[] = "MONTH(i.created_at) = :month AND YEAR(i.created_at) = :year";
                $bindings[':month'] = $filters['month'];
                $bindings[':year'] = $filters['year'];
            }

            // إضافة شروط WHERE إذا وجدت
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            // ترتيب النتائج
            $sql .= " ORDER BY i.created_at DESC";

            // إضافة الحد والإزاحة إذا وجدت
            if (!empty($filters['limit'])) {
                $sql .= " LIMIT :limit";
                if (!empty($filters['offset'])) {
                    $sql .= " OFFSET :offset";
                }
            }

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            // ربط الحد والإزاحة إذا وجدت
            if (!empty($filters['limit'])) {
                $this->db->bind(':limit', $filters['limit'], PDO::PARAM_INT);
                if (!empty($filters['offset'])) {
                    $this->db->bind(':offset', $filters['offset'], PDO::PARAM_INT);
                }
            }

            $invoices = $this->db->fetchAll();

            // إضافة العناصر لكل فاتورة
            foreach ($invoices as &$invoice) {
                $invoice['items'] = $this->getInvoiceItems($invoice['id']);
            }

            return $invoices;
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع قائمة الفواتير: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * حساب عدد الفواتير
     * @param array $filters فلاتر البحث (اختياري)
     * @return int عدد الفواتير
     */
    public function getInvoicesCount($filters = []) {
        try {
            $sql = "SELECT COUNT(*) FROM invoices i
                    LEFT JOIN customers c ON i.customer_id = c.id";

            $whereConditions = [];
            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['search'])) {
                $whereConditions[] = "(i.invoice_number LIKE :search OR c.name LIKE :search OR c.phone LIKE :search)";
                $bindings[':search'] = '%' . $filters['search'] . '%';
            }

            if (!empty($filters['customer_id'])) {
                $whereConditions[] = "i.customer_id = :customer_id";
                $bindings[':customer_id'] = $filters['customer_id'];
            }

            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "i.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (!empty($filters['employee_id'])) {
                $whereConditions[] = "i.employee_id = :employee_id";
                $bindings[':employee_id'] = $filters['employee_id'];
            }

            if (!empty($filters['payment_method'])) {
                $whereConditions[] = "i.payment_method = :payment_method";
                $bindings[':payment_method'] = $filters['payment_method'];
            }

            if (!empty($filters['payment_status'])) {
                $whereConditions[] = "i.payment_status = :payment_status";
                $bindings[':payment_status'] = $filters['payment_status'];
            }

            // فلتر بواسطة معرف نهاية اليوم
            if (isset($filters['end_day_id'])) {
                $whereConditions[] = "i.end_day_id = :end_day_id";
                $bindings[':end_day_id'] = $filters['end_day_id'];
            }

            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                $whereConditions[] = "i.created_at BETWEEN :start_date AND :end_date";
                $bindings[':start_date'] = $filters['start_date'] . ' 00:00:00';
                $bindings[':end_date'] = $filters['end_date'] . ' 23:59:59';
            } elseif (!empty($filters['date'])) {
                $whereConditions[] = "DATE(i.created_at) = :date";
                $bindings[':date'] = $filters['date'];
            } elseif (!empty($filters['month']) && !empty($filters['year'])) {
                $whereConditions[] = "MONTH(i.created_at) = :month AND YEAR(i.created_at) = :year";
                $bindings[':month'] = $filters['month'];
                $bindings[':year'] = $filters['year'];
            }

            // إضافة شروط WHERE إذا وجدت
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            return $this->db->fetchColumn();
        } catch (Exception $e) {
            error_log('خطأ أثناء حساب عدد الفواتير: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * حساب إجمالي الفواتير المدفوعة جزئياً
     * @param array $filters فلاتر البحث (اختياري)
     * @return float إجمالي الفواتير المدفوعة جزئياً
     */
    public function getTotalPartialPaidInvoices($filters = []) {
        try {
            $sql = "SELECT SUM(final_amount - COALESCE(paid_amount, 0)) FROM invoices i";

            $whereConditions = [];
            $bindings = [];

            // التأكد من احتساب الفواتير المدفوعة جزئياً فقط
            $whereConditions[] = "i.payment_status = 'partial'";

            // تطبيق الفلاتر
            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "i.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (!empty($filters['employee_id'])) {
                $whereConditions[] = "i.employee_id = :employee_id";
                $bindings[':employee_id'] = $filters['employee_id'];
            }

            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                $whereConditions[] = "i.created_at BETWEEN :start_date AND :end_date";
                $bindings[':start_date'] = $filters['start_date'] . ' 00:00:00';
                $bindings[':end_date'] = $filters['end_date'] . ' 23:59:59';
            }

            // إضافة شروط WHERE إذا وجدت
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            // تسجيل الاستعلام للتحقق
            error_log('SQL Query for getTotalPartialPaidInvoices: ' . $sql);
            foreach ($bindings as $param => $value) {
                error_log('Param ' . $param . ' = ' . $value);
            }

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            $result = $this->db->fetchColumn();
            error_log('Total partial paid invoices result: ' . ($result ? $result : '0'));
            return $result ? (float)$result : 0;
        } catch (Exception $e) {
            error_log('خطأ أثناء حساب إجمالي الفواتير المدفوعة جزئياً: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * حساب إجمالي المبيعات
     * @param array $filters فلاتر البحث (اختياري)
     * @return float إجمالي المبيعات
     */
    public function getTotalSales($filters = []) {
        try {
            // إنشاء مسار ملف السجل
            $logDir = dirname(__DIR__, 2) . '/logs';
            if (!is_dir($logDir)) {
                mkdir($logDir, 0755, true);
            }
            $logFile = $logDir . '/total_sales_debug_' . date('Y-m-d') . '.log';

            // دالة لتسجيل المعلومات في الملف
            $logToFile = function($message) use ($logFile) {
                $timestamp = date('Y-m-d H:i:s');
                file_put_contents(
                    $logFile,
                    "[$timestamp] $message" . PHP_EOL,
                    FILE_APPEND
                );
            };

            // تسجيل بداية الاستدعاء والفلاتر المستخدمة
            $logToFile("=== بداية استدعاء getTotalSales() ====");
            $logToFile("الفلاتر المستخدمة: " . json_encode($filters, JSON_UNESCAPED_UNICODE));

            // استخدام LEFT JOIN مع جدول end_days للحصول على تاريخ إقفال اليوم
            $sql = "SELECT SUM(i.final_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id";

            $whereConditions = [];
            $bindings = [];

            // التأكد من احتساب المبيعات للفواتير المدفوعة فقط
            $whereConditions[] = "i.payment_status = 'paid'";
            $logToFile("إضافة شرط الدفع الافتراضي: i.payment_status = 'paid'");

            // تطبيق الفلاتر
            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "i.branch_id = :sales_branch_id";
                $bindings[':sales_branch_id'] = $filters['branch_id'];
                $logToFile("إضافة فلتر الفرع: i.branch_id = " . $filters['branch_id']);
            }

            if (!empty($filters['employee_id'])) {
                $whereConditions[] = "i.employee_id = :sales_employee_id";
                $bindings[':sales_employee_id'] = $filters['employee_id'];
                $logToFile("إضافة فلتر الموظف: i.employee_id = " . $filters['employee_id']);
            }

            if (!empty($filters['payment_method'])) {
                $whereConditions[] = "i.payment_method = :sales_payment_method";
                $bindings[':sales_payment_method'] = $filters['payment_method'];
                $logToFile("إضافة فلتر طريقة الدفع: i.payment_method = " . $filters['payment_method']);
            }

            // فلتر بواسطة معرف نهاية اليوم
            if (isset($filters['end_day_id'])) {
                $whereConditions[] = "i.end_day_id = :sales_end_day_id";
                $bindings[':sales_end_day_id'] = $filters['end_day_id'];
                $logToFile("إضافة فلتر نهاية اليوم: i.end_day_id = " . $filters['end_day_id']);
            }

            // إذا تم تحديد حالة الدفع في الفلاتر، استخدمها بدلاً من القيمة الافتراضية
            if (array_key_exists('payment_status', $filters)) {
                $logToFile("تم تحديد حالة الدفع في الفلاتر: " . ($filters['payment_status'] ?? 'null'));

                // إزالة الشرط الافتراضي لحالة الدفع
                $whereConditions = array_filter($whereConditions, function($condition) {
                    return $condition !== "i.payment_status = 'paid'";
                });
                $logToFile("تمت إزالة شرط الدفع الافتراضي");

                if ($filters['payment_status'] !== null) {
                    $whereConditions[] = "i.payment_status = :sales_payment_status";
                    $bindings[':sales_payment_status'] = $filters['payment_status'];
                    $logToFile("إضافة شرط حالة الدفع الجديد: i.payment_status = " . $filters['payment_status']);
                }
            }

            // فلتر التاريخ - استخدام تاريخ end_day بدلاً من created_at
            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                // للفواتير المرتبطة بـ end_day، نستخدم تاريخ end_day
                // للفواتير غير المرتبطة بـ end_day، نستخدم created_at
                $whereConditions[] = "(
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :sales_start_date AND :sales_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :sales_start_date AND :sales_end_date)
                )";
                $bindings[':sales_start_date'] = $filters['start_date'];
                $bindings[':sales_end_date'] = $filters['end_date'];
                $logToFile("إضافة فلتر التاريخ باستخدام end_day: من " . $filters['start_date'] . " إلى " . $filters['end_date']);
            } elseif (!empty($filters['date'])) {
                $whereConditions[] = "(
                    (i.end_day_id IS NOT NULL AND ed.date = :sales_filter_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) = :sales_filter_date)
                )";
                $bindings[':sales_filter_date'] = $filters['date'];
                $logToFile("إضافة فلتر تاريخ محدد باستخدام end_day: " . $filters['date']);
            } elseif (!empty($filters['month']) && !empty($filters['year'])) {
                $whereConditions[] = "(
                    (i.end_day_id IS NOT NULL AND MONTH(ed.date) = :sales_filter_month AND YEAR(ed.date) = :sales_filter_year) OR
                    (i.end_day_id IS NULL AND MONTH(i.created_at) = :sales_filter_month AND YEAR(i.created_at) = :sales_filter_year)
                )";
                $bindings[':sales_filter_month'] = $filters['month'];
                $bindings[':sales_filter_year'] = $filters['year'];
                $logToFile("إضافة فلتر الشهر والسنة باستخدام end_day: " . $filters['month'] . "/" . $filters['year']);
            }

            // إضافة شروط WHERE إذا وجدت
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            // تسجيل الاستعلام النهائي
            $logToFile("الاستعلام النهائي: " . $sql);
            $logToFile("المعلمات المرتبطة: " . json_encode($bindings, JSON_UNESCAPED_UNICODE));

            // تسجيل الاستعلام للتحقق في error_log أيضاً
            error_log('SQL Query for getTotalSales: ' . $sql);
            foreach ($bindings as $param => $value) {
                error_log('Param ' . $param . ' = ' . $value);
            }

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
                $logToFile("ربط المعلمة: $param = $value");
            }

            // تنفيذ الاستعلام والحصول على النتيجة
            $result = $this->db->fetchColumn();

            // تسجيل النتيجة
            $logToFile("نتيجة الاستعلام: " . ($result !== false ? $result : 'لا توجد نتائج'));
            $logToFile("=== نهاية استدعاء getTotalSales() ====");

            error_log('Total sales result: ' . ($result ? $result : '0'));
            return $result ? (float)$result : 0;
        } catch (Exception $e) {
            // تسجيل الخطأ في ملف السجل
            $logDir = dirname(__DIR__, 2) . '/logs';
            $logFile = $logDir . '/total_sales_debug_' . date('Y-m-d') . '.log';
            $timestamp = date('Y-m-d H:i:s');
            file_put_contents(
                $logFile,
                "[$timestamp] خطأ أثناء حساب إجمالي المبيعات: " . $e->getMessage() . PHP_EOL,
                FILE_APPEND
            );

            error_log('خطأ أثناء حساب إجمالي المبيعات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * حساب إجمالي الخصومات
     * @param array $filters فلاتر البحث (اختياري)
     * @return float إجمالي الخصومات
     */
    public function getTotalDiscounts($filters = []) {
        try {
            // استخدام LEFT JOIN مع جدول end_days للحصول على تاريخ إقفال اليوم
            $sql = "SELECT SUM(i.discount_amount) FROM invoices i LEFT JOIN end_days ed ON i.end_day_id = ed.id";

            $whereConditions = [];
            $bindings = [];

            // التأكد من احتساب الخصومات للفواتير المدفوعة فقط
            $whereConditions[] = "i.payment_status = 'paid'";

            // تطبيق الفلاتر
            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "i.branch_id = :disc_branch_id";
                $bindings[':disc_branch_id'] = $filters['branch_id'];
            }

            if (!empty($filters['employee_id'])) {
                $whereConditions[] = "i.employee_id = :disc_employee_id";
                $bindings[':disc_employee_id'] = $filters['employee_id'];
            }

            if (!empty($filters['payment_method'])) {
                $whereConditions[] = "i.payment_method = :disc_payment_method";
                $bindings[':disc_payment_method'] = $filters['payment_method'];
            }

            // فلتر بواسطة معرف نهاية اليوم
            if (isset($filters['end_day_id'])) {
                $whereConditions[] = "i.end_day_id = :disc_end_day_id";
                $bindings[':disc_end_day_id'] = $filters['end_day_id'];
            }

            // إذا تم تحديد حالة الدفع في الفلاتر، استخدمها بدلاً من القيمة الافتراضية
            if (array_key_exists('payment_status', $filters)) {
                // إزالة الشرط الافتراضي لحالة الدفع
                $whereConditions = array_filter($whereConditions, function($condition) {
                    return $condition !== "i.payment_status = 'paid'";
                });

                if ($filters['payment_status'] !== null) {
                    $whereConditions[] = "i.payment_status = :disc_payment_status";
                    $bindings[':disc_payment_status'] = $filters['payment_status'];
                }
            }

            // فلتر التاريخ - استخدام تاريخ end_day بدلاً من created_at
            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                $whereConditions[] = "(
                    (i.end_day_id IS NOT NULL AND ed.date BETWEEN :disc_start_date AND :disc_end_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :disc_start_date AND :disc_end_date)
                )";
                $bindings[':disc_start_date'] = $filters['start_date'];
                $bindings[':disc_end_date'] = $filters['end_date'];
            } elseif (!empty($filters['date'])) {
                $whereConditions[] = "(
                    (i.end_day_id IS NOT NULL AND ed.date = :disc_date) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) = :disc_date)
                )";
                $bindings[':disc_date'] = $filters['date'];

                // تسجيل الاستعلام للتحقق
                error_log('Getting discounts for specific date: ' . $filters['date']);
            } elseif (!empty($filters['month']) && !empty($filters['year'])) {
                $whereConditions[] = "(
                    (i.end_day_id IS NOT NULL AND MONTH(ed.date) = :disc_month AND YEAR(ed.date) = :disc_year) OR
                    (i.end_day_id IS NULL AND MONTH(i.created_at) = :disc_month AND YEAR(i.created_at) = :disc_year)
                )";
                $bindings[':disc_month'] = $filters['month'];
                $bindings[':disc_year'] = $filters['year'];
            }

            // إضافة شروط WHERE إذا وجدت
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            // تسجيل الاستعلام للتحقق
            error_log('SQL Query for getTotalDiscounts: ' . $sql);
            foreach ($bindings as $param => $value) {
                error_log('Param ' . $param . ' = ' . $value);
            }

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            $result = $this->db->fetchColumn();
            error_log('Total discounts result: ' . ($result ? $result : '0'));
            return $result ? (float)$result : 0;
        } catch (Exception $e) {
            error_log('خطأ أثناء حساب إجمالي الخصومات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على تقرير المبيعات
     * @param array $filters فلاتر البحث (اختياري)
     * @return array تقرير المبيعات
     */
    public function getSalesReport($filters = []) {
        try {
            $report = [
                'filters' => $filters,
                'summary' => [],
                'payment_methods' => [],
                'daily_sales' => [],
                'top_services' => [],
                'top_products' => [],
                'invoices' => []
            ];

            // إجمالي المبيعات
            $report['summary']['total_sales'] = $this->getTotalSales($filters);
            $report['summary']['total_invoices'] = $this->getInvoicesCount($filters);

            // المبيعات حسب طريقة الدفع
            $paymentMethods = ['cash', 'card', 'other'];
            foreach ($paymentMethods as $method) {
                $methodFilters = $filters;
                $methodFilters['payment_method'] = $method;
                $total = $this->getTotalSales($methodFilters);

                $report['payment_methods'][] = [
                    'method' => $method,
                    'total' => $total
                ];
            }

            // المبيعات اليومية
            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                // الحصول على الفواتير الفعلية في الفترة المحددة
                $invoices = $this->getInvoicesByDateRange($filters['start_date'], $filters['end_date'], $filters);

                // تسجيل عدد الفواتير المسترجعة
                error_log('Number of invoices retrieved: ' . count($invoices));

                // تجميع المبيعات حسب اليوم
                $dailySalesMap = [];

                // إذا كان هناك فواتير، قم بتجميعها حسب اليوم
                if (!empty($invoices)) {
                    foreach ($invoices as $invoice) {
                        // تسجيل بيانات الفاتورة للتحقق
                        error_log('Invoice ID: ' . $invoice['id'] . ', Date: ' . $invoice['date'] . ', Amount: ' . $invoice['final_amount']);

                        $date = substr($invoice['date'], 0, 10); // الحصول على التاريخ فقط (YYYY-MM-DD)

                        if (!isset($dailySalesMap[$date])) {
                            $dateObj = new DateTime($date);
                            $dailySalesMap[$date] = [
                                'date' => $date,
                                'day' => $dateObj->format('d'),
                                'month' => $dateObj->format('m'),
                                'year' => $dateObj->format('Y'),
                                'total' => 0,
                                'invoices_count' => 0
                            ];
                        }

                        // إضافة قيمة الفاتورة إلى إجمالي اليوم
                        $dailySalesMap[$date]['total'] += floatval($invoice['final_amount']);
                        $dailySalesMap[$date]['invoices_count']++;

                        // تسجيل المجموع الجديد لليوم
                        error_log('Updated total for ' . $date . ': ' . $dailySalesMap[$date]['total'] . ', Invoices count: ' . $dailySalesMap[$date]['invoices_count']);
                    }
                } else {
                    error_log('No invoices found in the specified date range');
                }

                // تسجيل عدد الأيام التي تم تجميعها
                error_log('Number of days with sales: ' . count($dailySalesMap));

                // إنشاء سجلات للأيام التي لا توجد بها فواتير
                $startDate = new DateTime($filters['start_date']);
                $endDate = new DateTime($filters['end_date']);
                $endDate->modify('+1 day'); // لتضمين تاريخ النهاية

                $currentDate = clone $startDate;
                while ($currentDate < $endDate) {
                    $dateStr = $currentDate->format('Y-m-d');

                    // إذا لم يكن هناك سجل لهذا اليوم، قم بإنشائه مع قيمة صفر
                    if (!isset($dailySalesMap[$dateStr])) {
                        $dailySalesMap[$dateStr] = [
                            'date' => $dateStr,
                            'day' => $currentDate->format('d'),
                            'month' => $currentDate->format('m'),
                            'year' => $currentDate->format('Y'),
                            'total' => 0,
                            'invoices_count' => 0
                        ];
                    }

                    $currentDate->modify('+1 day');
                }

                // تحويل المصفوفة الترابطية إلى مصفوفة عادية
                $dailyData = array_values($dailySalesMap);

                // ترتيب البيانات حسب التاريخ
                usort($dailyData, function($a, $b) {
                    return strcmp($a['date'], $b['date']);
                });

                // تسجيل البيانات النهائية
                error_log('Final daily sales data: ' . json_encode($dailyData));

                $report['daily_sales'] = $dailyData;

                // إضافة الفواتير للتقرير التفصيلي
                if (isset($filters['report_type']) && $filters['report_type'] === 'detailed') {
                    $report['invoices'] = $invoices;
                }
            }

            // الخدمات الأكثر مبيعًا
            $serviceModel = new Service($this->db);
            $report['top_services'] = $serviceModel->getTopServices(5,
                $filters['branch_id'] ?? null,
                $filters['period'] ?? 'month'
            );

            // المنتجات الأكثر مبيعًا
            $report['top_products'] = $this->getTopProducts($filters);

            return $report;
        } catch (Exception $e) {
            error_log('خطأ أثناء إنشاء تقرير المبيعات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * المنتجات الأكثر مبيعًا
     * @param array $filters فلاتر البحث (اختياري)
     * @return array المنتجات الأكثر مبيعًا
     */
    public function getTopProducts($filters = []) {
        try {
            $sql = "SELECT p.id, p.name, p.price, p.cost,
                           SUM(ii.quantity) as sold_quantity,
                           SUM(ii.total) as total_sales,
                           COUNT(DISTINCT i.id) as orders_count
                    FROM products p
                    JOIN invoice_items ii ON p.id = ii.item_id AND ii.item_type = 'product'
                    JOIN invoices i ON ii.invoice_id = i.id";

            $whereConditions = [];
            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "i.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                $whereConditions[] = "i.created_at BETWEEN :start_date AND :end_date";
                $bindings[':start_date'] = $filters['start_date'] . ' 00:00:00';
                $bindings[':end_date'] = $filters['end_date'] . ' 23:59:59';
            } elseif (!empty($filters['month']) && !empty($filters['year'])) {
                $whereConditions[] = "MONTH(i.created_at) = :month AND YEAR(i.created_at) = :year";
                $bindings[':month'] = $filters['month'];
                $bindings[':year'] = $filters['year'];
            }

            // إضافة شروط WHERE
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            // تجميع حسب المنتج وترتيب النتائج
            $sql .= " GROUP BY p.id, p.name, p.price, p.cost
                      ORDER BY sold_quantity DESC
                      LIMIT 5";

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع المنتجات الأكثر مبيعًا: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * مقارنة المبيعات بين فترتين
     * @param array $period1 الفترة الأولى (start_date, end_date)
     * @param array $period2 الفترة الثانية (start_date, end_date)
     * @param int $branchId معرف الفرع (اختياري)
     * @return array نتائج المقارنة
     */
    public function compareSalesPeriods($period1, $period2, $branchId = null) {
        try {
            // الحصول على إجمالي المبيعات للفترة الأولى
            $filters1 = [
                'start_date' => $period1['start_date'],
                'end_date' => $period1['end_date']
            ];

            if ($branchId) {
                $filters1['branch_id'] = $branchId;
            }

            $total1 = $this->getTotalSales($filters1);

            // الحصول على إجمالي المبيعات للفترة الثانية
            $filters2 = [
                'start_date' => $period2['start_date'],
                'end_date' => $period2['end_date']
            ];

            if ($branchId) {
                $filters2['branch_id'] = $branchId;
            }

            $total2 = $this->getTotalSales($filters2);

            // حساب الفرق والنسبة المئوية
            $difference = $total2 - $total1;
            $percentChange = $total1 > 0 ? ($difference / $total1) * 100 : 0;

            // الحصول على تفاصيل المبيعات حسب الخدمات للفترتين
            $serviceModel = new Service($this->db);
            $serviceComparison = [];

            // الحصول على الخدمات للفترتين
            $serviceSales1 = $this->getServiceSalesByPeriod($period1, $branchId);
            $serviceSales2 = $this->getServiceSalesByPeriod($period2, $branchId);

            // تجميع كل الخدمات من الفترتين
            $allServices = [];

            foreach ($serviceSales1 as $service) {
                $allServices[$service['service_id']] = $service['service_name'];
            }

            foreach ($serviceSales2 as $service) {
                $allServices[$service['service_id']] = $service['service_name'];
            }

            // مقارنة مبيعات الخدمات
            foreach ($allServices as $serviceId => $serviceName) {
                $serviceTotal1 = 0;
                $serviceTotal2 = 0;
                $serviceCount1 = 0;
                $serviceCount2 = 0;

                // البحث عن المبيعات في الفترة الأولى
                foreach ($serviceSales1 as $service) {
                    if ($service['service_id'] == $serviceId) {
                        $serviceTotal1 = (float)$service['total_sales'];
                        $serviceCount1 = (int)$service['count'];
                        break;
                    }
                }

                // البحث عن المبيعات في الفترة الثانية
                foreach ($serviceSales2 as $service) {
                    if ($service['service_id'] == $serviceId) {
                        $serviceTotal2 = (float)$service['total_sales'];
                        $serviceCount2 = (int)$service['count'];
                        break;
                    }
                }

                $serviceDifference = $serviceTotal2 - $serviceTotal1;
                $servicePercentChange = $serviceTotal1 > 0 ? ($serviceDifference / $serviceTotal1) * 100 : 0;

                $serviceComparison[] = [
                    'service_id' => $serviceId,
                    'service_name' => $serviceName,
                    'period1_total' => $serviceTotal1,
                    'period2_total' => $serviceTotal2,
                    'period1_count' => $serviceCount1,
                    'period2_count' => $serviceCount2,
                    'total_difference' => $serviceDifference,
                    'total_percent_change' => $servicePercentChange
                ];
            }

            // ترتيب التقرير حسب قيمة الفرق
            usort($serviceComparison, function($a, $b) {
                return abs($b['total_difference']) - abs($a['total_difference']);
            });

            return [
                'period1' => [
                    'start_date' => $period1['start_date'],
                    'end_date' => $period1['end_date'],
                    'total' => $total1
                ],
                'period2' => [
                    'start_date' => $period2['start_date'],
                    'end_date' => $period2['end_date'],
                    'total' => $total2
                ],
                'difference' => $difference,
                'percent_change' => $percentChange,
                'services' => $serviceComparison
            ];
        } catch (Exception $e) {
            error_log('خطأ أثناء مقارنة فترات المبيعات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على مبيعات الخدمات لفترة محددة
     * @param array $period الفترة (start_date, end_date)
     * @param int $branchId معرف الفرع (اختياري)
     * @return array مبيعات الخدمات
     */
    private function getServiceSalesByPeriod($period, $branchId = null) {
        try {
            $sql = "SELECT s.id as service_id, s.name as service_name,
                           SUM(ii.total) as total_sales,
                           COUNT(DISTINCT ii.id) as count
                    FROM invoice_items ii
                    JOIN invoices i ON ii.invoice_id = i.id
                    JOIN services s ON ii.item_id = s.id
                    WHERE ii.item_type = 'service'
                    AND i.created_at BETWEEN :start_date AND :end_date";

            $bindings = [
                ':start_date' => $period['start_date'] . ' 00:00:00',
                ':end_date' => $period['end_date'] . ' 23:59:59'
            ];

            if ($branchId) {
                $sql .= " AND i.branch_id = :branch_id";
                $bindings[':branch_id'] = $branchId;
            }

            $sql .= " GROUP BY s.id, s.name
                      ORDER BY total_sales DESC";

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع مبيعات الخدمات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * إنشاء تقرير شامل للمبيعات
     * @param array $filters فلاتر البحث
     * @return array التقرير الشامل
     */
    public function generateSalesReport($filters) {
        try {
            $report = [
                'filters' => $filters,
                'summary' => [],
                'payment_methods' => [],
                'daily_sales' => [],
                'top_services' => [],
                'top_products' => [],
                'customer_breakdown' => [],
                'employee_performance' => []
            ];

            // إجمالي المبيعات
            $report['summary']['total_sales'] = $this->getTotalSales($filters);
            $report['summary']['total_invoices'] = $this->getInvoicesCount($filters);

            // المبيعات حسب طريقة الدفع
            $paymentMethods = ['cash', 'card', 'other'];
            foreach ($paymentMethods as $method) {
                $methodFilters = $filters;
                $methodFilters['payment_method'] = $method;
                $total = $this->getTotalSales($methodFilters);

                $report['payment_methods'][] = [
                    'method' => $method,
                    'total' => $total
                ];
            }

            // المبيعات اليومية (إذا كان نطاق التاريخ متاحًا)
            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                $startDate = new DateTime($filters['start_date']);
                $endDate = new DateTime($filters['end_date']);
                $endDate->modify('+1 day'); // لتضمين تاريخ النهاية

                $dailyData = [];
                $currentDate = clone $startDate;

                while ($currentDate < $endDate) {
                    $dateStr = $currentDate->format('Y-m-d');
                    $dailyFilters = $filters;
                    $dailyFilters['date'] = $dateStr;

                    $dailyTotal = $this->getTotalSales($dailyFilters);

                    $dailyData[] = [
                        'date' => $dateStr,
                        'day' => $currentDate->format('d'),
                        'month' => $currentDate->format('m'),
                        'year' => $currentDate->format('Y'),
                        'total' => $dailyTotal
                    ];

                    $currentDate->modify('+1 day');
                }

                $report['daily_sales'] = $dailyData;
            }

            // أفضل الخدمات
            $serviceModel = new Service($this->db);
            $report['top_services'] = $serviceModel->getTopServices(
                5,
                $filters['branch_id'] ?? null,
                $filters['period'] ?? 'month'
            );

            // أفضل المنتجات
            $report['top_products'] = $this->getTopProducts($filters);

            // تفصيل العملاء
            $report['customer_breakdown'] = $this->getCustomerSalesBreakdown($filters);

            // أداء الموظفين
            $report['employee_performance'] = $this->getEmployeeSalesPerformance($filters);

            return $report;
        } catch (Exception $e) {
            error_log('خطأ أثناء إنشاء تقرير المبيعات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * تفصيل مبيعات العملاء
     * @param array $filters فلاتر البحث
     * @return array تفصيل مبيعات العملاء
     */
    private function getCustomerSalesBreakdown($filters) {
        try {
            $sql = "SELECT c.id, c.name, c.phone,
                           COUNT(DISTINCT i.id) as total_invoices,
                           SUM(i.final_amount) as total_sales
                    FROM invoices i
                    LEFT JOIN customers c ON i.customer_id = c.id";

            $whereConditions = ["c.id IS NOT NULL"];
            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "i.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                $whereConditions[] = "i.created_at BETWEEN :start_date AND :end_date";
                $bindings[':start_date'] = $filters['start_date'] . ' 00:00:00';
                $bindings[':end_date'] = $filters['end_date'] . ' 23:59:59';
            } elseif (!empty($filters['month']) && !empty($filters['year'])) {
                $whereConditions[] = "MONTH(i.created_at) = :month AND YEAR(i.created_at) = :year";
                $bindings[':month'] = $filters['month'];
                $bindings[':year'] = $filters['year'];
            }

            // إضافة شروط WHERE
            $sql .= " WHERE " . implode(' AND ', $whereConditions);

            // تجميع حسب العميل وترتيب النتائج
            $sql .= " GROUP BY c.id, c.name, c.phone
                      ORDER BY total_sales DESC
                      LIMIT 10";

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع تفصيل مبيعات العملاء: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * أداء الموظفين في المبيعات
     * @param array $filters فلاتر البحث
     * @return array أداء الموظفين
     */
    private function getEmployeeSalesPerformance($filters) {
        try {
            $sql = "SELECT e.id, e.name, e.position,
                           COUNT(DISTINCT ii.invoice_id) as total_invoices,
                           SUM(ii.total) as total_sales,
                           AVG(ii.total) as average_invoice_value
                    FROM employees e
                    JOIN invoice_items ii ON e.id = ii.employee_id
                    JOIN invoices i ON ii.invoice_id = i.id";

            $whereConditions = ["ii.item_type = 'service'"];
            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "i.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                $whereConditions[] = "i.created_at BETWEEN :start_date AND :end_date";
                $bindings[':start_date'] = $filters['start_date'] . ' 00:00:00';
                $bindings[':end_date'] = $filters['end_date'] . ' 23:59:59';
            } elseif (!empty($filters['month']) && !empty($filters['year'])) {
                $whereConditions[] = "MONTH(i.created_at) = :month AND YEAR(i.created_at) = :year";
                $bindings[':month'] = $filters['month'];
                $bindings[':year'] = $filters['year'];
            }

            // إضافة شروط WHERE
            $sql .= " WHERE " . implode(' AND ', $whereConditions);

            // تجميع حسب الموظف وترتيب النتائج
            $sql .= " GROUP BY e.id, e.name, e.position
                      ORDER BY total_sales DESC
                      LIMIT 10";

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع أداء الموظفين في المبيعات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * إعادة طباعة فاتورة
     * @param int $invoiceId معرف الفاتورة
     * @return array بيانات الفاتورة للطباعة
     */
    public function prepareInvoiceForPrinting($invoiceId) {
        try {
            // استرجاع بيانات الفاتورة الرئيسية
            $invoice = $this->getInvoiceById($invoiceId);

            if (!$invoice) {
                throw new Exception('الفاتورة غير موجودة');
            }

            // استرجاع عناصر الفاتورة
            $invoice['items'] = $this->getInvoiceItems($invoiceId);

            // تحويل المبلغ إلى كلمات (اختياري)
            $invoice['total_amount_words'] = amountToArabicWords(
                $invoice['final_amount'],
                'ريال'
            );

            return $invoice;
        } catch (Exception $e) {
            error_log('خطأ أثناء التحضير للطباعة: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * تطبيق خصم على فاتورة
     * @param int $invoiceId معرف الفاتورة
     * @param float $discountAmount مبلغ الخصم
     * @param string $discountType نوع الخصم (percentage/amount)
     * @return bool نجاح أو فشل العملية
     */
    public function applyDiscount($invoiceId, $discountAmount, $discountType = 'amount') {
        try {
            $this->db->beginTransaction();

            // تسجيل معلومات الخصم للتصحيح
            error_log("Applying discount to invoice #{$invoiceId}: amount={$discountAmount}, type={$discountType}");

            // استرجاع بيانات الفاتورة الحالية
            $this->db->prepare("SELECT total_amount, final_amount FROM invoices WHERE id = :id");
            $this->db->bind(':id', $invoiceId);
            $invoice = $this->db->fetch();

            if (!$invoice) {
                throw new Exception('الفاتورة غير موجودة');
            }

            // حساب الخصم
            $totalAmount = $invoice['total_amount'];
            error_log("Invoice #{$invoiceId} total amount: {$totalAmount}");
            // ملاحظة: نستخدم المبلغ الإجمالي لحساب الخصم وتوزيعه

            if ($discountType === 'percentage') {
                $originalDiscountPercentage = $discountAmount; // حفظ النسبة المئوية الأصلية
                error_log("Original percentage discount: {$originalDiscountPercentage}%");
                $discountAmount = $totalAmount * ($discountAmount / 100);
                $discountPercentage = $discountAmount / $totalAmount; // نسبة الخصم من الإجمالي
                error_log("Calculated discount amount: {$discountAmount} EGP");
            } else {
                // تحويل المبلغ إلى نسبة مئوية
                $discountPercentage = $discountAmount / $totalAmount;
                error_log("Fixed discount: {$discountAmount} EGP, as percentage: {$discountPercentage}%");
            }

            // التأكد من عدم تجاوز الخصم للمبلغ الإجمالي
            $newFinalAmount = max(0, $totalAmount - $discountAmount);

            // استرجاع عناصر الفاتورة
            $this->db->prepare("SELECT id, item_type, item_id, quantity, price, total, employee_id FROM invoice_items WHERE invoice_id = :invoice_id");
            $this->db->bind(':invoice_id', $invoiceId);
            $items = $this->db->fetchAll();

            // توزيع الخصم على العناصر بالتناسب
            foreach ($items as $item) {
                // حساب الخصم لهذا العنصر
                $itemDiscount = $item['total'] * $discountPercentage;
                $newItemTotal = $item['total'] - $itemDiscount;

                // تحديث العنصر بالخصم الجديد
                $this->db->prepare("UPDATE invoice_items
                                  SET discount = :discount,
                                      total = :total
                                  WHERE id = :id");
                $this->db->bind(':id', $item['id']);
                $this->db->bind(':discount', $itemDiscount);
                $this->db->bind(':total', $newItemTotal);
                $this->db->execute();
            }

            // تحديث الفاتورة
            // حفظ قيمة الخصم الأصلية للنسبة المئوية في حقل notes
            // الحصول على الملاحظات الحالية
            $this->db->prepare("SELECT notes FROM invoices WHERE id = :id");
            $this->db->bind(':id', $invoiceId);
            $this->db->execute();
            $currentNotes = $this->db->fetch()['notes'] ?? '';

            // تحديث الملاحظات لتتضمن قيمة الخصم الأصلية
            $updatedNotes = $currentNotes;

            // إزالة أي علامة خصم سابقة
            $updatedNotes = preg_replace('/\[ORIGINAL_DISCOUNT:\d+(\.\d+)?\]/', '', $updatedNotes);

            // إضافة قيمة الخصم الأصلية إذا كان الخصم بالنسبة المئوية
            if ($discountType === 'percentage') {
                $originalDiscountValue = isset($originalDiscountPercentage) ? $originalDiscountPercentage : floatval($discountAmount);
                $updatedNotes .= "\n[ORIGINAL_DISCOUNT:{$originalDiscountValue}]";
                error_log("Added original discount value to notes: {$originalDiscountValue}%");
            }

            $this->db->prepare("UPDATE invoices
                              SET discount_amount = :discount_amount,
                                  discount_type = :discount_type,
                                  final_amount = :final_amount,
                                  notes = :notes
                              WHERE id = :id");

            $this->db->bind(':id', $invoiceId);
            $this->db->bind(':discount_amount', $discountAmount);
            $this->db->bind(':discount_type', $discountType);
            $this->db->bind(':final_amount', $newFinalAmount);
            $this->db->bind(':notes', $updatedNotes);

            $this->db->execute();

            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log('خطأ أثناء تطبيق الخصم: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * إلغاء فاتورة
     * @param int $invoiceId معرف الفاتورة
     * @return bool نجاح أو فشل العملية
     */
    public function cancelInvoice($invoiceId) {
        try {
            $this->db->beginTransaction();

            // استرجاع العناصر للتسوية
            $items = $this->getInvoiceItems($invoiceId);

            // استرجاع معرف الفرع
            $this->db->prepare("SELECT branch_id FROM invoices WHERE id = :id");
            $this->db->bind(':id', $invoiceId);
            $invoice = $this->db->fetch();

            if (!$invoice) {
                throw new Exception('الفاتورة غير موجودة');
            }

            $branchId = $invoice['branch_id'];

            // إعادة المخزون
            $inventoryModel = new Inventory($this->db);

            foreach ($items as $item) {
                if ($item['item_type'] === 'product') {
                    $inventoryModel->updateStockAfterCancelSale(
                        $item['item_id'],
                        $item['quantity'],
                        $branchId
                    );
                }
            }

            // تحديث حالة الفاتورة
            $this->db->prepare("UPDATE invoices
                              SET payment_status = 'cancelled',
                                  notes = CONCAT(notes, ' - تم الإلغاء في ', NOW())
                              WHERE id = :id");
            $this->db->bind(':id', $invoiceId);
            $this->db->execute();

            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log('خطأ أثناء إلغاء الفاتورة: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على آخر رقم فاتورة
     * @param int $branchId معرف الفرع (اختياري)
     * @return string آخر رقم فاتورة أو فارغ إذا لم توجد فواتير
     */
    public function getLastInvoiceNumber($branchId = null) {
        try {
            $sql = "SELECT invoice_number FROM invoices";
            $params = [];

            if ($branchId) {
                $sql .= " WHERE branch_id = :branch_id";
                $params[':branch_id'] = $branchId;
            }

            $sql .= " ORDER BY created_at DESC LIMIT 1";

            $this->db->prepare($sql);

            foreach ($params as $param => $value) {
                $this->db->bind($param, $value);
            }

            $result = $this->db->fetch();
            return $result ? $result['invoice_number'] : '';
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع آخر رقم فاتورة: ' . $e->getMessage());
            return '';
        }
    }

    /**
     * إنشاء فاتورة جديدة بدون بدء معاملة جديدة
     * @param array $invoiceData بيانات الفاتورة
     * @param array $items العناصر (خدمات/منتجات)
     * @return int|false معرف الفاتورة الجديدة أو false إذا فشلت العملية
     */
    public function createInvoiceWithoutTransaction($invoiceData, $items) {
        try {
            // توليد رقم فاتورة فريد إذا لم يتم توفيره
            if (empty($invoiceData['invoice_number'])) {
                $invoiceData['invoice_number'] = $this->generateInvoiceNumber($invoiceData['branch_id'] ?? null);
            }

            // التحقق من وجود نهاية يوم مفتوحة للفرع إذا لم يتم توفير معرف نهاية اليوم
            if (empty($invoiceData['end_day_id'])) {
                $branchId = $invoiceData['branch_id'] ?? $_SESSION['user_branch_id'];
                $this->db->prepare("SELECT id FROM end_days
                              WHERE branch_id = :branch_id
                              AND closed_at IS NULL");
                $this->db->bind(':branch_id', $branchId);
                $openEndDay = $this->db->fetch();

                if (!$openEndDay) {
                    throw new Exception('لا يمكن إنشاء فاتورة بدون بدء يوم العمل للفرع', 400);
                }

                // إضافة معرف نهاية اليوم إلى بيانات الفاتورة فقط إذا كانت الفاتورة مدفوعة
                $paymentStatus = $invoiceData['payment_status'] ?? 'paid';
                if ($paymentStatus === 'paid') {
                    $invoiceData['end_day_id'] = $openEndDay['id'];
                } else {
                    // لا تربط الفواتير غير المدفوعة أو المدفوعة جزئيًا بيوم العمل
                    $invoiceData['end_day_id'] = null;
                }
            }

            // إدراج بيانات الفاتورة الرئيسية
            $currentDateTime = date('Y-m-d H:i:s'); // الحصول على التاريخ والوقت الحالي من PHP

            $this->db->prepare("INSERT INTO invoices (
                invoice_number, customer_id, employee_id, cashier_id,
                total_amount, discount_amount, discount_type, tax_amount, final_amount,
                payment_method, payment_status, notes, branch_id, end_day_id, created_at
            ) VALUES (
                :invoice_number, :customer_id, :employee_id, :cashier_id,
                :total_amount, :discount_amount, :discount_type, :tax_amount, :final_amount,
                :payment_method, :payment_status, :notes, :branch_id, :end_day_id, :created_at
            )");

            $this->db->bind(':invoice_number', $invoiceData['invoice_number']);
            $this->db->bind(':customer_id', $invoiceData['customer_id'] ?? null);
            $this->db->bind(':employee_id', $invoiceData['employee_id'] ?? null);
            $this->db->bind(':cashier_id', $invoiceData['cashier_id'] ?? $_SESSION['user_id']);
            $this->db->bind(':total_amount', $invoiceData['total_amount']);
            $this->db->bind(':discount_amount', $invoiceData['discount_amount'] ?? 0);
            $this->db->bind(':discount_type', $invoiceData['discount_type'] ?? 'amount');
            $this->db->bind(':tax_amount', $invoiceData['tax_amount'] ?? 0);
            $this->db->bind(':final_amount', $invoiceData['final_amount']);
            $this->db->bind(':payment_method', $invoiceData['payment_method'] ?? 'cash');
            $this->db->bind(':payment_status', $invoiceData['payment_status'] ?? 'paid');
            $this->db->bind(':notes', $invoiceData['notes'] ?? null);
            $this->db->bind(':branch_id', $invoiceData['branch_id'] ?? $_SESSION['user_branch_id']);
            $this->db->bind(':end_day_id', $invoiceData['end_day_id'] ?? null);
            $this->db->bind(':created_at', $currentDateTime);

            $this->db->execute();
            $invoiceId = (int)$this->db->lastInsertId();

            // إضافة عناصر الفاتورة
            $this->addInvoiceItems($invoiceId, $items);

            // تحديث المخزون
            $this->updateInventoryAfterSale($items, $invoiceData['branch_id'] ?? $_SESSION['user_branch_id']);

            return $invoiceId;
        } catch (Exception $e) {
            error_log('خطأ أثناء إنشاء الفاتورة: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * التحقق من وجود رقم فاتورة
     * @param string $invoiceNumber رقم الفاتورة
     * @return bool
     */
    public function isInvoiceNumberExists($invoiceNumber) {
        $this->db->prepare("SELECT COUNT(*) as count FROM invoices WHERE invoice_number = :invoice_number");
        $this->db->bind(':invoice_number', $invoiceNumber);
        $result = $this->db->fetch(); // استخدام fetch بدلاً من single
        return $result['count'] > 0;
    }

    /**
     * نهاية الكلاس
     */
}
