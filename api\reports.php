<?php
/**
 * API Reports
 * نقطة الوصول للتقارير المختلفة في النظام
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// الحصول على نوع التقرير المطلوب
$report_type = $_GET['type'] ?? '';

// الاستجابة الافتراضية
$response = [
    'success' => false,
    'message' => 'نوع التقرير غير محدد أو غير صالح',
    'data' => null
];

// معالجة الطلب حسب نوع التقرير
try {
    switch ($report_type) {
        case 'sales':
            // التحقق من الصلاحية
            if (!hasPermission('reports_sales')) {
                throw new Exception('ليس لديك صلاحية الوصول لهذا التقرير');
            }

            // معالجة تقرير المبيعات
            $response = handleSalesReport();
            break;

        case 'employees':
            // التحقق من الصلاحية
            if (!hasPermission('reports_employees')) {
                throw new Exception('ليس لديك صلاحية الوصول لهذا التقرير');
            }

            // معالجة تقرير الموظفين
            $response = handleEmployeesReport();
            break;

        case 'services':
            // التحقق من الصلاحية
            if (!hasPermission('reports_services')) {
                throw new Exception('ليس لديك صلاحية الوصول لهذا التقرير');
            }

            // معالجة تقرير الخدمات
            $response = handleServicesReport();
            break;

        case 'expenses':
            // التحقق من الصلاحية
            if (!hasPermission('reports_expenses')) {
                throw new Exception('ليس لديك صلاحية الوصول لهذا التقرير');
            }

            // معالجة تقرير المصروفات
            $response = handleExpensesReport();
            break;

        case 'inventory':
            // التحقق من الصلاحية
            if (!hasPermission('inventory_view')) {
                throw new Exception('ليس لديك صلاحية الوصول لهذا التقرير');
            }

            // معالجة تقرير المخزون
            $response = handleInventoryReport();
            break;

        case 'customers':
            // التحقق من الصلاحية
            if (!hasPermission('customers_view')) {
                throw new Exception('ليس لديك صلاحية الوصول لهذا التقرير');
            }

            // معالجة تقرير العملاء
            $response = handleCustomersReport();
            break;

        case 'attendance':
            // التحقق من الصلاحية
            if (!hasPermission('employees_view')) {
                throw new Exception('ليس لديك صلاحية الوصول لهذا التقرير');
            }

            // معالجة تقرير الحضور
            $response = handleAttendanceReport();
            break;

        case 'compare_periods':
            // التحقق من الصلاحية
            if (!hasPermission('reports_sales')) {
                throw new Exception('ليس لديك صلاحية الوصول لهذا التقرير');
            }

            // معالجة تقرير مقارنة الفترات
            $response = handlePeriodsComparisonReport();
            break;

        case 'comprehensive':
            // التحقق من الصلاحية - يجب أن يكون لديه صلاحية عرض التقارير أو المصروفات
            if (!hasPermission('reports_sales') && !hasPermission('reports_expenses')) {
                throw new Exception('ليس لديك صلاحية الوصول لهذا التقرير');
            }

            // معالجة التقرير الشامل
            $response = handleComprehensiveReport();
            break;

        default:
            throw new Exception('نوع التقرير غير مدعوم');
    }

} catch (Exception $e) {
    $response['message'] = $e->getMessage();
}

// إرجاع النتيجة بتنسيق JSON
header('Content-Type: application/json; charset=UTF-8');
echo json_encode($response, JSON_UNESCAPED_UNICODE);
exit;

/**
 * معالجة تقرير المبيعات
 * @return array استجابة التقرير
 */
function handleSalesReport() {
    global $db;

    // جلب بيانات الفلترة
    $filters = getReportFilters();

    // تسجيل الفلاتر للتحقق
    error_log('Sales report filters: ' . json_encode($filters));

    // إنشاء كائن التقارير
    $reportModel = new Report($db);

    // جلب تقرير المبيعات
    $report = $reportModel->generateSalesReport($filters);

    // تسجيل بيانات التقرير للتحقق
    error_log('Generated sales report with ' . count($report['daily_sales']) . ' days of data');

    return [
        'success' => true,
        'message' => 'تم جلب تقرير المبيعات بنجاح',
        'data' => $report
    ];
}

/**
 * معالجة تقرير الموظفين
 * @return array استجابة التقرير
 */
function handleEmployeesReport() {
    global $db;

    // جلب بيانات الفلترة
    $filters = getReportFilters();

    // إنشاء كائن التقارير
    $reportModel = new Report($db);

    // جلب تقرير الموظفين
    $report = $reportModel->generateEmployeeReport($filters);

    return [
        'success' => true,
        'message' => 'تم جلب تقرير الموظفين بنجاح',
        'data' => $report
    ];
}

/**
 * معالجة تقرير الخدمات
 * @return array استجابة التقرير
 */
function handleServicesReport() {
    global $db;

    // جلب بيانات الفلترة
    $filters = getReportFilters();

    // إنشاء كائن الخدمات
    $serviceModel = new Service($db);

    // جلب تقرير الخدمات
    $report = $serviceModel->generateServicesReport($filters);

    return [
        'success' => true,
        'message' => 'تم جلب تقرير الخدمات بنجاح',
        'data' => $report
    ];
}

/**
 * معالجة تقرير المصروفات
 * @return array استجابة التقرير
 */
function handleExpensesReport() {
    global $db;

    // جلب بيانات الفلترة
    $filters = getReportFilters();

    // إنشاء كائن التقارير
    $reportModel = new Report($db);

    // جلب تقرير المصروفات
    $report = $reportModel->generateExpensesReport($filters);

    return [
        'success' => true,
        'message' => 'تم جلب تقرير المصروفات بنجاح',
        'data' => $report
    ];
}

/**
 * معالجة تقرير المخزون
 * @return array استجابة التقرير
 */
function handleInventoryReport() {
    global $db;

    // جلب بيانات الفلترة
    $filters = getReportFilters();

    // إنشاء كائن التقارير
    $reportModel = new Report($db);

    // جلب تقرير المخزون
    $report = $reportModel->generateInventoryReport($filters);

    return [
        'success' => true,
        'message' => 'تم جلب تقرير المخزون بنجاح',
        'data' => $report
    ];
}

/**
 * معالجة تقرير العملاء
 * @return array استجابة التقرير
 */
function handleCustomersReport() {
    global $db;

    // جلب بيانات الفلترة
    $filters = getReportFilters();

    // إنشاء كائن العملاء
    $customerModel = new Customer($db);

    // جلب بيانات العملاء
    $customers = $customerModel->getCustomers($filters);
    $totalCustomers = $customerModel->getCustomersCount($filters);

    // جلب العملاء الأكثر زيارة
    $topCustomers = $customerModel->getTopCustomers(10, $filters['branch_id'] ?? null);

    // تجميع البيانات في تقرير
    $report = [
        'filters' => $filters,
        'total_customers' => $totalCustomers,
        'top_customers' => $topCustomers,
        'customers' => $customers
    ];

    return [
        'success' => true,
        'message' => 'تم جلب تقرير العملاء بنجاح',
        'data' => $report
    ];
}

/**
 * معالجة تقرير الحضور
 * @return array استجابة التقرير
 */
function handleAttendanceReport() {
    global $db;

    // جلب بيانات الفلترة
    $filters = getReportFilters();

    // تسجيل الفلاتر للتحقق
    error_log('فلاتر تقرير الحضور: ' . json_encode($filters));

    // التحقق من صحة الفلاتر
    if (!empty($filters['month']) && !empty($filters['year'])) {
        // تحويل الشهر والسنة إلى تاريخ بداية ونهاية
        $month = intval($filters['month']);
        $year = intval($filters['year']);

        // التحقق من صحة الشهر والسنة
        if ($month < 1 || $month > 12 || $year < 2000 || $year > 2100) {
            throw new Exception('الشهر أو السنة غير صالحين');
        }

        // تعيين تاريخ بداية ونهاية الشهر
        $startDate = sprintf('%04d-%02d-01', $year, $month);
        $endDate = date('Y-m-t', strtotime($startDate));

        $filters['start_date'] = $startDate;
        $filters['end_date'] = $endDate;

        error_log('تم تحويل الشهر/السنة إلى نطاق تاريخ: ' . $startDate . ' إلى ' . $endDate);
    } else if (empty($filters['start_date']) || empty($filters['end_date'])) {
        // إذا لم يتم تحديد نطاق تاريخ، استخدم الشهر الحالي
        $filters['start_date'] = date('Y-m-01'); // أول يوم في الشهر الحالي
        $filters['end_date'] = date('Y-m-t');   // آخر يوم في الشهر الحالي

        error_log('لم يتم تحديد نطاق تاريخ، استخدام الشهر الحالي: ' . $filters['start_date'] . ' إلى ' . $filters['end_date']);
    }

    // إنشاء كائن الموظفين
    $employeeModel = new Employee($db);

    // جلب تقرير الحضور
    $report = $employeeModel->getAttendanceReport($filters);

    // تسجيل عدد السجلات المسترجعة
    $recordsCount = isset($report['records']) ? count($report['records']) : 0;
    error_log('تم استرجاع ' . $recordsCount . ' سجل حضور');

    return [
        'success' => true,
        'message' => 'تم جلب تقرير الحضور بنجاح',
        'data' => $report
    ];
}

/**
 * معالجة تقرير مقارنة الفترات
 * @return array استجابة التقرير
 */
function handlePeriodsComparisonReport() {
    global $db;

    // جلب البيانات المطلوبة من الطلب
    $compare_type = $_GET['compare_type'] ?? 'sales';
    $period1_start = $_GET['period1_start'] ?? '';
    $period1_end = $_GET['period1_end'] ?? '';
    $period2_start = $_GET['period2_start'] ?? '';
    $period2_end = $_GET['period2_end'] ?? '';
    $branch_id = $_GET['branch_id'] ?? null;

    // التحقق من البيانات
    if (empty($period1_start) || empty($period1_end) || empty($period2_start) || empty($period2_end)) {
        throw new Exception('يجب تحديد فترتي المقارنة');
    }

    // تعريف فترات المقارنة
    $period1 = [
        'start_date' => $period1_start,
        'end_date' => $period1_end
    ];

    $period2 = [
        'start_date' => $period2_start,
        'end_date' => $period2_end
    ];

    // تنفيذ المقارنة حسب النوع
    $report = null;
    $message = '';

    switch ($compare_type) {
        case 'sales':
            $invoiceModel = new Invoice($db);
            $report = $invoiceModel->compareSalesPeriods($period1, $period2, $branch_id);
            $message = 'تم جلب تقرير مقارنة المبيعات بنجاح';
            break;

        case 'services':
            $serviceModel = new Service($db);
            $report = $serviceModel->compareServicesPeriods($period1, $period2, $branch_id);
            $message = 'تم جلب تقرير مقارنة الخدمات بنجاح';
            break;

        case 'expenses':
            $expenseModel = new Expense($db);
            $report = $expenseModel->compareExpensesPeriods($period1, $period2, $branch_id);
            $message = 'تم جلب تقرير مقارنة المصروفات بنجاح';
            break;

        default:
            throw new Exception('نوع المقارنة غير مدعوم');
    }

    return [
        'success' => true,
        'message' => $message,
        'data' => $report
    ];
}

/**
 * استخراج فلاتر التقرير من الطلب
 * @return array فلاتر التقرير
 */
function getReportFilters() {
    $filters = [];

    // فلاتر التاريخ
    if (!empty($_REQUEST['start_date'])) {
        // تحويل التاريخ من العربي إلى الإنجليزي إذا لزم الأمر
        $start_date = $_REQUEST['start_date'];
        // التحقق من وجود أرقام عربية
        if (preg_match('/[٠-٩]/', $start_date)) {
            // تحويل الأرقام العربية إلى الإنجليزية
            $arabic_digits = array('٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩');
            $english_digits = array('0', '1', '2', '3', '4', '5', '6', '7', '8', '9');
            $start_date = str_replace($arabic_digits, $english_digits, $start_date);
        }
        $filters['start_date'] = $start_date;

        // تسجيل التاريخ للتشخيص
        error_log('Start date after conversion: ' . $start_date);
    }

    if (!empty($_REQUEST['end_date'])) {
        // تحويل التاريخ من العربي إلى الإنجليزي إذا لزم الأمر
        $end_date = $_REQUEST['end_date'];
        // التحقق من وجود أرقام عربية
        if (preg_match('/[٠-٩]/', $end_date)) {
            // تحويل الأرقام العربية إلى الإنجليزية
            $arabic_digits = array('٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩');
            $english_digits = array('0', '1', '2', '3', '4', '5', '6', '7', '8', '9');
            $end_date = str_replace($arabic_digits, $english_digits, $end_date);
        }
        $filters['end_date'] = $end_date;

        // تسجيل التاريخ للتشخيص
        error_log('End date after conversion: ' . $end_date);
    }

    if (!empty($_REQUEST['month']) && !empty($_REQUEST['year'])) {
        $filters['month'] = $_REQUEST['month'];
        $filters['year'] = $_REQUEST['year'];
    } elseif (!empty($_REQUEST['year'])) {
        $filters['year'] = $_REQUEST['year'];
    }

    // فلتر الفترة (month, year, all)
    if (!empty($_REQUEST['period'])) {
        $filters['period'] = $_REQUEST['period'];
    }

    // فلتر نطاق التاريخ (today, yesterday, this_week, last_week, this_month, last_month, this_year, last_year, custom)
    if (!empty($_REQUEST['date_range'])) {
        $filters['date_range'] = $_REQUEST['date_range'];

        // تحويل نطاق التاريخ إلى تواريخ فعلية
        $today = date('Y-m-d');
        $yesterday = date('Y-m-d', strtotime('-1 day'));
        $thisWeekStart = date('Y-m-d', strtotime('monday this week'));
        $thisWeekEnd = date('Y-m-d', strtotime('sunday this week'));
        $lastWeekStart = date('Y-m-d', strtotime('monday last week'));
        $lastWeekEnd = date('Y-m-d', strtotime('sunday last week'));
        $thisMonthStart = date('Y-m-01');
        $thisMonthEnd = date('Y-m-t');
        $lastMonthStart = date('Y-m-01', strtotime('first day of last month'));
        $lastMonthEnd = date('Y-m-t', strtotime('last day of last month'));
        $thisYearStart = date('Y-01-01');
        $thisYearEnd = date('Y-12-31');
        $lastYearStart = date('Y-01-01', strtotime('-1 year'));
        $lastYearEnd = date('Y-12-31', strtotime('-1 year'));

        switch ($filters['date_range']) {
            case 'today':
                $filters['start_date'] = $today;
                $filters['end_date'] = $today;
                break;
            case 'yesterday':
                $filters['start_date'] = $yesterday;
                $filters['end_date'] = $yesterday;
                break;
            case 'this_week':
                $filters['start_date'] = $thisWeekStart;
                $filters['end_date'] = $thisWeekEnd;
                break;
            case 'last_week':
                $filters['start_date'] = $lastWeekStart;
                $filters['end_date'] = $lastWeekEnd;
                break;
            case 'this_month':
                $filters['start_date'] = $thisMonthStart;
                $filters['end_date'] = $thisMonthEnd;
                break;
            case 'last_month':
                $filters['start_date'] = $lastMonthStart;
                $filters['end_date'] = $lastMonthEnd;
                break;
            case 'this_year':
                $filters['start_date'] = $thisYearStart;
                $filters['end_date'] = $thisYearEnd;
                break;
            case 'last_year':
                $filters['start_date'] = $lastYearStart;
                $filters['end_date'] = $lastYearEnd;
                break;
            case 'custom':
                // تم معالجة التواريخ المخصصة بالفعل في الأعلى
                break;
        }

        error_log("Date range '{$filters['date_range']}' converted to: {$filters['start_date']} - {$filters['end_date']}");
    }

    // نوع التقرير (summary, detailed)
    if (!empty($_REQUEST['report_type'])) {
        $filters['report_type'] = $_REQUEST['report_type'];
    }

    // فلتر الفرع
    if (!empty($_REQUEST['branch_id'])) {
        $filters['branch_id'] = $_REQUEST['branch_id'];
    } elseif (!isAdmin() && !empty($_SESSION['user_branch_id'])) {
        // استخدام فرع المستخدم الحالي إذا لم يتم تحديد فرع وكان المستخدم ليس مديرًا
        $filters['branch_id'] = $_SESSION['user_branch_id'];
    }

    // فلاتر إضافية حسب نوع التقرير
    if (!empty($_REQUEST['employee_id'])) {
        $filters['employee_id'] = $_REQUEST['employee_id'];
    }

    if (!empty($_REQUEST['service_id'])) {
        $filters['service_id'] = $_REQUEST['service_id'];
    }

    if (!empty($_REQUEST['customer_id'])) {
        $filters['customer_id'] = $_REQUEST['customer_id'];
    }

    if (!empty($_REQUEST['category_id'])) {
        $filters['category_id'] = $_REQUEST['category_id'];
    }

    if (isset($_REQUEST['is_active'])) {
        $filters['is_active'] = $_REQUEST['is_active'];
    }

    if (!empty($_REQUEST['payment_method'])) {
        $filters['payment_method'] = $_REQUEST['payment_method'];
    }

    if (!empty($_REQUEST['payment_status'])) {
        $filters['payment_status'] = $_REQUEST['payment_status'];
    }

    if (!empty($_REQUEST['search'])) {
        $filters['search'] = $_REQUEST['search'];
    }

    // فلاتر للمخزون
    if (!empty($_REQUEST['days'])) {
        $filters['days'] = (int)$_REQUEST['days'];
    }

    if (!empty($_REQUEST['period_days'])) {
        $filters['period_days'] = (int)$_REQUEST['period_days'];
    }

    // فلاتر للمقارنة بين فترتين
    if (!empty($_REQUEST['compare_periods'])) {
        $filters['compare_periods'] = json_decode($_REQUEST['compare_periods'], true);
    }

    // تسجيل الفلاتر النهائية
    error_log('Final report filters: ' . json_encode($filters));

    return $filters;
}

/**
 * معالجة التقرير الشامل
 * @return array استجابة التقرير
 */
function handleComprehensiveReport() {
    global $db;

    // جلب بيانات الفلترة
    $filters = getReportFilters();

    // إنشاء كائنات النماذج
    $endDayModel = new EndDay($db);
    $expenseModel = new Expense($db);
    $invoiceModel = new Invoice($db);
    $employeeModel = new Employee($db);

    // تجهيز هيكل البيانات للتقرير
    $report = [
        'filters' => $filters,
        'summary' => [],
        'daily_sales' => [],
        'payment_methods' => [],
        'expenses' => [],
        'salaries' => []
    ];

    // الحصول على بيانات نهاية اليوم للفترة المحددة
    $endDays = $endDayModel->getEndDays($filters);

    // إنشاء مصفوفة لتخزين جميع الأيام في النطاق المحدد
    $allDays = [];

    // التحقق من وجود تاريخ بداية ونهاية
    if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
        $startDate = new DateTime($filters['start_date']);
        $endDate = new DateTime($filters['end_date']);
        $endDate->modify('+1 day'); // لتضمين تاريخ النهاية

        // إنشاء مصفوفة لتخزين الأيام الموجودة بالفعل
        $existingDays = [];
        foreach ($endDays as $day) {
            $existingDays[$day['date']] = $day;
        }

        // إنشاء مصفوفة لجميع الأيام في النطاق
        $currentDate = clone $startDate;
        while ($currentDate < $endDate) {
            $dateStr = $currentDate->format('Y-m-d');

            // التحقق من وجود اليوم في الأيام الموجودة
            if (isset($existingDays[$dateStr])) {
                $allDays[] = $existingDays[$dateStr];
            } else {
                // إنشاء يوم افتراضي إذا لم يكن موجودًا
                // الحصول على بيانات المبيعات لهذا اليوم باستخدام end_day
                $daySalesQuery = "SELECT
                    SUM(i.final_amount) as total_sales,
                    SUM(CASE WHEN i.payment_method = 'cash' THEN i.final_amount ELSE 0 END) as cash_amount,
                    SUM(CASE WHEN i.payment_method = 'card' THEN i.final_amount ELSE 0 END) as card_amount,
                    SUM(CASE WHEN i.payment_method NOT IN ('cash', 'card') THEN i.final_amount ELSE 0 END) as other_amount
                FROM invoices i
                LEFT JOIN end_days ed ON i.end_day_id = ed.id
                WHERE (
                    (i.end_day_id IS NOT NULL AND ed.date = :date1) OR
                    (i.end_day_id IS NULL AND DATE(i.created_at) = :date2)
                )
                AND i.payment_status = 'paid'";

                if (!empty($filters['branch_id'])) {
                    $daySalesQuery .= " AND i.branch_id = :branch_id";
                }

                $db->prepare($daySalesQuery);
                $db->bind(':date1', $dateStr);
                $db->bind(':date2', $dateStr);

                if (!empty($filters['branch_id'])) {
                    $db->bind(':branch_id', $filters['branch_id']);
                }

                $daySalesResult = $db->fetch();

                // الحصول على بيانات المصروفات لهذا اليوم
                // المصروفات تعتمد على تاريخ المصروف نفسه وليس على end_day
                $dayExpensesQuery = "SELECT SUM(amount) as total_expenses FROM expenses WHERE DATE(date) = :date";

                if (!empty($filters['branch_id'])) {
                    $dayExpensesQuery .= " AND branch_id = :branch_id";
                }

                $db->prepare($dayExpensesQuery);
                $db->bind(':date', $dateStr);

                if (!empty($filters['branch_id'])) {
                    $db->bind(':branch_id', $filters['branch_id']);
                }

                $dayExpensesResult = $db->fetch();

                // إنشاء اليوم الافتراضي مع البيانات الحقيقية
                $allDays[] = [
                    'id' => null,
                    'date' => $dateStr,
                    'branch_id' => $filters['branch_id'] ?? null,
                    'total_sales' => $daySalesResult && $daySalesResult['total_sales'] ? $daySalesResult['total_sales'] : 0,
                    'total_expenses' => $dayExpensesResult && $dayExpensesResult['total_expenses'] ? $dayExpensesResult['total_expenses'] : 0,
                    'cash_amount' => $daySalesResult && $daySalesResult['cash_amount'] ? $daySalesResult['cash_amount'] : 0,
                    'card_amount' => $daySalesResult && $daySalesResult['card_amount'] ? $daySalesResult['card_amount'] : 0,
                    'other_amount' => $daySalesResult && $daySalesResult['other_amount'] ? $daySalesResult['other_amount'] : 0
                ];
            }

            $currentDate->modify('+1 day');
        }

        // استخدام المصفوفة الجديدة بدلاً من الأصلية
        $endDays = $allDays;
    }

    // إجمالي المبيعات والمصروفات
    // سنستخدم المجموع من الموديل بدلاً من حسابه يدوياً
    $totalSales = $invoiceModel->getTotalSales($filters);
    $totalExpenses = $expenseModel->getTotalExpenses($filters);
    $totalProfits = 0;
    $netProfit = 0;

    // الحصول على بيانات الرواتب المدفوعة خلال الفترة
    $totalSalaries = 0;
    $salaryData = [];

    // محاولة الحصول على الرواتب من عدة مصادر
    try {
        // البحث في جدول الرواتب إذا كان موجودًا
        $startDate = $filters['start_date'] ?? date('Y-m-01');
        $endDate = $filters['end_date'] ?? date('Y-m-t');

        error_log("Searching for salaries between $startDate and $endDate");

        // 1. البحث في جدول employee_salaries أولاً
        // استخدام month و year بدلاً من payment_date
        $startMonth = date('n', strtotime($startDate)); // شهر بدون صفر في البداية
        $startYear = date('Y', strtotime($startDate));
        $endMonth = date('n', strtotime($endDate));
        $endYear = date('Y', strtotime($endDate));

        $employeeSalaryQuery = "SELECT es.*, e.name as employee_name, e.position
                              FROM employee_salaries es
                              LEFT JOIN employees e ON es.employee_id = e.id
                              WHERE es.payment_status = 'paid'
                              AND (
                                  (es.year = :start_year AND es.year = :end_year AND es.month >= :start_month AND es.month <= :end_month) OR
                                  (es.year = :start_year AND es.year < :end_year AND es.month >= :start_month) OR
                                  (es.year = :end_year AND es.year > :start_year AND es.month <= :end_month) OR
                                  (es.year > :start_year AND es.year < :end_year)
                              )";

        if (!empty($filters['branch_id'])) {
            $employeeSalaryQuery .= " AND e.branch_id = :branch_id";
        }

        $db->prepare($employeeSalaryQuery);
        $db->bind(':start_year', $startYear);
        $db->bind(':start_month', $startMonth);
        $db->bind(':end_year', $endYear);
        $db->bind(':end_month', $endMonth);

        if (!empty($filters['branch_id'])) {
            $db->bind(':branch_id', $filters['branch_id']);
        }

        $employeeSalaries = $db->fetchAll();
        error_log("Found " . count($employeeSalaries) . " records in employee_salaries table");

        if (!empty($employeeSalaries)) {
            // إذا وجدنا بيانات في جدول رواتب الموظفين
            foreach ($employeeSalaries as $salary) {
                // تحويل البيانات إلى الشكل المطلوب
                $salaryData[] = [
                    'id' => $salary['id'],
                    'employee_id' => $salary['employee_id'],
                    'employee_name' => $salary['employee_name'],
                    'date' => $salary['payment_date'],
                    'amount' => $salary['total_amount'],
                    'notes' => $salary['notes'] ?? '',
                    'source' => 'employee_salaries'
                ];

                $totalSalaries += floatval($salary['total_amount']);
            }
        }

        // 2. البحث في جدول salaries إذا كان موجودًا
        $salaryQuery = "SELECT s.*, e.name as employee_name, u.name as user_name
                      FROM salaries s
                      LEFT JOIN employees e ON s.employee_id = e.id
                      LEFT JOIN users u ON s.user_id = u.id
                      WHERE (s.payment_date BETWEEN :start_date AND :end_date)";

        if (!empty($filters['branch_id'])) {
            $salaryQuery .= " AND (e.branch_id = :branch_id OR s.branch_id = :branch_id)";
        }

        try {
            $db->prepare($salaryQuery);
            $db->bind(':start_date', $startDate);
            $db->bind(':end_date', $endDate);

            if (!empty($filters['branch_id'])) {
                $db->bind(':branch_id', $filters['branch_id']);
            }

            $salariesFromTable = $db->fetchAll();
            error_log("Found " . count($salariesFromTable) . " records in salaries table");

            if (!empty($salariesFromTable)) {
                foreach ($salariesFromTable as $salary) {
                    $salaryData[] = [
                        'id' => $salary['id'],
                        'employee_id' => $salary['employee_id'] ?? null,
                        'employee_name' => $salary['employee_name'] ?? $salary['user_name'] ?? 'غير محدد',
                        'date' => $salary['payment_date'] ?? $salary['date'] ?? '',
                        'amount' => $salary['amount'] ?? $salary['total_amount'] ?? 0,
                        'notes' => $salary['notes'] ?? $salary['description'] ?? '',
                        'source' => 'salaries'
                    ];

                    $totalSalaries += floatval($salary['amount'] ?? $salary['total_amount'] ?? 0);
                }
            }
        } catch (Exception $e) {
            error_log('Error fetching from salaries table: ' . $e->getMessage());
            // لا نفعل شيئًا هنا، نستمر بالبحث في الجداول الأخرى
        }

        // 3. البحث عن مصروفات الرواتب في جدول المصروفات
        $expenseQuery = "SELECT e.*, c.name as category_name, u.name as user_name, b.name as branch_name, emp.name as employee_name
                       FROM expenses e
                       LEFT JOIN expense_categories c ON e.category_id = c.id
                       LEFT JOIN users u ON e.user_id = u.id
                       LEFT JOIN branches b ON e.branch_id = b.id
                       LEFT JOIN employees emp ON e.employee_id = emp.id
                       WHERE (c.name LIKE '%salary%' OR c.name LIKE '%راتب%' OR c.name LIKE '%رواتب%' OR c.name LIKE '%مرتب%' OR e.description LIKE '%salary%' OR e.description LIKE '%راتب%')
                       AND (e.date BETWEEN :start_date AND :end_date)";

        if (!empty($filters['branch_id'])) {
            $expenseQuery .= " AND (e.branch_id = :branch_id)";
        }

        try {
            $db->prepare($expenseQuery);
            $db->bind(':start_date', $startDate);
            $db->bind(':end_date', $endDate);

            if (!empty($filters['branch_id'])) {
                $db->bind(':branch_id', $filters['branch_id']);
            }

            $salariesFromExpenses = $db->fetchAll();
            error_log("Found " . count($salariesFromExpenses) . " salary records in expenses table");

            if (!empty($salariesFromExpenses)) {
                foreach ($salariesFromExpenses as $salary) {
                    $salaryData[] = [
                        'id' => $salary['id'],
                        'employee_id' => $salary['employee_id'] ?? null,
                        'employee_name' => $salary['employee_name'] ?? $salary['user_name'] ?? $salary['description'] ?? 'غير محدد',
                        'date' => $salary['date'] ?? '',
                        'amount' => $salary['amount'] ?? 0,
                        'notes' => $salary['description'] ?? $salary['category_name'] ?? '',
                        'source' => 'expenses'
                    ];

                    $totalSalaries += floatval($salary['amount'] ?? 0);
                }
            }
        } catch (Exception $e) {
            error_log('Error fetching from expenses table: ' . $e->getMessage());
        }

        // 4. البحث في جدول مدفوعات الموظفين إذا كان موجودًا
        $paymentQuery = "SELECT p.*, e.name as employee_name, u.name as user_name
                       FROM employee_payments p
                       LEFT JOIN employees e ON p.employee_id = e.id
                       LEFT JOIN users u ON p.user_id = u.id
                       WHERE (p.payment_date BETWEEN :start_date AND :end_date)";

        if (!empty($filters['branch_id'])) {
            $paymentQuery .= " AND (e.branch_id = :branch_id OR p.branch_id = :branch_id)";
        }

        try {
            $db->prepare($paymentQuery);
            $db->bind(':start_date', $startDate);
            $db->bind(':end_date', $endDate);

            if (!empty($filters['branch_id'])) {
                $db->bind(':branch_id', $filters['branch_id']);
            }

            $salariesFromPayments = $db->fetchAll();
            error_log("Found " . count($salariesFromPayments) . " records in employee_payments table");

            if (!empty($salariesFromPayments)) {
                foreach ($salariesFromPayments as $salary) {
                    $salaryData[] = [
                        'id' => $salary['id'],
                        'employee_id' => $salary['employee_id'] ?? null,
                        'employee_name' => $salary['employee_name'] ?? $salary['user_name'] ?? 'غير محدد',
                        'date' => $salary['payment_date'] ?? $salary['date'] ?? '',
                        'amount' => $salary['amount'] ?? $salary['payment_amount'] ?? 0,
                        'notes' => $salary['notes'] ?? $salary['description'] ?? '',
                        'source' => 'employee_payments'
                    ];

                    $totalSalaries += floatval($salary['amount'] ?? $salary['payment_amount'] ?? 0);
                }
            }
        } catch (Exception $e) {
            error_log('Error fetching from employee_payments table: ' . $e->getMessage());
        }

        // تسجيل عدد الرواتب التي تم العثور عليها
        error_log('Total found: ' . count($salaryData) . ' salary records for period ' . $startDate . ' to ' . $endDate);
        error_log('Total salaries amount: ' . $totalSalaries);

    } catch (Exception $e) {
        error_log('Error fetching salary data: ' . $e->getMessage());
        // لا نفعل شيئًا هنا، سنستمر بقيمة صفر للرواتب
    }

    // تجهيز بيانات المبيعات اليومية
    // حساب متوسط الرواتب اليومية إذا كانت هناك رواتب
    $dailySalaries = 0;
    $daysCount = count($endDays);
    if ($daysCount > 0 && $totalSalaries > 0) {
        $dailySalaries = $totalSalaries / $daysCount;
    }

    foreach ($endDays as $day) {
        $daySales = floatval($day['total_sales']);
        $dayExpenses = floatval($day['total_expenses']);
        $dayProfit = $daySales - $dayExpenses;

        // حساب صافي الربح بعد خصم الرواتب (يمكن أن يكون سالبًا)
        $dayNetProfit = $dayProfit - $dailySalaries;

        // حساب نسبة الربح/الخسارة لليوم
        $dayProfitPercentage = 0;
        if ($daySales > 0) {
            $dayProfitPercentage = ($dayNetProfit / $daySales) * 100;
        }

        // Ya no acumulamos los totales aquí, ya que los obtenemos directamente de los modelos

        // إضافة بيانات اليوم إلى التقرير
        $report['daily_sales'][] = [
            'id' => $day['id'], // إضافة معرف اليوم لاستخدامه في رابط التفاصيل
            'date' => $day['date'],
            'total_sales' => $daySales,
            'total_expenses' => $dayExpenses,
            'daily_salaries' => $dailySalaries,  // إضافة متوسط الرواتب اليومية
            'profit' => $dayProfit,
            'net_profit' => $dayNetProfit,
            'profit_percentage' => round($dayProfitPercentage, 2),
            'cash_amount' => floatval($day['cash_amount']),
            'card_amount' => floatval($day['card_amount']),
            'other_amount' => floatval($day['other_amount'])
        ];
    }

    // الحصول على بيانات الرواتب المدفوعة خلال الفترة
    $totalSalaries = 0;
    $salaryData = [];

    // محاولة الحصول على الرواتب من عدة مصادر
    try {
        // البحث في جدول الرواتب إذا كان موجودًا
        $startDate = $filters['start_date'] ?? date('Y-m-01');
        $endDate = $filters['end_date'] ?? date('Y-m-t');

        error_log("Searching for salaries between $startDate and $endDate");

        // 1. البحث في جدول employee_salaries أولاً
        // استخدام month و year بدلاً من payment_date
        $startMonth = date('n', strtotime($startDate)); // شهر بدون صفر في البداية
        $startYear = date('Y', strtotime($startDate));
        $endMonth = date('n', strtotime($endDate));
        $endYear = date('Y', strtotime($endDate));

        $employeeSalaryQuery = "SELECT es.*, e.name as employee_name, e.position
                              FROM employee_salaries es
                              LEFT JOIN employees e ON es.employee_id = e.id
                              WHERE es.payment_status = 'paid'
                              AND (
                                  (es.year = :start_year AND es.year = :end_year AND es.month >= :start_month AND es.month <= :end_month) OR
                                  (es.year = :start_year AND es.year < :end_year AND es.month >= :start_month) OR
                                  (es.year = :end_year AND es.year > :start_year AND es.month <= :end_month) OR
                                  (es.year > :start_year AND es.year < :end_year)
                              )";

        if (!empty($filters['branch_id'])) {
            $employeeSalaryQuery .= " AND e.branch_id = :branch_id";
        }

        $db->prepare($employeeSalaryQuery);
        $db->bind(':start_year', $startYear);
        $db->bind(':start_month', $startMonth);
        $db->bind(':end_year', $endYear);
        $db->bind(':end_month', $endMonth);

        if (!empty($filters['branch_id'])) {
            $db->bind(':branch_id', $filters['branch_id']);
        }

        $employeeSalaries = $db->fetchAll();
        error_log("Found " . count($employeeSalaries) . " records in employee_salaries table");

        if (!empty($employeeSalaries)) {
            // إذا وجدنا بيانات في جدول رواتب الموظفين
            foreach ($employeeSalaries as $salary) {
                // تحويل البيانات إلى الشكل المطلوب
                $salaryData[] = [
                    'id' => $salary['id'],
                    'employee_id' => $salary['employee_id'],
                    'employee_name' => $salary['employee_name'],
                    'date' => $salary['payment_date'],
                    'amount' => $salary['total_amount'],
                    'notes' => $salary['notes'] ?? '',
                    'source' => 'employee_salaries'
                ];

                $totalSalaries += floatval($salary['total_amount']);
            }
        }

        // 2. البحث في جدول salaries إذا كان موجودًا
        $salaryQuery = "SELECT s.*, e.name as employee_name, u.name as user_name
                      FROM salaries s
                      LEFT JOIN employees e ON s.employee_id = e.id
                      LEFT JOIN users u ON s.user_id = u.id
                      WHERE (s.payment_date BETWEEN :start_date AND :end_date)";

        if (!empty($filters['branch_id'])) {
            $salaryQuery .= " AND (e.branch_id = :branch_id OR s.branch_id = :branch_id)";
        }

        try {
            $db->prepare($salaryQuery);
            $db->bind(':start_date', $startDate);
            $db->bind(':end_date', $endDate);

            if (!empty($filters['branch_id'])) {
                $db->bind(':branch_id', $filters['branch_id']);
            }

            $salariesFromTable = $db->fetchAll();
            error_log("Found " . count($salariesFromTable) . " records in salaries table");

            if (!empty($salariesFromTable)) {
                foreach ($salariesFromTable as $salary) {
                    $salaryData[] = [
                        'id' => $salary['id'],
                        'employee_id' => $salary['employee_id'] ?? null,
                        'employee_name' => $salary['employee_name'] ?? $salary['user_name'] ?? 'غير محدد',
                        'date' => $salary['payment_date'] ?? $salary['date'] ?? '',
                        'amount' => $salary['amount'] ?? $salary['total_amount'] ?? 0,
                        'notes' => $salary['notes'] ?? $salary['description'] ?? '',
                        'source' => 'salaries'
                    ];

                    $totalSalaries += floatval($salary['amount'] ?? $salary['total_amount'] ?? 0);
                }
            }
        } catch (Exception $e) {
            error_log('Error fetching from salaries table: ' . $e->getMessage());
            // لا نفعل شيئًا هنا، نستمر بالبحث في الجداول الأخرى
        }

        // 3. البحث عن مصروفات الرواتب في جدول المصروفات
        $expenseQuery = "SELECT e.*, c.name as category_name, u.name as user_name, b.name as branch_name, emp.name as employee_name
                       FROM expenses e
                       LEFT JOIN expense_categories c ON e.category_id = c.id
                       LEFT JOIN users u ON e.user_id = u.id
                       LEFT JOIN branches b ON e.branch_id = b.id
                       LEFT JOIN employees emp ON e.employee_id = emp.id
                       WHERE (c.name LIKE '%salary%' OR c.name LIKE '%راتب%' OR c.name LIKE '%رواتب%' OR c.name LIKE '%مرتب%' OR e.description LIKE '%salary%' OR e.description LIKE '%راتب%')
                       AND (e.date BETWEEN :start_date AND :end_date)";

        if (!empty($filters['branch_id'])) {
            $expenseQuery .= " AND (e.branch_id = :branch_id)";
        }

        try {
            $db->prepare($expenseQuery);
            $db->bind(':start_date', $startDate);
            $db->bind(':end_date', $endDate);

            if (!empty($filters['branch_id'])) {
                $db->bind(':branch_id', $filters['branch_id']);
            }

            $salariesFromExpenses = $db->fetchAll();
            error_log("Found " . count($salariesFromExpenses) . " salary records in expenses table");

            if (!empty($salariesFromExpenses)) {
                foreach ($salariesFromExpenses as $salary) {
                    $salaryData[] = [
                        'id' => $salary['id'],
                        'employee_id' => $salary['employee_id'] ?? null,
                        'employee_name' => $salary['employee_name'] ?? $salary['user_name'] ?? $salary['description'] ?? 'غير محدد',
                        'date' => $salary['date'] ?? '',
                        'amount' => $salary['amount'] ?? 0,
                        'notes' => $salary['description'] ?? $salary['category_name'] ?? '',
                        'source' => 'expenses'
                    ];

                    $totalSalaries += floatval($salary['amount'] ?? 0);
                }
            }
        } catch (Exception $e) {
            error_log('Error fetching from expenses table: ' . $e->getMessage());
        }

        // 4. البحث في جدول مدفوعات الموظفين إذا كان موجودًا
        $paymentQuery = "SELECT p.*, e.name as employee_name, u.name as user_name
                       FROM employee_payments p
                       LEFT JOIN employees e ON p.employee_id = e.id
                       LEFT JOIN users u ON p.user_id = u.id
                       WHERE (p.payment_date BETWEEN :start_date AND :end_date)";

        if (!empty($filters['branch_id'])) {
            $paymentQuery .= " AND (e.branch_id = :branch_id OR p.branch_id = :branch_id)";
        }

        try {
            $db->prepare($paymentQuery);
            $db->bind(':start_date', $startDate);
            $db->bind(':end_date', $endDate);

            if (!empty($filters['branch_id'])) {
                $db->bind(':branch_id', $filters['branch_id']);
            }

            $salariesFromPayments = $db->fetchAll();
            error_log("Found " . count($salariesFromPayments) . " records in employee_payments table");

            if (!empty($salariesFromPayments)) {
                foreach ($salariesFromPayments as $salary) {
                    $salaryData[] = [
                        'id' => $salary['id'],
                        'employee_id' => $salary['employee_id'] ?? null,
                        'employee_name' => $salary['employee_name'] ?? $salary['user_name'] ?? 'غير محدد',
                        'date' => $salary['payment_date'] ?? $salary['date'] ?? '',
                        'amount' => $salary['amount'] ?? $salary['payment_amount'] ?? 0,
                        'notes' => $salary['notes'] ?? $salary['description'] ?? '',
                        'source' => 'employee_payments'
                    ];

                    $totalSalaries += floatval($salary['amount'] ?? $salary['payment_amount'] ?? 0);
                }
            }
        } catch (Exception $e) {
            error_log('Error fetching from employee_payments table: ' . $e->getMessage());
        }

        // تسجيل عدد الرواتب التي تم العثور عليها
        error_log('Total found: ' . count($salaryData) . ' salary records for period ' . $startDate . ' to ' . $endDate);
        error_log('Total salaries amount: ' . $totalSalaries);

    } catch (Exception $e) {
        error_log('Error fetching salary data: ' . $e->getMessage());
        // لا نفعل شيئًا هنا، سنستمر بقيمة صفر للرواتب
    }

    // إضافة بيانات الرواتب إلى التقرير
    $report['salaries'] = $salaryData;

    // حساب إجمالي الأرباح وصافي الربح بعد خصم الرواتب
    $totalProfits = $totalSales - $totalExpenses;
    $netProfit = $totalProfits - $totalSalaries; // السماح بالقيم السالبة (الخسائر)

    // حساب نسبة الربح أو الخسارة
    $profitPercentage = 0;
    if ($totalSales > 0) {
        $profitPercentage = ($netProfit / $totalSales) * 100;
    }

    // تحديد نوع النتيجة (ربح أو خسارة)
    $resultType = $netProfit >= 0 ? 'profit' : 'loss';

    // حساب إجمالي الفواتير المدفوعة جزئياً
    $totalPartialPaidInvoices = $invoiceModel->getTotalPartialPaidInvoices($filters);

    // حساب إجمالي الخصومات
    $totalDiscounts = 0;
    try {
        $discountQuery = "SELECT SUM(discount_amount) as total_discounts FROM invoices WHERE payment_status = 'paid'";

        if (!empty($filters['start_date'])) {
            $discountQuery .= " AND DATE(created_at) >= :start_date";
        }

        if (!empty($filters['end_date'])) {
            $discountQuery .= " AND DATE(created_at) <= :end_date";
        }

        if (!empty($filters['branch_id'])) {
            $discountQuery .= " AND branch_id = :branch_id";
        }

        $db->prepare($discountQuery);

        if (!empty($filters['start_date'])) {
            $db->bind(':start_date', $filters['start_date']);
        }

        if (!empty($filters['end_date'])) {
            $db->bind(':end_date', $filters['end_date']);
        }

        if (!empty($filters['branch_id'])) {
            $db->bind(':branch_id', $filters['branch_id']);
        }

        $discountResult = $db->fetch();
        $totalDiscounts = $discountResult && $discountResult['total_discounts'] ? $discountResult['total_discounts'] : 0;
        error_log('Total discounts: ' . $totalDiscounts);
    } catch (Exception $e) {
        error_log('Error calculating total discounts: ' . $e->getMessage());
    }

    // إضافة الملخص إلى التقرير
    $report['summary'] = [
        'total_sales' => $totalSales,
        'total_expenses' => $totalExpenses,
        'total_salaries' => $totalSalaries,
        'total_profits' => $totalProfits,
        'net_profit' => $netProfit,
        'profit_percentage' => round($profitPercentage, 2),
        'result_type' => $resultType,
        'total_partial_paid_invoices' => $totalPartialPaidInvoices,
        'total_discounts' => $totalDiscounts
    ];

    // الحصول على بيانات طرق الدفع
    $paymentMethods = ['cash', 'card', 'other'];
    $paymentMethodTotals = [];

    foreach ($paymentMethods as $method) {
        $methodFilters = $filters;
        $methodFilters['payment_method'] = $method;
        $total = $invoiceModel->getTotalSales($methodFilters);

        $paymentMethodTotals[] = [
            'method' => $method,
            'total' => $total
        ];
    }

    $report['payment_methods'] = $paymentMethodTotals;

    // الحصول على بيانات المصروفات حسب الفئة
    $expenseCategories = $expenseModel->getExpensesByCategory($filters);
    $report['expenses'] = $expenseCategories;

    return [
        'success' => true,
        'message' => 'تم جلب التقرير الشامل بنجاح',
        'data' => $report
    ];
}

// ملاحظة: تم نقل هذا الكود إلى بداية الملف