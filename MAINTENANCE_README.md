# نظام الصيانة - دليل الاستخدام

## نظرة عامة
تم إنشاء نظام صيانة شامل للموقع يسمح للمدراء بتفعيل وضع الصيانة عند الحاجة لإجراء تحديثات أو إصلاحات على النظام.

## الملفات المنشأة

### 1. صفحة الصيانة الرئيسية
- **الملف**: `maintenance.php`
- **الوصف**: صفحة جميلة ومهنية تظهر للمستخدمين عندما يكون النظام في وضع الصيانة
- **الميزات**:
  - تصميم متجاوب وجذاب
  - شريط تقدم قابل للتخصيص
  - تحديث تلقائي كل 30 ثانية
  - تأثيرات بصرية متحركة
  - معلومات التواصل والوقت المتوقع

### 2. لوحة التحكم في الصيانة
- **الملف**: `admin/maintenance_control.php`
- **الوصف**: لوحة تحكم للمدراء لإدارة وضع الصيانة
- **الميزات**:
  - تفعيل/إلغاء وضع الصيانة بنقرة واحدة
  - تعديل محتوى صفحة الصيانة
  - معاينة صفحة الصيانة
  - تحديث شريط التقدم والرسائل

### 3. ملف التحقق من الصيانة
- **الملف**: `includes/maintenance_check.php`
- **الوصف**: يتم استدعاؤه تلقائياً في كل صفحة للتحقق من وضع الصيانة
- **الميزات**:
  - فحص تلقائي لوضع الصيانة
  - استثناءات للمدراء والصفحات المهمة
  - إعادة توجيه تلقائية لصفحة الصيانة

### 4. ملف الإعدادات
- **الملف**: `config/maintenance_config.json`
- **الوصف**: ملف JSON يحتوي على إعدادات صفحة الصيانة
- **المحتوى**:
  - عنوان الصفحة والرسائل
  - نسبة التقدم والوقت المتوقع
  - معلومات التواصل
  - إعدادات التحديث التلقائي

## كيفية الاستخدام

### تفعيل وضع الصيانة
1. سجل دخول كمدير
2. اذهب إلى لوحة التحكم
3. انقر على "إدارة الصيانة" في قسم أدوات الإدارة
4. انقر على زر "تفعيل الصيانة"
5. سيتم إنشاء ملف `config/maintenance_mode.txt` تلقائياً

### إلغاء وضع الصيانة
1. اذهب إلى لوحة التحكم في الصيانة
2. انقر على زر "إلغاء الصيانة"
3. سيتم حذف ملف `config/maintenance_mode.txt` تلقائياً

### تخصيص صفحة الصيانة
1. في لوحة التحكم في الصيانة
2. عدل الحقول التالية:
   - عنوان الصفحة
   - الرسالة الرئيسية
   - الوصف التفصيلي
   - الوقت المتوقع
   - معلومات التواصل
   - نسبة التقدم (0-100%)
3. انقر على "حفظ الإعدادات"

## الاستثناءات

### المستخدمون المستثنون
- **المدراء**: يمكنهم الوصول للنظام حتى أثناء الصيانة
- **الصفحات المستثناة**:
  - `maintenance.php` (صفحة الصيانة نفسها)
  - `maintenance_control.php` (لوحة التحكم)
  - `login.php` (صفحة تسجيل الدخول)
  - `logout.php` (صفحة تسجيل الخروج)

### طلبات AJAX
- طلبات AJAX مستثناة من إعادة التوجيه لتجنب مشاكل في التطبيقات التفاعلية

## الميزات المتقدمة

### التحديث التلقائي
- صفحة الصيانة تتحدث تلقائياً كل 30 ثانية
- يمكن تعديل فترة التحديث من ملف الإعدادات

### شريط التقدم
- يمكن إظهار/إخفاء شريط التقدم
- نسبة التقدم قابلة للتخصيص (0-100%)
- تأثيرات بصرية متحركة

### التأثيرات البصرية
- جسيمات متحركة في الخلفية
- تدرجات لونية جذابة
- أيقونات متحركة
- تصميم متجاوب لجميع الأجهزة

## الأمان

### الحماية من الوصول المباشر
- جميع الملفات محمية من الوصول المباشر
- التحقق من الصلاحيات قبل السماح بالوصول
- استخدام ثوابت الحماية

### التحقق من الصلاحيات
- فقط المدراء يمكنهم الوصول للوحة التحكم
- التحقق من دور المستخدم في كل طلب
- حماية من التلاعب في الجلسات

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. صفحة الصيانة لا تظهر
- تأكد من وجود ملف `config/maintenance_mode.txt`
- تحقق من أن `includes/maintenance_check.php` مستدعى في `config/init.php`
- تأكد من صلاحيات الملفات

#### 2. لا يمكن الوصول للوحة التحكم
- تأكد من تسجيل الدخول كمدير
- تحقق من صلاحيات المجلد `admin/`
- تأكد من وجود ملف `admin/maintenance_control.php`

#### 3. الإعدادات لا تُحفظ
- تحقق من صلاحيات الكتابة في مجلد `config/`
- تأكد من صحة تنسيق JSON في ملف الإعدادات
- راجع سجلات الأخطاء

## الملفات والمجلدات

```
├── maintenance.php                     # صفحة الصيانة الرئيسية
├── admin/
│   └── maintenance_control.php         # لوحة التحكم في الصيانة
├── config/
│   ├── maintenance_mode.txt            # ملف تفعيل الصيانة (ينشأ تلقائياً)
│   └── maintenance_config.json         # إعدادات صفحة الصيانة
├── includes/
│   └── maintenance_check.php           # ملف التحقق من الصيانة
└── pages/
    └── dashboard.php                   # تم إضافة رابط إدارة الصيانة
```

## التحديثات المستقبلية

### ميزات مقترحة
- جدولة الصيانة التلقائية
- إشعارات للمستخدمين قبل الصيانة
- تقارير عن فترات الصيانة
- دعم لغات متعددة
- تكامل مع أنظمة المراقبة

### تحسينات الأداء
- تخزين مؤقت للإعدادات
- ضغط الملفات الثابتة
- تحسين استعلامات قاعدة البيانات
- دعم CDN للموارد الخارجية

---

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع سجلات النظام في مجلد `logs/`
- تحقق من إعدادات قاعدة البيانات
- تأكد من صلاحيات الملفات والمجلدات
- راجع هذا الدليل للحلول الشائعة

تم إنشاء هذا النظام بواسطة Augment Agent 🤖
