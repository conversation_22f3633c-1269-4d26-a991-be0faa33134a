<?php
/**
 * صفحة النظام تحت الصيانة - نسخة الإنتاج
 * يتم عرضها عندما يكون النظام في وضع الصيانة
 */

// إعدادات الصيانة الافتراضية
$maintenanceConfig = [
    'title' => 'النظام تحت الصيانة',
    'message' => 'نعتذر، النظام غير متاح حالياً بسبب أعمال الصيانة والتطوير.',
    'description' => 'نحن نعمل على تحسين النظام وإضافة ميزات جديدة لتوفير تجربة أفضل لك.',
    'estimated_time' => 'الوقت المتوقع للانتهاء: 30 دقيقة',
    'contact_info' => 'للاستفسارات الطارئة، يرجى التواصل معنا على: <EMAIL>',
    'show_progress' => true,
    'progress_percentage' => 75,
    'last_updated' => date('Y-m-d H:i:s')
];

// التحقق من وجود ملف إعدادات الصيانة
$configFile = 'config/maintenance_config.json';
if (file_exists($configFile)) {
    $fileConfig = json_decode(file_get_contents($configFile), true);
    if ($fileConfig) {
        $maintenanceConfig = array_merge($maintenanceConfig, $fileConfig);
    }
}
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $maintenanceConfig['title']; ?></title>
    
    <!-- Bootstrap RTL via CDN -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- Google Font Tajawal -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap">
    
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }

        .maintenance-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            text-align: center;
            max-width: 600px;
            width: 100%;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .maintenance-icon {
            font-size: 80px;
            color: #667eea;
            margin-bottom: 30px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .maintenance-title {
            color: #333;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
        }

        .maintenance-message {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .maintenance-description {
            color: #888;
            font-size: 1rem;
            margin-bottom: 30px;
            line-height: 1.5;
        }

        .progress-container {
            margin: 30px 0;
        }

        .progress {
            height: 10px;
            border-radius: 10px;
            background-color: #e9ecef;
            overflow: hidden;
        }

        .progress-bar {
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 10px;
            transition: width 0.3s ease;
            animation: progressAnimation 2s ease-in-out infinite alternate;
        }

        @keyframes progressAnimation {
            0% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .estimated-time {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            color: #495057;
            font-weight: 500;
        }

        .contact-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            color: #1976d2;
            font-size: 0.9rem;
        }

        .refresh-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            color: white;
            padding: 12px 30px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .refresh-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .last-updated {
            color: #999;
            font-size: 0.8rem;
            margin-top: 30px;
            font-style: italic;
        }

        .features-list {
            text-align: right;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .features-list h5 {
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }

        .features-list ul {
            list-style: none;
            padding: 0;
        }

        .features-list li {
            padding: 5px 0;
            color: #666;
        }

        .features-list li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-left: 10px;
        }

        .social-links {
            margin-top: 30px;
        }

        .social-links a {
            color: #667eea;
            font-size: 1.5rem;
            margin: 0 10px;
            transition: color 0.3s ease;
        }

        .social-links a:hover {
            color: #764ba2;
        }

        @media (max-width: 768px) {
            .maintenance-container {
                padding: 30px 20px;
                margin: 10px;
            }
            
            .maintenance-title {
                font-size: 2rem;
            }
            
            .maintenance-icon {
                font-size: 60px;
            }
        }

        /* تأثير الجسيمات في الخلفية */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <!-- جسيمات الخلفية -->
    <div class="particles">
        <div class="particle" style="left: 10%; width: 10px; height: 10px; animation-delay: 0s;"></div>
        <div class="particle" style="left: 20%; width: 15px; height: 15px; animation-delay: 1s;"></div>
        <div class="particle" style="left: 30%; width: 8px; height: 8px; animation-delay: 2s;"></div>
        <div class="particle" style="left: 40%; width: 12px; height: 12px; animation-delay: 3s;"></div>
        <div class="particle" style="left: 50%; width: 6px; height: 6px; animation-delay: 4s;"></div>
        <div class="particle" style="left: 60%; width: 14px; height: 14px; animation-delay: 5s;"></div>
        <div class="particle" style="left: 70%; width: 9px; height: 9px; animation-delay: 6s;"></div>
        <div class="particle" style="left: 80%; width: 11px; height: 11px; animation-delay: 7s;"></div>
        <div class="particle" style="left: 90%; width: 7px; height: 7px; animation-delay: 8s;"></div>
    </div>

    <div class="maintenance-container">
        <!-- أيقونة الصيانة -->
        <div class="maintenance-icon">
            <i class="fas fa-tools"></i>
        </div>

        <!-- العنوان -->
        <h1 class="maintenance-title"><?php echo $maintenanceConfig['title']; ?></h1>

        <!-- الرسالة الرئيسية -->
        <p class="maintenance-message"><?php echo $maintenanceConfig['message']; ?></p>

        <!-- الوصف -->
        <p class="maintenance-description"><?php echo $maintenanceConfig['description']; ?></p>

        <!-- شريط التقدم -->
        <?php if ($maintenanceConfig['show_progress']): ?>
        <div class="progress-container">
            <div class="progress">
                <div class="progress-bar" style="width: <?php echo $maintenanceConfig['progress_percentage']; ?>%"></div>
            </div>
            <small class="text-muted mt-2 d-block"><?php echo $maintenanceConfig['progress_percentage']; ?>% مكتمل</small>
        </div>
        <?php endif; ?>

        <!-- الوقت المتوقع -->
        <div class="estimated-time">
            <i class="fas fa-clock me-2"></i>
            <?php echo $maintenanceConfig['estimated_time']; ?>
        </div>

        <!-- قائمة الميزات الجديدة -->
        <div class="features-list">
            <h5><i class="fas fa-star me-2"></i>الميزات الجديدة التي نعمل عليها</h5>
            <ul>
                <li>تحسين أداء النظام والسرعة</li>
                <li>إضافة ميزات جديدة لإدارة الصالون</li>
                <li>تطوير واجهة المستخدم</li>
                <li>تحسين الأمان والحماية</li>
                <li>إضافة تقارير متقدمة</li>
            </ul>
        </div>

        <!-- معلومات التواصل -->
        <div class="contact-info">
            <i class="fas fa-envelope me-2"></i>
            <?php echo $maintenanceConfig['contact_info']; ?>
        </div>

        <!-- زر التحديث -->
        <button class="refresh-btn" onclick="location.reload()">
            <i class="fas fa-sync-alt me-2"></i>
            تحديث الصفحة
        </button>

        <!-- روابط التواصل الاجتماعي -->
        <div class="social-links">
            <a href="#" title="فيسبوك"><i class="fab fa-facebook"></i></a>
            <a href="#" title="تويتر"><i class="fab fa-twitter"></i></a>
            <a href="#" title="واتساب"><i class="fab fa-whatsapp"></i></a>
            <a href="#" title="إنستغرام"><i class="fab fa-instagram"></i></a>
        </div>

        <!-- آخر تحديث -->
        <div class="last-updated">
            آخر تحديث: <?php echo date('d/m/Y H:i', strtotime($maintenanceConfig['last_updated'])); ?>
        </div>
    </div>

    <!-- jQuery via CDN -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Bootstrap Bundle JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تحديث تلقائي للصفحة كل 30 ثانية
        setTimeout(function() {
            location.reload();
        }, 30000);

        // تأثير الجسيمات المتحركة
        function createParticle() {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.top = '100%';
            particle.style.width = (Math.random() * 10 + 5) + 'px';
            particle.style.height = particle.style.width;
            particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
            
            document.querySelector('.particles').appendChild(particle);
            
            // إزالة الجسيم بعد انتهاء الحركة
            setTimeout(() => {
                particle.remove();
            }, 6000);
        }

        // إنشاء جسيم جديد كل ثانيتين
        setInterval(createParticle, 2000);
    </script>
</body>
</html>
