<?php
/**
 * صفحة اختبار إصلاح التقارير
 * تختبر حساب المبيعات باستخدام تاريخ end_day بدلاً من created_at
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Location: ../../pages/auth/login.php');
    exit;
}

// التحقق من الصلاحيات
if (!hasPermission('reports_view')) {
    header('Location: ../../pages/dashboard.php?error=no_permission');
    exit;
}

// إنشاء نموذج الفواتير
$invoiceModel = new Invoice($db);

$pageTitle = 'اختبار إصلاح التقارير';
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SYSTEM_NAME; ?> - <?php echo $pageTitle; ?></title>
    
    <!-- Bootstrap RTL via CDN -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- Google Font Tajawal -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap">
    
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f8f9fa;
        }
        .test-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            padding: 20px;
        }
        .test-result {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .test-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .test-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .test-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .test-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        table {
            font-size: 14px;
        }
        .highlight {
            background-color: #fff3cd;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="fas fa-bug"></i> <?php echo $pageTitle; ?></h1>
                    <a href="sales.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i> العودة للتقارير
                    </a>
                </div>

                <!-- اختبار 1: حساب إجمالي المبيعات لشهر يونيو 2025 -->
                <div class="test-card">
                    <h3><i class="fas fa-calendar-alt"></i> اختبار 1: إجمالي المبيعات لشهر يونيو 2025</h3>

                    <?php
                    $filters = [
                        'start_date' => '2025-06-01',
                        'end_date' => '2025-06-30',
                        'branch_id' => $_SESSION['user_branch_id']
                    ];

                    try {
                        echo "<div class='test-result test-info'>";
                        echo "<strong>الفلاتر المستخدمة:</strong> " . json_encode($filters, JSON_UNESCAPED_UNICODE);
                        echo "</div>";

                        // اختبار مباشر للاستعلام
                        echo "<div class='test-result test-info'>";
                        echo "<strong>اختبار مباشر للاستعلام:</strong><br>";

                        // أولاً: اختبار الطريقة القديمة
                        $oldSql = "SELECT SUM(final_amount) FROM invoices
                                  WHERE payment_status = 'paid'
                                  AND branch_id = :branch_id
                                  AND DATE(created_at) BETWEEN :start_date AND :end_date";

                        $db->prepare($oldSql);
                        $db->bind(':branch_id', $filters['branch_id']);
                        $db->bind(':start_date', $filters['start_date']);
                        $db->bind(':end_date', $filters['end_date']);
                        $oldTotal = $db->fetchColumn();

                        echo "الطريقة القديمة (created_at): " . number_format($oldTotal, 2) . " جنيه<br>";

                        // ثانياً: اختبار الطريقة الجديدة
                        $newSql = "SELECT SUM(i.final_amount) FROM invoices i
                                  LEFT JOIN end_days ed ON i.end_day_id = ed.id
                                  WHERE i.payment_status = 'paid'
                                  AND i.branch_id = :branch_id
                                  AND (
                                      (i.end_day_id IS NOT NULL AND ed.date BETWEEN :start_date1 AND :end_date1) OR
                                      (i.end_day_id IS NULL AND DATE(i.created_at) BETWEEN :start_date2 AND :end_date2)
                                  )";

                        $db->prepare($newSql);
                        $db->bind(':branch_id', $filters['branch_id']);
                        $db->bind(':start_date1', $filters['start_date']);
                        $db->bind(':end_date1', $filters['end_date']);
                        $db->bind(':start_date2', $filters['start_date']);
                        $db->bind(':end_date2', $filters['end_date']);
                        $newTotal = $db->fetchColumn();

                        echo "الطريقة الجديدة (end_day): " . number_format($newTotal, 2) . " جنيه<br>";
                        echo "الفرق: " . number_format($newTotal - $oldTotal, 2) . " جنيه";
                        echo "</div>";

                        $totalSales = $invoiceModel->getTotalSales($filters);
                        echo "<div class='test-result test-success'>";
                        echo "<strong>إجمالي المبيعات لشهر يونيو 2025:</strong> " . number_format($totalSales, 2) . " جنيه";
                        echo "</div>";
                    } catch (Exception $e) {
                        echo "<div class='test-result test-error'>";
                        echo "<strong>خطأ:</strong> " . $e->getMessage();
                        echo "<br><strong>تفاصيل الخطأ:</strong> " . $e->getTraceAsString();
                        echo "</div>";
                    }
                    ?>
                </div>

                <!-- اختبار 2: حساب إجمالي المبيعات لشهر مايو 2025 -->
                <div class="test-card">
                    <h3><i class="fas fa-calendar-alt"></i> اختبار 2: إجمالي المبيعات لشهر مايو 2025</h3>
                    
                    <?php
                    $filters = [
                        'start_date' => '2025-05-01',
                        'end_date' => '2025-05-31',
                        'branch_id' => $_SESSION['user_branch_id']
                    ];

                    try {
                        $totalSales = $invoiceModel->getTotalSales($filters);
                        echo "<div class='test-result test-success'>";
                        echo "<strong>إجمالي المبيعات لشهر مايو 2025:</strong> " . number_format($totalSales, 2) . " جنيه";
                        echo "</div>";
                    } catch (Exception $e) {
                        echo "<div class='test-result test-error'>";
                        echo "<strong>خطأ:</strong> " . $e->getMessage();
                        echo "</div>";
                    }
                    ?>
                </div>

                <!-- اختبار 3: فحص الفواتير المرتبطة بيوم 31/5/2025 -->
                <div class="test-card">
                    <h3><i class="fas fa-search"></i> اختبار 3: فحص الفواتير المرتبطة بيوم 31/5/2025</h3>
                    
                    <?php
                    try {
                        // البحث عن end_day_id ليوم 31/5/2025
                        $db->prepare("SELECT id, date, closed_at FROM end_days WHERE date = '2025-05-31' AND branch_id = :branch_id");
                        $db->bind(':branch_id', $_SESSION['user_branch_id']);
                        $endDay = $db->fetch();
                        
                        if ($endDay) {
                            echo "<div class='test-result test-info'>";
                            echo "<strong>تم العثور على يوم العمل:</strong><br>";
                            echo "ID = {$endDay['id']}<br>";
                            echo "التاريخ = {$endDay['date']}<br>";
                            echo "تم الإقفال في = " . ($endDay['closed_at'] ? $endDay['closed_at'] : 'لم يتم الإقفال بعد');
                            echo "</div>";
                            
                            // البحث عن الفواتير المرتبطة بهذا اليوم
                            $db->prepare("SELECT COUNT(*) as count, SUM(final_amount) as total FROM invoices WHERE end_day_id = :end_day_id AND payment_status = 'paid'");
                            $db->bind(':end_day_id', $endDay['id']);
                            $invoiceStats = $db->fetch();
                            
                            echo "<div class='test-result test-success'>";
                            echo "<strong>عدد الفواتير المرتبطة بهذا اليوم:</strong> {$invoiceStats['count']}<br>";
                            echo "<strong>إجمالي قيمة الفواتير:</strong> " . number_format($invoiceStats['total'], 2) . " جنيه";
                            echo "</div>";
                            
                            // فحص تواريخ إنشاء هذه الفواتير
                            $db->prepare("SELECT DATE(created_at) as creation_date, COUNT(*) as count, SUM(final_amount) as total 
                                         FROM invoices 
                                         WHERE end_day_id = :end_day_id AND payment_status = 'paid' 
                                         GROUP BY DATE(created_at) 
                                         ORDER BY creation_date");
                            $db->bind(':end_day_id', $endDay['id']);
                            $creationDates = $db->fetchAll();
                            
                            if (!empty($creationDates)) {
                                echo "<h5>توزيع الفواتير حسب تاريخ الإنشاء:</h5>";
                                echo "<table class='table table-striped table-sm'>";
                                echo "<thead><tr><th>تاريخ الإنشاء</th><th>عدد الفواتير</th><th>إجمالي القيمة</th></tr></thead>";
                                echo "<tbody>";
                                foreach ($creationDates as $date) {
                                    $rowClass = ($date['creation_date'] != '2025-05-31') ? 'highlight' : '';
                                    echo "<tr class='$rowClass'>";
                                    echo "<td>{$date['creation_date']}</td>";
                                    echo "<td>{$date['count']}</td>";
                                    echo "<td>" . number_format($date['total'], 2) . " جنيه</td>";
                                    echo "</tr>";
                                }
                                echo "</tbody></table>";
                                
                                // تحقق من وجود فواتير تم إنشاؤها في تاريخ مختلف
                                $hasOtherDates = false;
                                foreach ($creationDates as $date) {
                                    if ($date['creation_date'] != '2025-05-31') {
                                        $hasOtherDates = true;
                                        break;
                                    }
                                }
                                
                                if ($hasOtherDates) {
                                    echo "<div class='test-result test-warning'>";
                                    echo "<strong>تنبيه:</strong> توجد فواتير مرتبطة بيوم العمل 31/5 ولكن تم إنشاؤها في تواريخ أخرى. ";
                                    echo "هذا هو السبب في المشكلة الأصلية - هذه الفواتير كانت تُحسب في التقرير الخطأ.";
                                    echo "</div>";
                                }
                            }
                            
                        } else {
                            echo "<div class='test-result test-warning'>";
                            echo "لم يتم العثور على يوم العمل لتاريخ 31/5/2025";
                            echo "</div>";
                        }
                    } catch (Exception $e) {
                        echo "<div class='test-result test-error'>";
                        echo "<strong>خطأ:</strong> " . $e->getMessage();
                        echo "</div>";
                    }
                    ?>
                </div>

                <!-- اختبار 4: مقارنة النتائج قبل وبعد الإصلاح -->
                <div class="test-card">
                    <h3><i class="fas fa-balance-scale"></i> اختبار 4: مقارنة النتائج</h3>
                    
                    <?php
                    try {
                        // الطريقة القديمة (باستخدام created_at فقط)
                        $db->prepare("SELECT SUM(final_amount) as total FROM invoices 
                                     WHERE DATE(created_at) BETWEEN '2025-06-01' AND '2025-06-30' 
                                     AND payment_status = 'paid' AND branch_id = :branch_id");
                        $db->bind(':branch_id', $_SESSION['user_branch_id']);
                        $oldMethod = $db->fetch();
                        
                        echo "<div class='test-result test-info'>";
                        echo "<strong>الطريقة القديمة (created_at):</strong> " . number_format($oldMethod['total'], 2) . " جنيه";
                        echo "</div>";
                        
                        // الطريقة الجديدة (باستخدام end_day)
                        $newMethodTotal = $invoiceModel->getTotalSales([
                            'start_date' => '2025-06-01',
                            'end_date' => '2025-06-30',
                            'branch_id' => $_SESSION['user_branch_id']
                        ]);
                        
                        echo "<div class='test-result test-info'>";
                        echo "<strong>الطريقة الجديدة (end_day):</strong> " . number_format($newMethodTotal, 2) . " جنيه";
                        echo "</div>";
                        
                        $difference = $newMethodTotal - $oldMethod['total'];
                        
                        if ($difference != 0) {
                            echo "<div class='test-result test-success'>";
                            echo "<strong>الفرق:</strong> " . number_format($difference, 2) . " جنيه<br>";
                            echo "<strong>✅ تم إصلاح المشكلة!</strong> الآن التقارير تعتمد على تاريخ إقفال اليوم وليس تاريخ إنشاء الفاتورة.";
                            echo "</div>";
                        } else {
                            echo "<div class='test-result test-warning'>";
                            echo "<strong>الفرق:</strong> " . number_format($difference, 2) . " جنيه<br>";
                            echo "لا يوجد فرق في هذه الحالة، قد تحتاج لاختبار فترة أخرى أو قد لا توجد فواتير متأثرة.";
                            echo "</div>";
                        }
                        
                    } catch (Exception $e) {
                        echo "<div class='test-result test-error'>";
                        echo "<strong>خطأ:</strong> " . $e->getMessage();
                        echo "</div>";
                    }
                    ?>
                </div>

                <!-- ملاحظات مهمة -->
                <div class="test-card">
                    <h3><i class="fas fa-info-circle"></i> ملاحظات مهمة</h3>
                    <div class="test-result test-info">
                        <ul class="mb-0">
                            <li><strong>الإصلاح المطبق:</strong> الآن النظام يستخدم تاريخ إقفال اليوم (end_day.date) بدلاً من تاريخ إنشاء الفاتورة (created_at)</li>
                            <li><strong>النتيجة:</strong> الفواتير التي تم إنشاؤها في 1/6 ولكن تنتمي ليوم العمل 31/5 ستظهر في تقرير مايو وليس يونيو</li>
                            <li><strong>الاستثناء:</strong> للفواتير غير المرتبطة بيوم عمل، سيتم استخدام تاريخ الإنشاء كما هو</li>
                            <li><strong>الملفات المعدلة:</strong> includes/classes/Invoice.php و api/reports.php</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery via CDN -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Bootstrap Bundle JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
