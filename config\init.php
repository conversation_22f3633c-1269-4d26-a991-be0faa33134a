<?php
/**
 * ملف التهيئة الرئيسي للنظام
 * يتم استدعاؤه في بداية كل صفحة
 */

// إظهار جميع الأخطاء للتصحيح
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// تعريف المسار الأساسي للنظام
if (!defined('BASEPATH')) {
    define('BASEPATH', true);
}

// تعيين المنطقة الزمنية لمصر
date_default_timezone_set('Africa/Cairo');

// بدء الجلسة
session_start();

// استدعاء ملفات التكوين
require_once 'config.php';
require_once 'logger.php';

// استدعاء الفئة الرئيسية لقاعدة البيانات
require_once __DIR__ . '/../includes/classes/Database.php';

// استدعاء الفئات الأساسية
require_once __DIR__ . '/../includes/classes/User.php';
require_once __DIR__ . '/../includes/classes/Customer.php';
require_once __DIR__ . '/../includes/classes/Employee.php';
require_once __DIR__ . '/../includes/classes/appointments.php';
require_once __DIR__ . '/../includes/classes/Service.php';
require_once __DIR__ . '/../includes/classes/Product.php';
require_once __DIR__ . '/../includes/classes/Promotion.php';
require_once __DIR__ . '/../includes/classes/Notification.php';
require_once __DIR__ . '/../includes/classes/PromoCode.php';
require_once __DIR__ . '/../includes/classes/Inventory.php';
require_once __DIR__ . '/../includes/classes/Settings.php';
require_once __DIR__ . '/../includes/classes/EndDay.php';
require_once __DIR__ . '/../includes/classes/DatabaseBackup.php';
require_once __DIR__ . '/../includes/classes/WhatsAppAutomation.php';
require_once __DIR__ . '/../includes/classes/WatiService.php';
require_once __DIR__ . '/../includes/classes/WhatsAppService.php';
require_once __DIR__ . '/../includes/classes/InvoiceNotification.php';
require_once __DIR__ . '/../includes/classes/AdminNotification.php';
require_once __DIR__ . '/../includes/classes/AdminInvoiceNotification.php';
require_once __DIR__ . '/../includes/classes/Invoice.php';
require_once __DIR__ . '/../includes/classes/Expense.php';
require_once __DIR__ . '/../includes/classes/Branch.php';
require_once __DIR__ . '/../includes/classes/Report.php';

// استدعاء الدوال المساعدة
require_once __DIR__ . '/../includes/functions/helpers.php';
require_once __DIR__ . '/../includes/functions/auth.php';
require_once __DIR__ . '/../includes/functions/validation.php';
require_once __DIR__ . '/../includes/functions/print.php';

// استدعاء مساعد التسجيل
require_once __DIR__ . '/../includes/helpers/logger.php';

// استدعاء مكونات واجهة المستخدم الموحدة
require_once __DIR__ . '/../includes/ui_components.php';

// إنشاء اتصال بقاعدة البيانات
$db = new Database();

// التحقق من حالة تسجيل الدخول
$currentUser = null;
if (isset($_SESSION['user_id'])) {
    $userModel = new User($db);
    $currentUser = $userModel->getUserById($_SESSION['user_id']);

    // تحديث وقت آخر نشاط للمستخدم
    $_SESSION['last_activity'] = time();
}

// التحقق من انتهاء الجلسة بعد فترة من عدم النشاط (30 دقيقة)
if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > 1800)) {
    // تدمير الجلسة وإعادة التوجيه لصفحة تسجيل الدخول
    session_unset();
    session_destroy();
    if (!isAjaxRequest()) {
        header('Location: ' . BASE_URL . 'pages/auth/login.php');
        exit;
    }
}

/**
 * دالة للتحقق مما إذا كان الطلب هو طلب AJAX
 * @return bool
 */
function isAjaxRequest() {
    return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
        strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
}

/**
 * دالة للتحقق من وجود جلسة مستخدم نشطة
 * @return bool
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

/**
 * دالة للتحقق من صلاحيات المستخدم
 * @param array $allowedRoles مصفوفة بالأدوار المسموح لها
 * @return bool
 */
function checkRole($allowedRoles = []) {
    if (!isLoggedIn()) {
        return false;
    }

    if (empty($allowedRoles)) {
        return true;
    }

    if (in_array($_SESSION['user_role'], $allowedRoles)) {
        return true;
    }

    return false;
}

/**
 * دالة للتحقق من صلاحية محددة
 * @param string $permission الصلاحية المطلوبة
 * @return bool
 */
function hasPermission($permission) {
    if (!isLoggedIn()) {
        return false;
    }

    // المدير يملك جميع الصلاحيات
    if ($_SESSION['user_role'] === ROLE_ADMIN) {
        return true;
    }

    // التحقق من وجود الصلاحية
    if (isset($_SESSION['user_permissions']) && in_array($permission, $_SESSION['user_permissions'])) {
        return true;
    }

    return false;
}

/**
 * دالة لإعادة توجيه المستخدم إذا لم يكن مسجل الدخول
 * @param bool $redirectToLogin هل يتم إعادة التوجيه لصفحة تسجيل الدخول؟
 */
function redirectIfNotLoggedIn($redirectToLogin = true) {
    if (!isLoggedIn()) {
        if ($redirectToLogin) {
            header('Location: ' . BASE_URL . 'pages/auth/login.php');
        } else {
            header('Location: ' . BASE_URL);
        }
        exit;
    }
}

/**
 * دالة لإعادة توجيه المستخدم إذا لم يملك الصلاحية المطلوبة
 * @param array|string $roles الدور أو الأدوار المسموح لها
 */
function redirectIfNotAllowed($roles) {
    if (is_string($roles)) {
        $roles = [$roles];
    }

    if (!checkRole($roles)) {
        if (isAjaxRequest()) {
            header('Content-Type: application/json');
            echo json_encode(['status' => 'error', 'message' => ACCESS_DENIED_MSG]);
        } else {
            $_SESSION['error_message'] = ACCESS_DENIED_MSG;
            header('Location: ' . BASE_URL . 'pages/dashboard.php');
        }
        exit;
    }
}
