-- إضافة عمود end_day_id لجدول employee_salaries لربط الرواتب بأيام العمل

-- إضافة العمود الجديد
ALTER TABLE `employee_salaries` 
ADD COLUMN `end_day_id` INT(11) NULL AFTER `payment_date`,
ADD INDEX `idx_end_day_id` (`end_day_id`);

-- إضافة المفتاح الخارجي
ALTER TABLE `employee_salaries` 
ADD CONSTRAINT `fk_employee_salaries_end_day` 
FOREIGN KEY (`end_day_id`) REFERENCES `end_days` (`id`) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- تحديث السجلات الموجودة لربطها بأيام العمل المناسبة
-- هذا الاستعلام سيربط الرواتب المدفوعة بأيام العمل بناءً على تاريخ الدفع
UPDATE `employee_salaries` es
LEFT JOIN `end_days` ed ON DATE(es.payment_date) = ed.date
SET es.end_day_id = ed.id
WHERE es.payment_status = 'paid' 
AND es.payment_date IS NOT NULL 
AND ed.id IS NOT NULL;

-- عرض النتائج للتحقق
SELECT 
    es.id,
    es.employee_id,
    es.month,
    es.year,
    es.payment_date,
    es.end_day_id,
    ed.date as end_day_date,
    es.total_amount
FROM employee_salaries es
LEFT JOIN end_days ed ON es.end_day_id = ed.id
WHERE es.payment_status = 'paid'
ORDER BY es.payment_date DESC
LIMIT 10;
